import type { Config } from '@jest/types'

// Sync object
const config: Config.InitialOptions = {
  testEnvironment: 'node',
  rootDir: './src',
  testMatch: ['**/__tests__/**/*.spec.ts'],
  collectCoverage: true,
  collectCoverageFrom: [
    '<rootDir>/**/*.ts',
    '!<rootDir>/testUtils/**',
    '!<rootDir>/shared/database/seed/**',
    '!<rootDir>/scripts',
  ],
  transform: {
    '^.+\\.ts$': '@swc/jest',
  },
  verbose: true,
  clearMocks: true,

  workerIdleMemoryLimit: '512MB',
}

export default config
