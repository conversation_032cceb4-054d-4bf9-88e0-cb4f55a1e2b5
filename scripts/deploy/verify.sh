#!/bin/bash

get_container_name() {
  case "$1" in
  "production") echo "ciimar-api-prod" ;;
  "development") echo "ciimar-api-dev" ;;
  *)
    echo "Unknown deployment group: $1" >&2
    return 1
    ;;
  esac
}

CONTAINER_NAME=$(get_container_name "$DEPLOYMENT_GROUP_NAME")

if [[ -z "$DEPLOYMENT_GROUP_NAME" ]]; then
  echo "❌ DEPLOYMENT_GROUP_NAME is not set." >&2
  exit 1
fi

if [[ -z "$CONTAINER_NAME" ]]; then
  echo "❌ Container name could not be determined for deployment group: $DEPLOYMENT_GROUP_NAME" >&2
  exit 1
fi

RETRIES=10
START_PERIOD=20
RETRY_INTERVAL=10

# Aguardar o container iniciar
echo "Awaiting container to start..."
sleep $START_PERIOD

for i in $(seq 1 $RETRIES); do
  # Get docker healthcheck status
  STATUS=$(docker inspect --format='{{.State.Health.Status}}' "$CONTAINER_NAME")

  if [ "$STATUS" == "healthy" ]; then
    echo "Container started successfully."
    exit 0
  fi

  sleep $RETRY_INTERVAL
done

echo "The container did not start in time."
exit 1
