# CIIMAR Biobank Management (backend)

## Environments

| Environment | URL                                       |
| ----------- | ----------------------------------------- |
| Development | <https://ciimar-api-dev.yacoobalabs.com/> |
| Production  | <https://ciimar-api.yacoobalabs.com/>     |

## Migrations

Database commands are prefixed with `db:`. The following commands allow you to perform the following actions:

- `pnpm db:migrate:new --name=<some_appropriate_name_with_underscores>`: generate a new migration in the `/shared/database/drizzle` folder;
- `pnpm db:migrate:run`: run all pending migrations;
- `pnpm db:push`: push all changes to the database without generating migrations. Useful when working on a PR;
- `pnpm db:studio`: launches a lightweight, browser-based database explorer.

## Seeding

Database seeding commands are prefixed with `db:seed:`. The following commands allow you to perform the following actions:

- `pnpm db:seed`: seeds the database with default data;
- `pnpm db:seed:reset`: resets the database and then seeds it with default data.

## Specification

This is the base template that should be used for declaring the spec for entities' routes. A new module will usually have the following file structure:

```sh
.
└── modules/
    └── {entity}/
        ├── models.tsp
        └── main.tsp
```

You should replace `[e|E]ntity` with the corresponding entity name (e.g. `[u|U]sers`).

```typespec
import "./common/models/rest.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace Users {
  interface Routes {
    @post operationNameThatMatchesUseCase(
      @body body: string,
    ): Rest.ApiResponse.Success<YourModel> | Rest.ApiResponse.Error;
  }
}
```

You must then expand the `Specification` namespace:

```typespec
namespace Specification {
  // ...

  @route("/entity")
  @tag("Entity")
  interface _Entity extends Entity.Routes {}
}
```

## Docker

### Build command

```bash
pnpm build:image
```

This command builds a Docker image for the project, using the version specified in `package.json` or defaulting to `latest`.

#### Output

- Docker Image: `ciimar-biobanks-backend:<version>`
- Additional Tag: `ciimar-biobanks-backend:latest`

## Static Content

The application serves static content via dedicated public endpoints, configured per environment. All static files should be placed in the `public` folder, which is exposed externally through the reverse proxy.

| Environment | Public Static Endpoint                            | S3 Bucket   | Folder |
| ----------- | ------------------------------------------------- | ----------- | ------ |
| Development | <https://ciimar-api-dev.yacoobalabs.com/content/> | ciimar-dev  | public |
| Production  | <https://ciimar-api.yacoobalabs.com/content/>     | ciimar-prod | public |

> [!WARNING]
> Do **not** create a `/content` route manually in the application.
> This path is automatically managed and exposed by the **reverse proxy**.
> Any manual configuration may lead to conflicts or unexpected behavior.
