FROM public.ecr.aws/docker/library/node:22.14-alpine AS base
RUN apk upgrade --no-cache && apk add --no-cache curl

FROM base AS build-base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

FROM build-base AS build
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile --prefer-offline
COPY . .
RUN pnpm build

FROM build-base AS dependencies
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --prod --frozen-lockfile --ignore-scripts --prefer-offline

FROM base AS runner

# Metadata

LABEL description="Backend of the plataform to manage CIIMAR Biobanks"
LABEL maintainer="Yacooba Labs <<EMAIL>>"
LABEL org.opencontainers.image.title="ciimar-biobanks-management-backend"
LABEL org.opencontainers.image.description="Backend of the plataform to manage CIIMAR Biobanks"
LABEL org.opencontainers.image.vendor="Yacooba Labs"

# Environment configuration

RUN adduser -S yacooba -G node && \
    mkdir -p /app && chown -R node:node /app

USER yacooba:node

# Copy files

WORKDIR /app
COPY --from=build --chown=yacooba:node /app/dist ./dist
COPY --from=dependencies --chown=yacooba:node /app/node_modules ./node_modules
COPY --chown=yacooba:node scripts/healthcheck.sh ./scripts/healthcheck.sh
RUN chmod +x ./scripts/healthcheck.sh

# Healthcheck
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD ["./scripts/healthcheck.sh"]

# Run application

CMD ["node", "dist/src/index.js"]