import "@typespec/http";
import "@typespec/versioning";
import "./modules/users";
import "./modules/invites";
import "./modules/collections";
import "./modules/services";
import "./modules/fields";
import "./modules/fieldGroups";
import "./modules/entries";
import "./modules/changes";
import "./modules/notifications";
import "./modules/health";

using TypeSpec.Http;

@service(#{ title: "CIIMAR Biobanks REST API" })
@Versioning.versioned(Specification.Versions)
@Http.server(
  "https://ciimar-api.yacoobalabs.com/{version}/",
  "Production",
  {
    version: Specification.Versions = Specification.Versions.v1,
  }
)
@Http.server(
  "https://ciimar-api-dev.yacoobalabs.com/{version}/",
  "Development",
  {
    version: Specification.Versions = Specification.Versions.v1,
  }
)
@Http.server(
  "http://localhost:{port}/{version}/",
  "Local",
  {
    port: string = "3000",
    version: Specification.Versions = Specification.Versions.v1,
  }
)
@Http.server(
  "{schema}://{host}/{version}/",
  "Custom",
  {
    schema: "http" | "https" = "https",
    host: string,
    version: Specification.Versions = Specification.Versions.v1,
  }
)
namespace Specification {
  enum Versions {
    v1,
  }
  @route("/users")
  @tag("Users")
  interface _User extends Users.Routes {}

  @useAuth(BearerAuth)
  @route("/invites")
  @tag("Invites")
  interface _Invites extends Invites.Routes {}

  @useAuth(BearerAuth)
  @route("/collections")
  @tag("Collections")
  interface _Collections extends Collections.Routes {}

  @useAuth(BearerAuth)
  @route("/services")
  @tag("Services")
  interface _Services extends Services.Routes {}

  @useAuth(BearerAuth)
  @route("/fields")
  @tag("Fields")
  interface _Fields extends Fields.Routes {}

  @useAuth(BearerAuth)
  @route("/field-groups")
  @tag("Field Groups")
  interface _FieldGroups extends FieldGroups.Routes {}

  @useAuth(BearerAuth)
  @route("/entries")
  @tag("Entries")
  interface _Entries extends Entries.Routes {}

  @useAuth(BearerAuth)
  @route("/changes")
  @tag("Changes")
  interface _Changes extends Changes.Routes {}

  @useAuth(BearerAuth)
  @route("/notifications")
  @tag("Notifications")
  interface _Notifications extends Notifications.Routes {}

  @route("/health")
  @tag("Health")
  interface _Health extends Health.Routes {}
}
