{"openapi": "3.1.0", "info": {"title": "CIIMAR Biobanks REST API", "version": "v1"}, "tags": [{"name": "Users"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Collections"}, {"name": "Services"}, {"name": "Fields"}, {"name": "Field Groups"}, {"name": "Entries"}, {"name": "Changes"}, {"name": "Notifications"}, {"name": "Health"}], "paths": {"/changes/{changeId}": {"get": {"operationId": "_Changes_getChange", "description": "Retrieves a change by its unique identifier", "parameters": [{"name": "changeId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Change"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Changes"], "security": [{"BearerAuth": []}]}, "delete": {"operationId": "_Changes_deleteChange", "description": "Deletes a specific Change by its unique identifier", "parameters": [{"name": "changeId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Changes"], "security": [{"BearerAuth": []}]}}, "/changes/{changeId}/approve": {"patch": {"operationId": "_Changes_approve<PERSON><PERSON>e", "description": "Approves a specific Change by its unique identifier", "parameters": [{"name": "changeId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Change"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Changes"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.ReviewRequestBody"}}}}, "security": [{"BearerAuth": []}]}}, "/changes/{changeId}/reject": {"patch": {"operationId": "_Changes_rejectChange", "description": "Rejects a specific Change by its unique identifier", "parameters": [{"name": "changeId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Change"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Changes"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.ReviewRequestBody"}}}}, "security": [{"BearerAuth": []}]}}, "/collections": {"get": {"operationId": "_Collections_getCollections", "description": "Retrieves all Collections associated with the authenticated user", "parameters": [], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Collection"}}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}}, "/collections/stats": {"get": {"operationId": "_Collections_getCollectionsStats", "description": "Retrieves statistics for current user collections", "parameters": [], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Statistic"}}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}": {"get": {"operationId": "_Collections_getCollection", "description": "Fetches the details of a specific Collection by its unique identifier", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Collection"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}, "patch": {"operationId": "_Collections_updateCollection", "description": "Updates a Collection identified by its unique identifier", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Collection"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.CollectionUpdate"}}}}, "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/activity": {"get": {"operationId": "_Collections_getCollectionActivity", "description": "Retrieves all Activity associated with a specific Collection", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}, {"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Activity"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/entries": {"get": {"operationId": "_Collections_getCollectionEntries", "description": "Retrieves all Entries associated with a specific Collection", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}, {"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Entry"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}, "post": {"operationId": "_Collections_createCollectionEntry", "description": "Creates a new Entry in a specific Collection", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Entry"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.CreateEntry"}}}}, "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/field-groups": {"get": {"operationId": "_Collections_getCollectionFieldGroups", "description": "Retrieves all Field Groups associated with a specific Collection", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.FieldGroup"}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}, "post": {"operationId": "_Collections_createCollectionFieldGroup", "description": "Creates a Field Group associated with a specific Collection", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.FieldGroup"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.CreateFieldGroup"}}}}, "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/invites": {"get": {"operationId": "_Collections_getCollectionInvites", "description": "Retrieves all Invites associated with a specific Collection", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}, {"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.CollectionInvite"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/services": {"get": {"operationId": "_Collections_getCollectionServices", "description": "Retrieves all Services associated with a specific Collection", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}, {"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Service"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}, "post": {"operationId": "_Collections_createCollectionService", "description": "Creates a Service associated with a specific Collection", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Service"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.CreateService"}}}}, "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/users": {"get": {"operationId": "_Collections_getCollectionUsers", "description": "Retrieves all Users associated with a specific Collection", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}, {"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.User"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}}, "/collections/{collectionId}/users/{userId}": {"patch": {"operationId": "_Collections_updateCollectionUserAssignment", "description": "Updates a user's role within a collection", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}, {"name": "userId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.User"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.UpdateUserRole"}}}}, "security": [{"BearerAuth": []}]}, "delete": {"operationId": "_Collections_deleteCollectionUserAssignment", "description": "Removes a user from a collection", "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}, {"name": "userId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Collections"], "security": [{"BearerAuth": []}]}}, "/entries/{entryId}": {"get": {"operationId": "_Entries_getEntry", "description": "Retrive a specific Entry by its unique identifier", "parameters": [{"name": "entryId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Entry"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Entries"], "security": [{"BearerAuth": []}]}, "patch": {"operationId": "_Entries_updateEntry", "description": "Updates an existing Entry by its unique identifier", "parameters": [{"name": "entryId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Entry"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Entries"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.EntryUpdate"}}}}, "security": [{"BearerAuth": []}]}, "delete": {"operationId": "_Entries_deleteCollectionEntry", "description": "Deletes a specific Entry by its unique identifier", "parameters": [{"name": "entryId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Entries"], "security": [{"BearerAuth": []}]}}, "/entries/{entryId}/changes": {"post": {"operationId": "_Entries_createChange", "description": "Creates a new Change for the specified Entry", "parameters": [{"name": "entryId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Change"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Entries"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.CreateChange"}}}}, "security": [{"BearerAuth": []}]}, "get": {"operationId": "_Entries_getChanges", "description": "Get all Changes of the specified Entry", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}, {"name": "entryId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Change"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Entries"], "security": [{"BearerAuth": []}]}}, "/field-groups/{fieldGroupId}": {"get": {"operationId": "_FieldGroups_getFieldGroup", "description": "Fetches the details of a specific Field Group by its unique identifier", "parameters": [{"name": "fieldGroupId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.FieldGroup"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Field Groups"], "security": [{"BearerAuth": []}]}, "patch": {"operationId": "_FieldGroups_updateFieldGroup", "description": "Updates an existing Field Group by its unique identifier.", "parameters": [{"name": "fieldGroupId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.FieldGroup"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Field Groups"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.FieldGroupUpdate"}}}}, "security": [{"BearerAuth": []}]}, "delete": {"operationId": "_FieldGroups_deleteFieldGroup", "description": "Deletes a specific Field Group by its unique identifier.", "parameters": [{"name": "fieldGroupId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Field Groups"], "security": [{"BearerAuth": []}]}}, "/field-groups/{fieldGroupId}/fields": {"post": {"operationId": "_FieldGroups_createField", "description": "Creates a Field associated with a specific Field Group.", "parameters": [{"name": "fieldGroupId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.SimpleValueField"}, {"$ref": "#/components/schemas/Models.SelectField"}]}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Field Groups"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/CreateSimpleValueField"}, {"$ref": "#/components/schemas/CreateSelectField"}]}}}}, "security": [{"BearerAuth": []}]}}, "/fields/{fieldId}": {"get": {"operationId": "_<PERSON>_getField", "description": "Retrieves the details of a specific Field by its unique identifier", "parameters": [{"name": "fieldId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.SimpleValueField"}, {"$ref": "#/components/schemas/Models.SelectField"}]}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Fields"], "security": [{"BearerAuth": []}]}, "patch": {"operationId": "_Fields_updateField", "description": "Updates an existing Field by its unique identifier", "parameters": [{"name": "fieldId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.SimpleValueField"}, {"$ref": "#/components/schemas/Models.SelectField"}]}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Fields"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.SimpleValueFieldUpdate"}, {"$ref": "#/components/schemas/Models.SelectFieldUpdate"}]}}}}, "security": [{"BearerAuth": []}]}, "delete": {"operationId": "_Fields_deleteField", "description": "Deletes a specific Field by its unique identifier", "parameters": [{"name": "fieldId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Fields"], "security": [{"BearerAuth": []}]}}, "/health": {"get": {"operationId": "_Health_healthCheck", "description": "Health check endpoint", "parameters": [], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Health"]}}, "/invites": {"get": {"operationId": "_Invites_getInvites", "description": "Retrieves the list of invites (platform wide)", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Models.PlatformInvite"}, {"$ref": "#/components/schemas/Models.CollectionInvite"}]}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["<PERSON><PERSON><PERSON>"], "security": [{"BearerAuth": []}]}, "post": {"operationId": "_Invites_invite", "description": "Sends an invite to a email address", "parameters": [], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.PlatformInvite"}, {"$ref": "#/components/schemas/Models.CollectionInvite"}]}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/CreatePlatformInvite"}, {"$ref": "#/components/schemas/CreateCollectionInvite"}]}}}}, "security": [{"BearerAuth": []}]}}, "/invites/{inviteId}": {"get": {"operationId": "_Invites_getInvite", "description": "Retrieves a specific invite", "parameters": [{"name": "inviteId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.PlatformInvite"}, {"$ref": "#/components/schemas/Models.CollectionInvite"}]}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["<PERSON><PERSON><PERSON>"], "security": [{"BearerAuth": []}]}}, "/invites/{inviteId}/cancel": {"patch": {"operationId": "_Invites_cancelInvite", "description": "Cancels an invite", "parameters": [{"name": "inviteId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Models.PlatformInvite"}, {"$ref": "#/components/schemas/Models.CollectionInvite"}]}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["<PERSON><PERSON><PERSON>"], "security": [{"BearerAuth": []}]}}, "/notifications": {"get": {"operationId": "_Notifications_getNotifications", "description": "Retrieves all Notifications associated with the authenticated user", "parameters": [], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Notification"}}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Notifications"], "security": [{"BearerAuth": []}]}}, "/notifications/all-read": {"patch": {"operationId": "_Notifications_markAllAsRead", "description": "Mark all authenticated user notifications as read", "parameters": [], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Notifications"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"notificationId": {"$ref": "#/components/schemas/Domain.UniqueID"}}}}}}, "security": [{"BearerAuth": []}]}}, "/notifications/{notificationId}/read": {"patch": {"operationId": "_Notifications_markAsRead", "description": "Mark a notification as read", "parameters": [{"name": "notificationId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Notification"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Notifications"], "security": [{"BearerAuth": []}]}}, "/services/{serviceId}": {"get": {"operationId": "_Services_getService", "description": "Fetches the details of a specific Service by its unique identifier", "parameters": [{"name": "serviceId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Service"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Services"], "security": [{"BearerAuth": []}]}, "patch": {"operationId": "_Services_updateService", "description": "Updates an existing Service identified by its unique identifier", "parameters": [{"name": "serviceId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Service"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Services"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.ServiceUpdate"}}}}, "security": [{"BearerAuth": []}]}, "delete": {"operationId": "_Services_deleteService", "description": "Deletes a specific Service by its unique identifier", "parameters": [{"name": "serviceId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Domain.UniqueID"}}], "responses": {"204": {"description": "There is no content to send for this request, but the headers may be useful. "}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Services"], "security": [{"BearerAuth": []}]}}, "/users": {"get": {"operationId": "_User_getUsers", "description": "Retrieves the list of users", "parameters": [{"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.offset"}, {"$ref": "#/components/parameters/Rest.ApiRequest.PaginatedRequest.limit"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderBy"}, {"$ref": "#/components/parameters/Rest.ApiRequest.SortedRequest.orderDirection"}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"type": "object", "required": ["items", "totalItems"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Models.User"}, "description": "The data returned in the successful response"}, "totalItems": {"type": "integer", "format": "uint32", "description": "Indicates the total number of items", "examples": [596]}}}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Users"], "security": [{"BearerAuth": []}]}}, "/users/login": {"post": {"operationId": "_User_login", "description": "Temporary Endpoint for login", "parameters": [], "responses": {"200": {"description": "The request has succeeded.", "content": {"text/plain": {"schema": {"type": "string"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Users"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Login"}}}}}}, "/users/me": {"get": {"operationId": "_User_getCurrentUser", "description": "Retrieves the details of the authenticated user", "parameters": [], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.User"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Users"], "security": [{"BearerAuth": []}]}}, "/users/{inviteId}": {"post": {"operationId": "_User_createUser", "description": "Creates a new user", "parameters": [{"name": "inviteId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"201": {"description": "The request has succeeded and a new resource has been created as a result.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.User"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Users"], "security": [{"BearerAuth": []}]}}, "/users/{userId}": {"get": {"operationId": "_User_getUser", "description": "Retrieves the details of a specific user by its unique identifier", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.User"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Users"], "security": [{"BearerAuth": []}]}, "patch": {"operationId": "_User_updateUser", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The request has succeeded.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.User"}}}}, "400": {"description": "This error is returned when the request is invalid or cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.BadRequest"}}}}, "401": {"description": "This error is returned when the request is unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Unauthorized"}}}}, "403": {"description": "This error is returned when the request is forbidden or the user does not have permission to access the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.Forbidden"}}}}, "404": {"description": "This error is returned when the requested resource does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.NotFound"}}}}, "500": {"description": "This error is returned when an internal server error occurs and the request cannot be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Rest.ApiErrors.InternalServerError"}}}}}, "tags": ["Users"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.UserUpdate"}}}}, "security": [{"BearerAuth": []}]}}}, "components": {"parameters": {"Rest.ApiRequest.PaginatedRequest.limit": {"name": "limit", "in": "query", "required": false, "description": "The maximum number of items to return in the response. Used for pagination.", "schema": {"type": "integer", "format": "uint32"}, "explode": false}, "Rest.ApiRequest.PaginatedRequest.offset": {"name": "offset", "in": "query", "required": false, "description": "The starting position of the results to return. Used for pagination.", "schema": {"type": "integer", "format": "uint32"}, "explode": false}, "Rest.ApiRequest.SortedRequest.orderBy": {"name": "orderBy", "in": "query", "required": false, "description": "The field by which the results should be sorted", "schema": {"type": "string"}, "explode": false}, "Rest.ApiRequest.SortedRequest.orderDirection": {"name": "orderDirection", "in": "query", "required": false, "description": "The sort order: 'ASC' for ascending or 'DESC' for descending", "schema": {"type": "string", "enum": ["ASC", "DESC"]}, "explode": false}}, "schemas": {"CreateCollectionInvite": {"type": "object", "required": ["name", "email", "collectionId", "role"], "properties": {"name": {"type": "string", "description": "Represents the user full name", "examples": ["<PERSON>", "<PERSON>"]}, "email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>", "<EMAIL>"]}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Represents the collection the user is invited to"}, "role": {"type": "string", "enum": ["COLLECTION_MANAGER", "EDITOR", "CONTRIBUTOR", "VIEWER"], "description": "Represents the user role"}}, "description": ""}, "CreatePlatformInvite": {"type": "object", "required": ["name", "email", "role"], "properties": {"name": {"type": "string", "description": "Represents the user full name", "examples": ["<PERSON>", "<PERSON>"]}, "email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>", "<EMAIL>"]}, "role": {"type": "string", "enum": ["PLATFORM_MANAGER"]}}, "description": ""}, "CreateSelectField": {"type": "object", "required": ["name", "position", "mandatory", "visibility", "type", "preview", "options"], "properties": {"name": {"type": "string", "description": "Human-readable field name"}, "position": {"type": "number", "description": "Represents the position of the field in a Field Group"}, "maxLength": {"type": "number", "description": "Represents the maximum length of the field value"}, "mandatory": {"type": "boolean", "description": "Indicates if the field is mandatory"}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "Indicates if the field is visibile to the Federated Catalog"}, "type": {"type": "string", "enum": ["SELECT", "SELECT-MANY"], "description": "Indicates the type of the field"}, "preview": {"type": "boolean", "description": "Indicates if the field is shown in a compact table view"}, "options": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "A human-readable label"}, "value": {"type": "string", "description": "The value of the option"}}, "required": ["label", "value"]}}}, "description": ""}, "CreateSimpleValueField": {"type": "object", "required": ["name", "position", "mandatory", "visibility", "type", "preview"], "properties": {"name": {"type": "string", "description": "Human-readable field name"}, "position": {"type": "number", "description": "Represents the position of the field in a Field Group"}, "maxLength": {"type": "number", "description": "Represents the maximum length of the field value"}, "mandatory": {"type": "boolean", "description": "Indicates if the field is mandatory"}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "Indicates if the field is visibile to the Federated Catalog"}, "type": {"type": "string", "enum": ["TEXT", "TEXTAREA", "IMAGE-UPLOAD"], "description": "Indicates the type of the field"}, "preview": {"type": "boolean", "description": "Indicates if the field is shown in a compact table view"}}, "description": ""}, "Domain.UniqueID": {"type": "string", "format": "uuid"}, "Models.Activity": {"type": "object", "required": ["id", "collectionId", "userId", "type", "data", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Related Collection"}, "userId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Related User"}, "type": {"type": "string", "description": "Type of the Activity"}, "data": {"description": "Metadata of the Activity"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}, "description": "Represents a user Activity"}, "Models.Change": {"type": "object", "required": ["id", "entryId", "created<PERSON>y", "status", "data", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "entryId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The unique identifier of the Entry to which the Change belongs", "readOnly": true}, "createdBy": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The user who created the Change", "readOnly": true}, "reviewedBy": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The user who reviewed the Change", "readOnly": true}, "reviewerMessage": {"type": "string", "description": "The message provided by the reviewer when the Change is accepted or rejected", "readOnly": true}, "status": {"type": "string", "enum": ["PENDING", "ACCEPTED", "REJECTED"], "description": "The status of the Change", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Models.FieldData"}, "description": "The data of the Change"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}, "description": "Representes a Change in a Entry"}, "Models.Collection": {"type": "object", "allOf": [{"type": "object", "required": ["id", "name", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "name": {"type": "string", "description": "A human-readable name for the collection"}, "description": {"type": "string", "description": "The description of the collection"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}], "description": "Represents a Collection entity"}, "Models.CollectionInvite": {"type": "object", "allOf": [{"type": "object", "required": ["id", "name", "email", "status", "collectionId", "role", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "name": {"type": "string", "description": "Represents the user full name", "examples": ["<PERSON>"]}, "email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>"]}, "status": {"type": "string", "enum": ["PENDING", "EXPIRED", "REGISTERED", "CANCELED"], "description": "Represents the invite status", "examples": ["PENDING"], "readOnly": true}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Represents the collection the user is invited to"}, "role": {"type": "string", "enum": ["COLLECTION_MANAGER", "EDITOR", "CONTRIBUTOR", "VIEWER"], "description": "Represents the user role"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}]}, "Models.CollectionUpdate": {"type": "object", "allOf": [{"type": "object", "properties": {"name": {"type": "string", "description": "A human-readable name for the collection"}, "description": {"type": "string", "description": "The description of the collection"}}}], "description": "Represents a Collection entity"}, "Models.CreateChange": {"type": "object", "required": ["data"], "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Models.FieldData"}, "description": "The data of the Change"}}, "description": ""}, "Models.CreateEntry": {"type": "object", "required": ["visibility", "data"], "properties": {"visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "The visibility of the Entry"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Models.FieldData"}, "description": "The current data of the Entry"}}, "description": ""}, "Models.CreateFieldGroup": {"type": "object", "required": ["name", "position", "fieldIds"], "properties": {"name": {"type": "string", "description": "Human-readable name of the field group"}, "position": {"type": "number", "description": "The position of the field group"}, "fieldIds": {"type": "array", "items": {"$ref": "#/components/schemas/Domain.UniqueID"}, "description": "Unique identifier's of related fields"}}, "description": ""}, "Models.CreateService": {"type": "object", "required": ["name", "description", "category", "availability", "link"], "properties": {"name": {"type": "string", "description": "Name of the service"}, "description": {"type": "string", "description": "A brief description of the service"}, "category": {"type": "string", "description": "A category that the service belongs to"}, "availability": {"type": "string", "enum": ["AVAILABLE", "NOT_AVAILABLE"], "description": "The current availability status of the service"}, "link": {"type": "string", "format": "uri", "description": "A URL that points to the service"}}, "description": ""}, "Models.Entry": {"type": "object", "allOf": [{"type": "object", "required": ["id", "collectionId", "visibility", "data", "changes", "created<PERSON>y", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The unique identifier of the Collection to which the Entry belongs", "readOnly": true}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "The visibility of the Entry"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Models.FieldData"}, "description": "The current data of the Entry"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Change"}, "description": "The history of changes made to the Entry", "readOnly": true}, "createdBy": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The user who created the Entry", "readOnly": true}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}], "description": "Representes a Entry in a Collection (A row in a collection)"}, "Models.EntryUpdate": {"type": "object", "allOf": [{"type": "object", "properties": {"visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "The visibility of the Entry"}}}], "description": "Representes a Entry in a Collection (A row in a collection)"}, "Models.FieldData": {"type": "object", "required": ["fieldId", "value"], "properties": {"fieldId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Reference to thre related Field"}, "value": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "description": "The value of the Field"}}, "description": "Represents the data of a Field in an Entry"}, "Models.FieldGroup": {"type": "object", "allOf": [{"type": "object", "required": ["id", "collectionId", "name", "position", "fields", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The unique identifier of the Collection to which the field group belongs", "readOnly": true}, "name": {"type": "string", "description": "Human-readable name of the field group"}, "position": {"type": "number", "description": "The position of the field group"}, "fields": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Models.SimpleValueField"}, {"$ref": "#/components/schemas/Models.SelectField"}]}, "description": "The fields associated with the field group", "readOnly": true}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}], "description": "Represents a group of fields within a Collection entity"}, "Models.FieldGroupUpdate": {"type": "object", "allOf": [{"type": "object", "properties": {"name": {"type": "string", "description": "Human-readable name of the field group"}, "position": {"type": "number", "description": "The position of the field group"}, "fieldIds": {"type": "array", "items": {"$ref": "#/components/schemas/Domain.UniqueID"}, "description": "Unique identifier's of related fields"}}}], "description": "Represents a group of fields within a Collection entity"}, "Models.Login": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>"]}}}, "Models.Notification": {"type": "object", "allOf": [{"type": "object", "required": ["id", "userId", "activityId", "read", "sent", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "userId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Related user"}, "activityId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Related activity"}, "read": {"type": "boolean", "description": "Indicates true if the notification is read"}, "sent": {"type": "boolean", "description": "Indicates true if the notification is already sent"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}], "description": "Represents a Notification"}, "Models.PlatformInvite": {"type": "object", "allOf": [{"type": "object", "required": ["id", "name", "email", "status", "role", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "name": {"type": "string", "description": "Represents the user full name", "examples": ["<PERSON>"]}, "email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>"]}, "status": {"type": "string", "enum": ["PENDING", "EXPIRED", "REGISTERED", "CANCELED"], "description": "Represents the invite status", "examples": ["PENDING"], "readOnly": true}, "role": {"type": "string", "enum": ["PLATFORM_MANAGER"]}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}]}, "Models.ReviewRequestBody": {"type": "object", "required": ["reviewerMessage"], "properties": {"reviewerMessage": {"type": "string", "description": "The message provided by the reviewer"}}, "description": "Represents a request to review a Change"}, "Models.SelectField": {"type": "object", "required": ["id", "name", "position", "mandatory", "visibility", "fieldGroupId", "type", "preview", "options", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "name": {"type": "string", "description": "Human-readable field name"}, "position": {"type": "number", "description": "Represents the position of the field in a Field Group"}, "maxLength": {"type": "number", "description": "Represents the maximum length of the field value"}, "mandatory": {"type": "boolean", "description": "Indicates if the field is mandatory"}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "Indicates if the field is visibile to the Federated Catalog"}, "fieldGroupId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The unique identifier of the Field Group this field belongs to", "readOnly": true}, "type": {"type": "string", "enum": ["SELECT", "SELECT-MANY"], "description": "Indicates the type of the field"}, "preview": {"type": "boolean", "description": "Indicates if the field is shown in a compact table view"}, "options": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "A human-readable label"}, "value": {"type": "string", "description": "The value of the option"}}, "required": ["label", "value"]}}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}, "Models.SelectFieldUpdate": {"type": "object", "properties": {"name": {"type": "string", "description": "Human-readable field name"}, "position": {"type": "number", "description": "Represents the position of the field in a Field Group"}, "maxLength": {"type": "number", "description": "Represents the maximum length of the field value"}, "mandatory": {"type": "boolean", "description": "Indicates if the field is mandatory"}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "Indicates if the field is visibile to the Federated Catalog"}, "type": {"type": "string", "enum": ["SELECT", "SELECT-MANY"], "description": "Indicates the type of the field"}, "preview": {"type": "boolean", "description": "Indicates if the field is shown in a compact table view"}, "options": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "A human-readable label"}, "value": {"type": "string", "description": "The value of the option"}}, "required": ["label", "value"]}}}}, "Models.Service": {"type": "object", "required": ["id", "collectionId", "name", "description", "category", "availability", "link", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "A unique identifier for the collection that this service belongs to", "readOnly": true}, "name": {"type": "string", "description": "Name of the service"}, "description": {"type": "string", "description": "A brief description of the service"}, "category": {"type": "string", "description": "A category that the service belongs to"}, "availability": {"type": "string", "enum": ["AVAILABLE", "NOT_AVAILABLE"], "description": "The current availability status of the service"}, "link": {"type": "string", "format": "uri", "description": "A URL that points to the service"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}, "description": "Represents a Service provided by the Collection organization"}, "Models.ServiceUpdate": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the service"}, "description": {"type": "string", "description": "A brief description of the service"}, "category": {"type": "string", "description": "A category that the service belongs to"}, "availability": {"type": "string", "enum": ["AVAILABLE", "NOT_AVAILABLE"], "description": "The current availability status of the service"}, "link": {"type": "string", "format": "uri", "description": "A URL that points to the service"}}, "description": "Represents a Service provided by the Collection organization"}, "Models.SimpleValueField": {"type": "object", "required": ["id", "name", "position", "mandatory", "visibility", "fieldGroupId", "type", "preview", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "name": {"type": "string", "description": "Human-readable field name"}, "position": {"type": "number", "description": "Represents the position of the field in a Field Group"}, "maxLength": {"type": "number", "description": "Represents the maximum length of the field value"}, "mandatory": {"type": "boolean", "description": "Indicates if the field is mandatory"}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "Indicates if the field is visibile to the Federated Catalog"}, "fieldGroupId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The unique identifier of the Field Group this field belongs to", "readOnly": true}, "type": {"type": "string", "enum": ["TEXT", "TEXTAREA", "IMAGE-UPLOAD"], "description": "Indicates the type of the field"}, "preview": {"type": "boolean", "description": "Indicates if the field is shown in a compact table view"}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}, "Models.SimpleValueFieldUpdate": {"type": "object", "properties": {"name": {"type": "string", "description": "Human-readable field name"}, "position": {"type": "number", "description": "Represents the position of the field in a Field Group"}, "maxLength": {"type": "number", "description": "Represents the maximum length of the field value"}, "mandatory": {"type": "boolean", "description": "Indicates if the field is mandatory"}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"], "description": "Indicates if the field is visibile to the Federated Catalog"}, "type": {"type": "string", "enum": ["TEXT", "TEXTAREA", "IMAGE-UPLOAD"], "description": "Indicates the type of the field"}, "preview": {"type": "boolean", "description": "Indicates if the field is shown in a compact table view"}}}, "Models.Statistic": {"type": "object", "required": ["collectionId", "entries", "members"], "properties": {"collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "The unique identifier of the collection"}, "entries": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64", "description": "The number of entries in the collection"}, "public": {"type": "integer", "format": "int64", "description": "The number of entries in the collection that are publicly visible"}}, "required": ["total", "public"]}, "members": {"type": "integer", "format": "int64", "description": "The number of users who are members of the collection"}}}, "Models.UpdateUserRole": {"type": "object", "required": ["role"], "properties": {"role": {"type": "string", "enum": ["COLLECTION_MANAGER", "EDITOR", "CONTRIBUTOR", "VIEWER"]}}}, "Models.User": {"type": "object", "allOf": [{"type": "object", "required": ["id", "name", "email", "settings", "assignments", "createdAt"], "properties": {"id": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Universal unique identifier", "readOnly": true}, "name": {"type": "string", "description": "Represents the user full name", "examples": ["<PERSON>"]}, "email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>"]}, "settings": {"type": "object", "properties": {"emailNotificationsEnabled": {"type": "boolean", "description": "Represents the user email notifications preference", "examples": [true]}}, "required": ["emailNotificationsEnabled"], "description": "Represents the user profile preferences"}, "assignments": {"type": "array", "items": {"$ref": "#/components/schemas/Models.UserAssignment"}, "readOnly": true}, "createdAt": {"type": "string", "format": "date-time", "description": "Date and time of creation", "readOnly": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Date and time of the last update", "readOnly": true}}}], "description": "User model"}, "Models.UserAssignment": {"type": "object", "required": ["role", "collectionId"], "properties": {"role": {"type": "string", "enum": ["PLATFORM_MANAGER", "COLLECTION_MANAGER", "EDITOR", "CONTRIBUTOR", "VIEWER"], "description": "Represents the user role"}, "collectionId": {"allOf": [{"$ref": "#/components/schemas/Domain.UniqueID"}], "description": "Represents the collection the user is assigned to"}}}, "Models.UserUpdate": {"type": "object", "allOf": [{"type": "object", "properties": {"name": {"type": "string", "description": "Represents the user full name", "examples": ["<PERSON>"]}, "email": {"type": "string", "format": "email", "description": "Represents the user email address", "examples": ["<EMAIL>"]}, "settings": {"type": "object", "properties": {"emailNotificationsEnabled": {"type": "boolean", "description": "Represents the user email notifications preference", "examples": [true]}}, "description": "Represents the user profile preferences"}}}], "description": "User model"}, "Rest.ApiErrors.BadRequest": {"type": "object", "allOf": [{"type": "object", "required": ["type", "message"], "properties": {"type": {"type": "string", "enum": ["BadRequestError"], "description": "Indicates the type of the error"}, "message": {"type": "string", "description": "Represents an error message that provides details about the error that occurred"}}, "description": "Base error model.\n\nThis model represents a generic error that can be extended by other\nerror models to provide additional information."}], "description": "This error is returned when the request is invalid or cannot be processed"}, "Rest.ApiErrors.Forbidden": {"type": "object", "allOf": [{"type": "object", "required": ["type", "message"], "properties": {"type": {"type": "string", "enum": ["ForbiddenError"], "description": "Indicates the type of the error"}, "message": {"type": "string", "description": "Represents an error message that provides details about the error that occurred"}}, "description": "Base error model.\n\nThis model represents a generic error that can be extended by other\nerror models to provide additional information."}], "description": "This error is returned when the request is forbidden or the user does not have permission to access the resource"}, "Rest.ApiErrors.InternalServerError": {"type": "object", "allOf": [{"type": "object", "required": ["type", "message"], "properties": {"type": {"type": "string", "enum": ["InternalServerError"], "description": "Indicates the type of the error"}, "message": {"type": "string", "description": "Represents an error message that provides details about the error that occurred"}}, "description": "Base error model.\n\nThis model represents a generic error that can be extended by other\nerror models to provide additional information."}], "description": "This error is returned when an internal server error occurs and the request cannot be processed"}, "Rest.ApiErrors.NotFound": {"type": "object", "allOf": [{"type": "object", "required": ["type", "message"], "properties": {"type": {"type": "string", "enum": ["NotFoundError"], "description": "Indicates the type of the error"}, "message": {"type": "string", "description": "Represents an error message that provides details about the error that occurred"}}, "description": "Base error model.\n\nThis model represents a generic error that can be extended by other\nerror models to provide additional information."}], "description": "This error is returned when the requested resource does not exist"}, "Rest.ApiErrors.Unauthorized": {"type": "object", "allOf": [{"type": "object", "required": ["type", "message"], "properties": {"type": {"type": "string", "enum": ["UnauthorizedError"], "description": "Indicates the type of the error"}, "message": {"type": "string", "description": "Represents an error message that provides details about the error that occurred"}}, "description": "Base error model.\n\nThis model represents a generic error that can be extended by other\nerror models to provide additional information."}], "description": "This error is returned when the request is unauthorized"}, "Specification.Versions": {"type": "string", "enum": ["v1"]}, "Versions": {"type": "string", "enum": ["v1"]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "Bearer"}}}, "servers": [{"url": "{schema}://{host}/{version}/", "description": "Custom", "variables": {"schema": {"default": "https", "enum": ["http", "https"]}, "host": {"default": ""}, "version": {"default": "v1", "enum": ["v1"]}}}, {"url": "http://localhost:{port}/{version}/", "description": "Local", "variables": {"port": {"default": "3000"}, "version": {"default": "v1", "enum": ["v1"]}}}, {"url": "https://ciimar-api-dev.yacoobalabs.com/{version}/", "description": "Development", "variables": {"version": {"default": "v1", "enum": ["v1"]}}}, {"url": "https://ciimar-api.yacoobalabs.com/{version}/", "description": "Production", "variables": {"version": {"default": "v1", "enum": ["v1"]}}}]}