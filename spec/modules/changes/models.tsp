import "../../common/models/domain.tsp";
import "../entries/models.tsp";

namespace Models {
  /**
   * Indicates the state of a Change
   */
  alias ChangeStatus = "PENDING" | "ACCEPTED" | "REJECTED";

  model ChangeProps {
    @visibility(Lifecycle.Read)
    @doc("The unique identifier of the Entry to which the Change belongs")
    entryId: Domain.UniqueID;

    @visibility(Lifecycle.Read)
    @doc("The user who created the Change")
    createdBy: Domain.UniqueID;

    @visibility(Lifecycle.Read)
    @doc("The user who reviewed the Change")
    reviewedBy?: Domain.UniqueID;

    @visibility(Lifecycle.Read)
    @doc("The message provided by the reviewer when the Change is accepted or rejected")
    reviewerMessage?: string;

    @visibility(Lifecycle.Read)
    @doc("The status of the Change")
    status: ChangeStatus;

    @doc("The data of the Change")
    data: FieldData[];
  }
  model ReviewRequestProps {
    @doc("The message provided by the reviewer")
    reviewerMessage: string;
  }

  @doc("Representes a Change in a Entry")
  model Change is Domain.Entity<ChangeProps>;

  model CreateChange is Create<Domain.Entity<ChangeProps>>;

  @doc("Represents a request to review a Change")
  model ReviewRequestBody is Update<Domain.Entity<ReviewRequestProps>>;
}
