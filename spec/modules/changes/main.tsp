import "@typespec/http";
import "../../common/models/rest.tsp";
import "./models.tsp";

using TypeSpec.Http;

namespace Changes {
  interface Routes {
    @doc("Retrieves a change by its unique identifier")
    @get
    getChange(
      @path changeId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Change> | Rest.ApiErrors.CommonErrors;

    @doc("Approves a specific Change by its unique identifier")
    @route("/{changeId}/approve")
    @patch(#{ implicitOptionality: false })
    approveChange(
      @path changeId: Domain.UniqueID,
      @body _: Models.ReviewRequestBody,
    ): Rest.ApiResponse.Success<Models.Change> | Rest.ApiErrors.CommonErrors;

    @doc("Rejects a specific Change by its unique identifier")
    @route("/{changeId}/reject")
    @patch(#{ implicitOptionality: false })
    rejectChange(
      @path changeId: Domain.UniqueID,
      @body _: Models.ReviewRequestBody,
    ): Rest.ApiResponse.Success<Models.Change> | Rest.ApiErrors.CommonErrors;

    @doc("Deletes a specific Change by its unique identifier")
    @route("/{changeId}")
    @delete
    deleteChange(
      @path changeId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.CommonErrors;
  }
}
