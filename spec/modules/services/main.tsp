import "../../common/models/rest.tsp";
import "./models.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace Services {
  interface Routes {
    @doc("Fetches the details of a specific Service by its unique identifier")
    @get
    getService(
      @path serviceId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Service> | Rest.ApiErrors.CommonErrors;

    @doc("Updates an existing Service identified by its unique identifier")
    @patch(#{ implicitOptionality: true })
    updateService(
      @path serviceId: Domain.UniqueID,
      @body service: Models.Service,
    ): Rest.ApiResponse.Success<Models.Service> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Deletes a specific Service by its unique identifier")
    @delete
    deleteService(
      @path serviceId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.CommonErrors;
  }
}
