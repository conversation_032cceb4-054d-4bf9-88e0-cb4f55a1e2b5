import "../../common/models/domain.tsp";

namespace Models {
  alias ServiceAvailability = "AVAILABLE" | "NOT_AVAILABLE";

  model ServiceProps {
    @visibility(Lifecycle.Read)
    @doc("A unique identifier for the collection that this service belongs to")
    collectionId: Domain.UniqueID;

    @doc("Name of the service")
    name: string;

    @doc("A brief description of the service")
    description: string;

    @doc("A category that the service belongs to")
    category: string;

    @doc("The current availability status of the service")
    availability: ServiceAvailability;

    @doc("A URL that points to the service")
    link: url;
  }

  @doc("Represents a Service provided by the Collection organization")
  model Service is Domain.Entity<ServiceProps>;

  model CreateService is Create<Service>;
}
