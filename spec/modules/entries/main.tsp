import "@typespec/http";
import "../../common/models/rest.tsp";
import "./models.tsp";

using TypeSpec.Http;

namespace Entries {
  interface Routes {
    @doc("Retrive a specific Entry by its unique identifier")
    @get
    getEntry(
      @path entryId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Entry> | Rest.ApiErrors.CommonErrors;

    @doc("Updates an existing Entry by its unique identifier")
    @patch(#{ implicitOptionality: true })
    updateEntry(
      @path entryId: Domain.UniqueID,
      @body entry: Models.Entry,
    ): Rest.ApiResponse.Success<Models.Entry> | Rest.ApiErrors.CommonErrors;

    @doc("Deletes a specific Entry by its unique identifier")
    @delete
    deleteCollectionEntry(
      @path entryId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.CommonWithAuthErrors;

    // Changes

    @doc("Creates a new Change for the specified Entry")
    @route("/{entryId}/changes")
    @post
    createChange(
      @path entryId: Domain.UniqueID,
      @body change: Models.CreateChange,
    ): Rest.ApiResponse.Created<Models.Change> | Rest.ApiErrors.CommonErrors;

    @doc("Get all Changes of the specified Entry")
    @route("/{entryId}/changes")
    @get
    getChanges(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
      @path entryId: Domain.UniqueID,
    ): Rest.ApiResponse.PaginatedSuccess<Models.Change[]> | Rest.ApiErrors.CommonErrors;
  }
}
