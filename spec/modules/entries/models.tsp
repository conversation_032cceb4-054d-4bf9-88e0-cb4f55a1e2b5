import "../../common/models/domain.tsp";
import "../changes/models.tsp";

namespace Models {
  /**
   * Indicates the visibility of an Entry
   */
  alias EntryVisibility = "PUBLIC" | "PRIVATE";

  /**
   * Represents the value of a field
   */
  alias FieldValue = string | string[];

  @doc("Represents the data of a Field in an Entry")
  model FieldData {
    @doc("Reference to thre related Field")
    fieldId: Domain.UniqueID;

    @doc("The value of the Field")
    value: FieldValue;
  }

  model EntryProps {
    @visibility(Lifecycle.Read)
    @doc("The unique identifier of the Collection to which the Entry belongs")
    collectionId: Domain.UniqueID;

    @doc("The visibility of the Entry")
    visibility: EntryVisibility;

    @visibility(Lifecycle.Read, Lifecycle.Create)
    @doc("The current data of the Entry")
    data: FieldData[];

    @visibility(Lifecycle.Read)
    @doc("The history of changes made to the Entry")
    changes: Change[];

    @visibility(Lifecycle.Read)
    @doc("The user who created the Entry")
    createdBy: Domain.UniqueID;
  }

  @doc("Representes a Entry in a Collection (A row in a collection)")
  model Entry extends Domain.Entity<EntryProps> {}

  model CreateEntry is Create<Entry>;
}
