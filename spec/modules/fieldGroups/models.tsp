import "../../common/models/domain.tsp";
import "../../modules/fields";

namespace Models {
  model FieldGroupProps {
    @visibility(Lifecycle.Read)
    @doc("The unique identifier of the Collection to which the field group belongs")
    collectionId: Domain.UniqueID;

    @doc("Human-readable name of the field group")
    name: string;

    @doc("The position of the field group")
    position: numeric;

    @visibility(Lifecycle.Read)
    @doc("The fields associated with the field group")
    fields: Field[];

    @visibility(Lifecycle.Create, Lifecycle.Update)
    @doc("Unique identifier's of related fields")
    fieldIds: Domain.UniqueID[];
  }
  @doc("Represents a group of fields within a Collection entity")
  model FieldGroup extends Domain.Entity<FieldGroupProps> {}

  model CreateFieldGroup is Create<FieldGroup>;
}
