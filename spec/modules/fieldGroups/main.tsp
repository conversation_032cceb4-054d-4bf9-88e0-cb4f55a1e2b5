import "../../common/models/rest.tsp";
import "./models.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace FieldGroups {
  interface Routes {
    @doc("Fetches the details of a specific Field Group by its unique identifier")
    @get
    getFieldGroup(
      @path fieldGroupId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.FieldGroup> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Updates an existing Field Group by its unique identifier.")
    @patch(#{ implicitOptionality: true })
    updateFieldGroup(
      @path fieldGroupId: Domain.UniqueID,
      @body body: Models.FieldGroup,
    ): Rest.ApiResponse.Success<Models.FieldGroup> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Deletes a specific Field Group by its unique identifier.")
    @delete
    deleteFieldGroup(
      @path fieldGroupId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.CommonErrors;

    // Fields

    @doc("Creates a Field associated with a specific Field Group.")
    @route("/{fieldGroupId}/fields")
    @post
    createField(
      @path fieldGroupId: Domain.UniqueID,
      @body body: Models.CreateField,
    ): Rest.ApiResponse.Created<Models.Field> | Rest.ApiErrors.CommonErrors;
  }
}
