import "../../common/models/rest.tsp";
import "./models.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace Fields {
  interface Routes {
    @doc("Retrieves the details of a specific Field by its unique identifier")
    @get
    getField(
      @path fieldId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Field> | Rest.ApiErrors.CommonErrors;

    @doc("Updates an existing Field by its unique identifier")
    @patch(#{ implicitOptionality: true })
    updateField(
      @path fieldId: Domain.UniqueID,
      @body body: Models.Field,
    ): Rest.ApiResponse.Success<Models.Field> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Deletes a specific Field by its unique identifier")
    @delete
    deleteField(
      @path fieldId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.CommonErrors;
  }
}
