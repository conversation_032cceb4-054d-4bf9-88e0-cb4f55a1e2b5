import "../../common/models/domain.tsp";
namespace Models {
  alias SimpleValueFieldTypes = "TEXT" | "TEXTAREA" | "IMAGE-UPLOAD";
  alias SelectFieldTypes = "SELECT" | "SELECT-MANY";

  // Represents all available field types, including both simple value and selection-based fields
  alias FieldTypes = SimpleValueFieldTypes | SelectFieldTypes;

  // Defines the possible visibility settings for a field.
  alias FieldVisibility = "PUBLIC" | "PRIVATE";

  @doc("Represents a Select option")
  model FieldOption {
    @doc("A human-readable label")
    label: string;

    @doc("The value of the option")
    value: string;
  }

  model CommonFieldProps<T = FieldTypes> {
    @doc("Human-readable field name")
    name: string;

    @doc("Represents the position of the field in a Field Group")
    position: numeric;

    @doc("Represents the maximum length of the field value")
    maxLength?: numeric;

    @doc("Indicates if the field is mandatory")
    mandatory: boolean;

    @doc("Indicates if the field is visibile to the Federated Catalog")
    visibility: FieldVisibility;

    @visibility(Lifecycle.Read)
    @doc("The unique identifier of the Field Group this field belongs to")
    fieldGroupId: Domain.UniqueID;

    @doc("Indicates the type of the field")
    type: T;

    @doc("Indicates if the field is shown in a compact table view")
    preview: boolean;
  }

  model SimpleValueField
    is Domain.Entity<CommonFieldProps<SimpleValueFieldTypes>>;

  model SelectFieldProps {
    options: {
      ...FieldOption;
    }[];
  }

  model SelectField
    is Domain.Entity<CommonFieldProps<SelectFieldTypes> & SelectFieldProps>;

  alias Field = SimpleValueField | SelectField;

  alias CreateField = Create<SimpleValueField> | Create<SelectField>;
}
