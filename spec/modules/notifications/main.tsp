import "../../common/models/rest.tsp";
import "./models.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace Notifications {
  interface Routes {
    @doc("Retrieves all Notifications associated with the authenticated user")
    @get
    getNotifications(
    ): Rest.ApiResponse.Success<Models.Notification[]> | Rest.ApiErrors.InternalServerError | Rest.ApiErrors.Unauthorized;

    @doc("Mark a notification as read")
    @route("/{notificationId}/read")
    @patch(#{ implicitOptionality: true })
    markAsRead(
      notificationId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Notification> | Rest.ApiErrors.InternalServerError | Rest.ApiErrors.Unauthorized;

    @doc("Mark all authenticated user notifications as read")
    @route("/all-read")
    @patch(#{ implicitOptionality: true })
    markAllAsRead(
      notificationId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.InternalServerError | Rest.ApiErrors.Unauthorized;
  }
}
