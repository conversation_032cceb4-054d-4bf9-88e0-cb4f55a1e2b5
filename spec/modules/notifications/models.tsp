import "../../common/models/domain.tsp";

namespace Models {
  model NotificationProps {
    @doc("Related user")
    userId: Domain.UniqueID;

    @doc("Related activity")
    activityId: Domain.UniqueID;

    @doc("Indicates true if the notification is read")
    read: boolean;

    @doc("Indicates true if the notification is already sent")
    sent: boolean;
  }

  @doc("Represents a Notification")
  model Notification extends Domain.Entity<NotificationProps> {}
}
