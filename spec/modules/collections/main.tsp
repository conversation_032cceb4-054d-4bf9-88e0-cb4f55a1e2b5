import "@typespec/http";
import "../../common/models/rest.tsp";
import "../services/models.tsp";
import "../fieldGroups/models.tsp";
import "../entries/models.tsp";
import "../activities/models.tsp";
import "../invites/models.tsp";
import "../users/models.tsp";
import "./models.tsp";

using TypeSpec.Http;

namespace Collections {
  interface Routes {
    // Collections

    @doc("Retrieves all Collections associated with the authenticated user")
    @get
    getCollections(
    ): Rest.ApiResponse.Success<Models.Collection[]> | Rest.ApiErrors.InternalServerError | Rest.ApiErrors.Unauthorized;

    @doc("Fetches the details of a specific Collection by its unique identifier")
    @get
    getCollection(
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Collection> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Updates a Collection identified by its unique identifier")
    @patch(#{ implicitOptionality: true })
    updateCollection(
      @path collectionId: Domain.UniqueID,
      @body collection: Models.Collection,
    ): Rest.ApiResponse.Success<Models.Collection> | Rest.ApiErrors.CommonWithAuthErrors;

    // Services

    @doc("Retrieves all Services associated with a specific Collection")
    @route("/{collectionId}/services")
    @get
    @list
    getCollectionServices(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.PaginatedSuccess<Models.Service[]> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Creates a Service associated with a specific Collection")
    @route("/{collectionId}/services")
    @post
    createCollectionService(
      @path collectionId: Domain.UniqueID,
      @body service: Models.CreateService,
    ): Rest.ApiResponse.Created<Models.Service> | Rest.ApiErrors.CommonWithAuthErrors;

    // Field Groups

    @doc("Retrieves all Field Groups associated with a specific Collection")
    @get
    @route("/{collectionId}/field-groups")
    getCollectionFieldGroups(
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.FieldGroup[]> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Creates a Field Group associated with a specific Collection")
    @route("/{collectionId}/field-groups")
    @post
    createCollectionFieldGroup(
      @path collectionId: Domain.UniqueID,

      @body
      fieldGroups: Models.CreateFieldGroup,
    ): Rest.ApiResponse.Created<Models.FieldGroup> | Rest.ApiErrors.CommonWithAuthErrors;

    // Entries

    @doc("Retrieves all Entries associated with a specific Collection")
    @route("/{collectionId}/entries")
    @list
    @get
    getCollectionEntries(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.PaginatedSuccess<Models.Entry[]> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Creates a new Entry in a specific Collection")
    @post
    @route("/{collectionId}/entries")
    createCollectionEntry(
      @path collectionId: Domain.UniqueID,
      @body entry: Models.CreateEntry,
    ): Rest.ApiResponse.Created<Models.Entry> | Rest.ApiErrors.CommonWithAuthErrors;

    // Activities

    @doc("Retrieves all Activity associated with a specific Collection")
    @route("/{collectionId}/activity")
    @get
    @list
    getCollectionActivity(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.PaginatedSuccess<Models.Activity[]> | Rest.ApiErrors.CommonWithAuthErrors;

    // Invites

    @doc("Retrieves all Invites associated with a specific Collection")
    @route("/{collectionId}/invites")
    @get
    @list
    getCollectionInvites(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.PaginatedSuccess<Models.CollectionInvite[]> | Rest.ApiErrors.CommonWithAuthErrors;

    // Users

    @doc("Retrieves all Users associated with a specific Collection")
    @route("/{collectionId}/users")
    @get
    @list
    getCollectionUsers(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
      @path collectionId: Domain.UniqueID,
    ): Rest.ApiResponse.PaginatedSuccess<Models.User[]> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Updates a user's role within a collection")
    @route("/{collectionId}/users/{userId}")
    @patch(#{ implicitOptionality: false })
    updateCollectionUserAssignment(
      @path collectionId: Domain.UniqueID,
      @path userId: Domain.UniqueID,
      @body _: Models.UpdateUserRole,
    ): Rest.ApiResponse.Success<Models.User> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Removes a user from a collection")
    @route("/{collectionId}/users/{userId}")
    @delete
    deleteCollectionUserAssignment(
      @path collectionId: Domain.UniqueID,
      @path userId: Domain.UniqueID,
    ): Rest.ApiResponse.NoContent | Rest.ApiErrors.CommonWithAuthErrors;

    // Stats

    @doc("Retrieves statistics for current user collections")
    @route("/stats")
    @get
    getCollectionsStats(
    ): Rest.ApiResponse.Success<Models.Statistic[]> | Rest.ApiErrors.InternalServerError | Rest.ApiErrors.Unauthorized;
  }
}
