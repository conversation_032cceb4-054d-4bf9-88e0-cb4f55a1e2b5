import "../../common/models/domain.tsp";

namespace Models {
  model CollectionProps {
    @doc("A human-readable name for the collection")
    name: string;

    @doc("The description of the collection")
    description?: string;
  }

  @doc("Represents a Collection entity")
  model Collection extends Domain.Entity<CollectionProps> {}

  model Statistic {
    @doc("The unique identifier of the collection")
    collectionId: Domain.UniqueID;

    entries: {
      @doc("The number of entries in the collection")
      total: int64;

      @doc("The number of entries in the collection that are publicly visible")
      public: int64;
    };

    @doc("The number of users who are members of the collection")
    members: int64;
  }
}
