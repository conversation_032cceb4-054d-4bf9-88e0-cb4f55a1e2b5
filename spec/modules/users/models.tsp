import "../../common/models/domain.tsp";

namespace Models {
  alias PLATFORM_ROLES = "PLATFORM_MANAGER";

  alias COLLECTION_ROLES =
    | "COLLECTION_MANAGER"
    | "EDITOR"
    | "CONTRIBUTOR"
    | "VIEWER";
  alias UserRoles = PLATFORM_ROLES | COLLECTION_ROLES;
  model UserAssignment {
    @doc("Represents the user role")
    role: UserRoles;

    @doc("Represents the collection the user is assigned to")
    collectionId: Domain.UniqueID;
  }

  model UserProps {
    @visibility(Lifecycle.Create)
    @doc("Represents the related invite")
    inviteId: Domain.UniqueID;

    @example("John Doe")
    @doc("Represents the user full name")
    @visibility(Lifecycle.Create, Lifecycle.Read, Lifecycle.Update)
    name: string;

    @format("email")
    @example("<EMAIL>")
    @doc("Represents the user email address")
    @visibility(Lifecycle.Create, Lifecycle.Read, Lifecycle.Update)
    email: string;

    @doc("Represents the user profile preferences")
    settings: {
      @visibility(Lifecycle.Read, Lifecycle.Update)
      @example(true)
      @doc("Represents the user email notifications preference")
      emailNotificationsEnabled: boolean;
    };

    @visibility(Lifecycle.Read)
    assignments: UserAssignment[];
  }

  model Login {
    @format("email")
    @example("<EMAIL>")
    @doc("Represents the user email address")
    email: string;
  }

  @doc("User model")
  model User extends Domain.Entity<UserProps> {}

  model UpdateUserRole {
    role: COLLECTION_ROLES;
  }
}
