import "../../common/models/rest.tsp";
import "./models.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace Users {
  interface Routes {
    @useAuth(BearerAuth)
    @doc("Retrieves the details of the authenticated user")
    @route("/me")
    @get
    getCurrentUser(
    ): Rest.ApiResponse.Success<Models.User> | Rest.ApiErrors.CommonWithAuthErrors;

    @useAuth(BearerAuth)
    @doc("Retrieves the list of users")
    @get
    getUsers(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
    ): Rest.ApiResponse.PaginatedSuccess<Models.User[]> | Rest.ApiErrors.CommonWithAuthErrors;

    @useAuth(BearerAuth)
    @doc("Retrieves the details of a specific user by its unique identifier")
    @get
    getUser(
      @path userId: string,
    ): Rest.ApiResponse.Success<Models.User> | Rest.ApiErrors.CommonWithAuthErrors;

    @useAuth(BearerAuth)
    @patch(#{ implicitOptionality: true })
    updateUser(
      @path userId: string,
      @body user: Models.User,
    ): Rest.ApiResponse.Success<Models.User> | Rest.ApiErrors.CommonWithAuthErrors;

    @useAuth(BearerAuth)
    @doc("Creates a new user")
    @post
    createUser(
      @path inviteId: string,
    ): Rest.ApiResponse.Created<Models.User> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Temporary Endpoint for login")
    @route("/login")
    @post
    login(
      @body _: Models.Login,
    ): Rest.ApiResponse.Success<string> | Rest.ApiErrors.CommonWithAuthErrors;
  }
}
