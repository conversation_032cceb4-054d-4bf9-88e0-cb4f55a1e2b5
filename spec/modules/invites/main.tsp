import "../../common/models/rest.tsp";
import "./models.tsp";
import "@typespec/http";

using TypeSpec.Http;

namespace Invites {
  interface Routes {
    @doc("Retrieves the list of invites (platform wide)")
    @get
    getInvites(
      _: Rest.ApiRequest.EmptyRequest &
        Rest.ApiRequest.PaginatedRequest &
        Rest.ApiRequest.SortedRequest,
    ): Rest.ApiResponse.PaginatedSuccess<Models.Invite[]> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Retrieves a specific invite")
    @get
    getInvite(
      @path inviteId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Invite> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Sends an invite to a email address")
    @post
    invite(
      @body invite: Models.CreateInvite,
    ): Rest.ApiResponse.Created<Models.Invite> | Rest.ApiErrors.CommonWithAuthErrors;

    @doc("Cancels an invite")
    @route("/{inviteId}/cancel")
    @patch
    cancelInvite(
      @path inviteId: Domain.UniqueID,
    ): Rest.ApiResponse.Success<Models.Invite> | Rest.ApiErrors.CommonWithAuthErrors;
  }
}
