import "../../common/models/domain.tsp";
import "../users/models.tsp";

namespace Models {
  alias InviteStatus = "PENDING" | "EXPIRED" | "REGISTERED" | "CANCELED";

  alias BaseInviteProps = PickProperties<UserProps, "name" | "email"> & {
    @doc("Represents the invite status")
    @example("PENDING")
    @visibility(Lifecycle.Read)
    status: InviteStatus;
  };

  model PlatformInviteProps {
    ...BaseInviteProps;
    role: PLATFORM_ROLES;
  }

  model CollectionInviteProps {
    ...BaseInviteProps;

    @doc("Represents the collection the user is invited to")
    collectionId: Domain.UniqueID;

    @doc("Represents the user role")
    role: COLLECTION_ROLES;
  }

  model PlatformInvite extends Domain.Entity<PlatformInviteProps> {}
  model CollectionInvite extends Domain.Entity<CollectionInviteProps> {}

  alias Invite = PlatformInvite | CollectionInvite;
  alias CreateInvite = Create<PlatformInvite> | Create<CollectionInvite>;
}
