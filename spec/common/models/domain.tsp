namespace Domain {
  @format("uuid")
  scalar UniqueID extends string;
  model Entity<TProps> {
    @visibility(Lifecycle.Read)
    @doc("Universal unique identifier")
    id: UniqueID;

    ...OmitProperties<TProps, "id" | "createdAt" | "updatedAt">;

    @visibility(Lifecycle.Read)
    @doc("Date and time of creation")
    createdAt: utcDateTime;

    @visibility(Lifecycle.Read)
    @doc("Date and time of the last update")
    updatedAt?: utcDateTime;
  }
}
