import "@typespec/http";

using TypeSpec.Http;

namespace Rest {
  namespace ApiErrors {
    /**
     * Base error model.
     *
     * This model represents a generic error that can be extended by other
     * error models to provide additional information.
     *
     * @model BaseError
     * @template T - The type of error.
     * @description Base error model that can be extended by other error models.
     */
    model BaseError<T> {
      @doc("Indicates the type of the error")
      type: T;

      @doc("Represents an error message that provides details about the error that occurred")
      message: string;
    }

    @error
    @doc("This error is returned when the requested resource does not exist")
    model NotFound extends BaseError<"NotFoundError"> {
      @statusCode _: 404;
    }

    @error
    @doc("This error is returned when the request is invalid or cannot be processed")
    model BadRequest extends BaseError<"BadRequestError"> {
      @statusCode _: 400;
    }

    @error
    @doc("This error is returned when the request is unauthorized")
    model Unauthorized extends BaseError<"UnauthorizedError"> {
      @statusCode _: 401;
    }

    @error
    @doc("This error is returned when the request is forbidden or the user does not have permission to access the resource")
    model Forbidden extends BaseError<"ForbiddenError"> {
      @statusCode _: 403;
    }

    @error
    @doc("This error is returned when an internal server error occurs and the request cannot be processed")
    model InternalServerError extends BaseError<"InternalServerError"> {
      @statusCode _: 500;
    }

    alias CommonErrors =
      | NotFound
      | BadRequest
      | Forbidden
      | InternalServerError;

    alias CommonWithAuthErrors = CommonErrors | Unauthorized;
  }

  namespace ApiResponse {
    // @doc("Represents a successful API response")
    model Success<T> {
      @statusCode _: 200;
      @body body: T;
    }

    model PaginatedSuccess<T> {
      @statusCode _: 200;

      @pageItems
      @doc("The data returned in the successful response")
      items: T;

      @doc("Indicates the total number of items")
      @example(596)
      totalItems: uint32;
    }

    model NoContent {
      @statusCode _: 204;
    }

    model Created<T> {
      @statusCode _: 201;
      @body body: T;
    }

    @error
    model Error {
      type: ApiErrors.CommonWithAuthErrors;
      message: string;
    }
  }

  namespace ApiRequest {
    model EmptyRequest {
      @body body: void;
    }
    model PaginatedRequest {
      @example(1)
      @doc("The starting position of the results to return. Used for pagination.")
      @query
      offset?: uint32;

      @example(20)
      @doc("The maximum number of items to return in the response. Used for pagination.")
      @query
      limit?: uint32;
    }

    model SortedRequest {
      @example("name")
      @doc("The field by which the results should be sorted")
      @query
      orderBy?: string;

      @doc("The sort order: 'ASC' for ascending or 'DESC' for descending")
      @query
      orderDirection?: "ASC" | "DESC";
    }
  }
}
