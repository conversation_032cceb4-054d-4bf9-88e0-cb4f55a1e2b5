{"name": "ciimar-biobanks-backend", "version": "1.0.0", "description": "", "main": "dist/src/index.js", "scripts": {"dev": "nodemon | pino-pretty", "test": "pnpm dotenv -e .env.test jest", "compile": "tsc --noEmit", "build": "pnpm clean && tsc", "build:image": "docker build -t ciimar-biobanks-backend:${npm_package_version:-latest} -t ciimar-biobanks-backend:latest .", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint --fix . --max-warnings 0", "format": "prettier --config .prettierrc --write \"src/**/*.ts\" --write \"spec/**/*.tsp\" --log-level warn", "prepare": "husky", "dump:example-environment": "ts-node src/scripts/dumpExampleEnvironment.ts", "db:push": "drizzle-kit push --config ./src/shared/database/drizzle.config.ts", "db:migrate:new": "drizzle-kit --config ./src/shared/database/drizzle.config.ts generate", "db:migrate:run": "drizzle-kit --config ./src/shared/database/drizzle.config.ts migrate", "db:studio": "drizzle-kit --config ./src/shared/database/drizzle.config.ts studio", "db:seed": "ts-node src/shared/database/seed/index.ts", "db:seed:reset": "ts-node src/shared/database/seed/index.ts --reset", "spec:compile": "cd spec/ && tsp compile main.tsp", "spec:dev": "cd spec/ && concurrently \"tsp compile main.tsp --watch\" \"redocly preview-docs dist/openapi.v1.json\""}, "keywords": [], "author": "", "license": "ISC", "nodemonConfig": {"watch": ["src"], "ext": "ts", "exec": "pnpm dotenv -e .env -- ts-node src/index.ts"}, "devDependencies": {"@azure-tools/typespec-ts": "^0.42.0", "@eslint/js": "^9.31.0", "@jest/globals": "30.0.4", "@jest/types": "^30.0.1", "@redocly/cli": "^1.34.4", "@swc/core": "^1.12.14", "@swc/jest": "^0.2.39", "@types/body-parser": "^1.19.6", "@types/config": "^3.3.5", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^24.0.13", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@typespec/compiler": "1.1.0", "@typespec/http": "1.1.0", "@typespec/openapi": "^1.1.0", "@typespec/openapi3": "^1.1.0", "@typespec/prettier-plugin-typespec": "1.1.0", "@typespec/rest": "^0.71.0", "@typespec/versioning": "^0.71.0", "concurrently": "^9.2.0", "drizzle-kit": "^0.31.4", "drizzle-seed": "^0.3.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jest": "^30.0.4", "jiti": "^2.4.2", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "pino-pretty": "^13.0.0", "prettier": "^3.6.2", "rimraf": "^6.0.1", "supertest": "^7.1.3", "ts-essentials": "^10.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0"}, "dependencies": {"@electric-sql/pglite": "^0.3.4", "body-parser": "^2.2.0", "config": "^4.0.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "dotenv-cli": "^8.0.0", "drizzle-orm": "^0.44.2", "eslint": "^9.31.0", "express": "^5.1.0", "helmet": "^8.1.0", "jose": "^6.0.11", "lodash": "^4.17.21", "pg": "^8.16.3", "pino": "^9.7.0", "swagger-ui-express": "^5.0.1", "yup": "^1.6.1"}, "packageManager": "pnpm@10.13.1", "lint-staged": {"src/**/*.ts": ["prettier --write", "eslint --fix ."]}}