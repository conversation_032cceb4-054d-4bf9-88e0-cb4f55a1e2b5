import { stubCollection } from '../../modules/collections/domain/__stubs__/collection.stub'
import { stubUser } from '../../modules/users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../modules/users/domain/__stubs__/userAssignment.stub'
import { UserUtils } from '../userUtils'

describe('UserUtils.hasRole', () => {
  it('should return true if user has a role in a specific collection', () => {
    const collection = stubCollection()

    const user = stubUser({
      userAssignments: [
        stubUserAssignment({
          collectionId: collection.id,
          role: 'CONTRIBUTOR',
        }),
      ],
    })

    expect(UserUtils.hasRole(user, 'CONTRIBUTOR', collection.id)).toBe(true)
  })

  it('should return false if user does not have a role in a specific collection', () => {
    const collection = stubCollection()

    const user = stubUser({
      userAssignments: [
        stubUserAssignment({ collectionId: collection.id, role: 'EDITOR' }),
      ],
    })

    expect(UserUtils.hasRole(user, 'CONTRIBUTOR', collection.id)).toBe(false)
  })

  it('should return true if user has a global role', () => {
    const user = stubUser({
      userAssignments: [
        stubUserAssignment({
          role: 'PLATFORM_MANAGER',
          collectionId: undefined,
        }),
      ],
    })
    expect(UserUtils.hasRole(user, 'PLATFORM_MANAGER')).toBe(true)
  })

  it('should return false if user does not have a global role', () => {
    const collection = stubCollection()

    const user = stubUser({
      userAssignments: [
        stubUserAssignment({
          collectionId: collection.id,
          role: 'COLLECTION_MANAGER',
        }),
      ],
    })
    expect(UserUtils.hasRole(user, 'PLATFORM_MANAGER')).toBe(false)
  })

  it('should return false if user has multiple roles, but not the one being checked', () => {
    const collection = stubCollection()

    const user = stubUser({
      userAssignments: [
        stubUserAssignment({
          collectionId: collection.id,
          role: 'CONTRIBUTOR',
        }),
        stubUserAssignment({ collectionId: collection.id, role: 'EDITOR' }),
      ],
    })

    expect(UserUtils.hasRole(user, 'PLATFORM_MANAGER', collection.id)).toBe(
      false,
    )
  })

  it('should return false if user has no roles assigned', () => {
    const user = stubUser({
      userAssignments: [],
    })
    const collection = stubCollection()
    expect(UserUtils.hasRole(user, 'CONTRIBUTOR', collection.id)).toBe(false)
  })

  it('should return false if user has no user assignments, but a collection is specified', () => {
    const user = stubUser({
      userAssignments: [],
    })
    const collection = stubCollection()
    expect(UserUtils.hasRole(user, 'CONTRIBUTOR', collection.id)).toBe(false)
  })

  it('should return false if user has no user assignments, and no collection is specified', () => {
    const user = stubUser({
      userAssignments: [],
    })
    expect(UserUtils.hasRole(user, 'CONTRIBUTOR')).toBe(false)
  })
})
