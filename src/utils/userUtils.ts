import { User } from '../modules/users/domain/user'
import { UserAssignment } from '../modules/users/domain/userAssignment'
import { Role } from '../modules/users/types/user'
import { UniqueEntityID } from '../shared/domain/uniqueEntityID'

class UserUtils {
  /**
   * Retrieves the user's assignment for a specific collection.
   *
   * @param {User} user - The user whose assignment is being retrieved
   * @param {UniqueEntityID} collectionId - The collection for which the assignment is needed
   * @return {UserAssignment | undefined} - The assignment object if found, otherwise undefined
   */

  private static getUserAssignment(
    user: User,
    collectionId: UniqueEntityID,
  ): UserAssignment | undefined {
    return user.userAssignments.find((assignment) =>
      assignment.collectionId?.equals(collectionId),
    )
  }

  /**
   * Check if a user has a given role, either globally or on a specific
   * collection.
   *
   * @param {User} user - The user to check
   * @param {Role} role - The role to check
   * @param {UniqueEntityID} [collectionId] - The collection to check on
   * @return {boolean} - Whether the user has the role
   */
  static hasRole(
    user: User,
    role: Role,
    collectionId?: UniqueEntityID,
  ): boolean {
    if (collectionId) {
      const assignment = this.getUserAssignment(user, collectionId)

      if (!assignment) return false

      return role === assignment.role
    }

    const userAssignedRoles = user.userAssignments.map(
      (assignment) => assignment.role,
    )

    return userAssignedRoles.includes(role)
  }

  static hasAssignment(user: User, collectionId: UniqueEntityID) {
    return this.getUserAssignment(user, collectionId) !== undefined
  }
}

export { UserUtils }
