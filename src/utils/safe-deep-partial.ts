type SafeDeepPartial<Thing> = Thing extends (...args: unknown[]) => unknown
  ? Thing
  : Thing extends Array<infer ArrayType>
    ? // TODO: Not deep partial for arrays, was this intentional? Array<SafeDeepPartial<ArrayType>>
      Array<ArrayType>
    : Thing extends object
      ? DeepPartialObject<Thing>
      : Thing | undefined

type DeepPartialObject<Thing> = {
  [Key in keyof Thing]?: SafeDeepPartial<Thing[Key]>
}

export type { SafeDeepPartial }
