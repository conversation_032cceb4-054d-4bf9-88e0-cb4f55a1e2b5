import { User } from '../../modules/users/domain/user'
import { AuthContext } from '../../shared/infra/auth/authContext'
import { UseCase } from '../../shared/infra/useCase'

export function route(path: string, version = 'v1') {
  return `/${version}${path}`
}

export async function execute<Input, Output>(
  useCase: UseCase<Input, Output>,
  request: Input,
  user?: User,
): Promise<Output> {
  return AuthContext.run(user, () => {
    return useCase.execute(request)
  })
}
