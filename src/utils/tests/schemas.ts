import { AnyObject, array, number, object, ObjectSchema } from 'yup'

function arraySchema<T extends AnyObject>(itemsSchema: ObjectSchema<T>) {
  return array(itemsSchema).of(itemsSchema)
}

function paginatedSchema<T extends AnyObject>(itemSchema: ObjectSchema<T>) {
  return object({
    items: array().of(itemSchema).required(),
    totalItems: number().required(),
  })
}

export { paginatedSchema, arraySchema }
