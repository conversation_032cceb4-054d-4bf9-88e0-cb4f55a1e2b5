import { ServicesNotInitialized } from '../errors/serviceErrors'

abstract class ServicesInitializer<Services extends Record<string, unknown>> {
  private services?: Services

  protected abstract setup(): Promise<Services>

  get<Service extends keyof Services>(service: Service): Services[Service] {
    if (!this.services) {
      throw new ServicesNotInitialized(this.constructor.name)
    }
    return this.services[service]
  }

  async initialize(): Promise<void> {
    this.services = await this.setup()
  }
}

export { ServicesInitializer }
