import { object, string, InferType, number } from 'yup'

const PaginationSchema = object({
  offset: number().transform((value) => Number(value)),
  limit: number().transform((value) => Number(value)),
})

const SortSchema = object({
  orderBy: string(),
  orderDirection: string<'ASC' | 'DESC'>().oneOf(['ASC', 'DESC']),
})

const QuerySchema = PaginationSchema.concat(SortSchema)

type PaginationParams = InferType<typeof PaginationSchema>
type SortParams = InferType<typeof SortSchema>
type QueryParams = InferType<typeof QuerySchema>

export type { PaginationParams, SortParams, QueryParams }
export { PaginationSchema, SortSchema, QuerySchema }
