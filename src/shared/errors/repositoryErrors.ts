import { constants } from 'http2'
import { AppError } from '../errors'

const {
  HTTP_STATUS_NOT_FOUND,
  HTTP_STATUS_CONFLICT,
  HTTP_STATUS_INTERNAL_SERVER_ERROR,
  HTTP_STATUS_BAD_REQUEST,
} = constants

export class RepositoryError extends AppError {
  constructor(
    message: string,
    status: number = HTTP_STATUS_INTERNAL_SERVER_ERROR,
    cause?: unknown,
  ) {
    super(`Repository error: ${message}`, status, 'error')
    this.cause = cause
  }
}

export class EntityNotFound extends RepositoryError {
  constructor(entityName: string, id: string) {
    super(`${entityName} with ID ${id} not found`, HTTP_STATUS_NOT_FOUND)
  }
}

export class DuplicateEntity extends RepositoryError {
  constructor(entityName: string, field: string, value: string) {
    super(
      `${entityName} with ${field} '${value}' already exists`,
      HTTP_STATUS_CONFLICT,
    )
  }
}

export class InvalidEntity extends RepositoryError {
  constructor(entityName: string, details: string) {
    super(`${entityName} validation failed: ${details}`, HTTP_STATUS_CONFLICT)
  }
}

export class CannotModifyDeletedEntity extends RepositoryError {
  constructor(entityName: string, id: string) {
    super(
      `Cannot modify ${entityName} with ID ${id} because it is marked as deleted.`,
      HTTP_STATUS_BAD_REQUEST,
    )
  }
}

export class UniqueConstraintViolation extends RepositoryError {
  constructor(
    entityName: string,
    constraintName: string,
    originalError?: unknown,
  ) {
    super(
      `Unique constraint '${constraintName}' violated for entity '${entityName}'`,
      HTTP_STATUS_CONFLICT,
      originalError,
    )
  }
}
