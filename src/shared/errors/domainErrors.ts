import { constants } from 'http2'
import { AppError } from '.'

export abstract class DomainError extends AppError {
  public constructor(
    message: string,
    public readonly entity: string,
    status: number = constants.HTTP_STATUS_BAD_REQUEST,
  ) {
    super(message, status)
  }
}

export class InvalidValue extends DomainError {
  public constructor(
    entity: string,
    public readonly field: string,
    public readonly value: unknown,
    status?: number,
  ) {
    super(`Invalid value for '${field}': ${value}`, entity, status)
  }
}

export class AlreadyDeleted extends DomainError {
  public constructor(entity: string, status?: number) {
    super(`Can't delete the ${entity} as it is already deleted`, entity, status)
  }
}

export class InvalidState extends DomainError {
  public constructor(message: string, entity: string) {
    super(message, entity)
  }
}
