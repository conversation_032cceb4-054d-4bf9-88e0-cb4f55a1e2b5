import { constants } from 'http2'
import { AppError } from '.'

export abstract class UseCaseError extends AppError {
  constructor(
    message: string,
    status: number,
    public readonly useCase: string,
  ) {
    super(message, status)
  }
}

export class InvalidInput extends UseCaseError {
  constructor(
    message: string,
    useCase: string,
    override readonly cause: unknown,
  ) {
    super(message, constants.HTTP_STATUS_BAD_REQUEST, useCase)
  }
}

export class NotAllowed extends UseCaseError {
  constructor(useCase: string, message?: string) {
    super(
      message ?? 'You do not have permission to perform this operation',
      constants.HTTP_STATUS_FORBIDDEN,
      useCase,
    )
  }
}

export class NotFound extends UseCaseError {
  private static generateDefaultMessage(resourceName?: string): string {
    if (resourceName) {
      return `The resource ${resourceName} was not found`
    }
    return 'The requested resource was not found'
  }
  constructor(
    useCase: string,
    public readonly resourceName?: string,
  ) {
    super(
      NotFound.generateDefaultMessage(resourceName),
      constants.HTTP_STATUS_NOT_FOUND,
      useCase,
    )
  }
}
