import { constants } from 'http2'
import { AppError } from '.'

export abstract class ServiceError extends AppError {
  constructor(
    message: string,
    status: number = constants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
  ) {
    super(message, status)
  }
}

export class ServicesNotInitialized extends ServiceError {
  constructor(className?: string) {
    super(`${className ?? 'Service Initializer'} was not initialized`)
  }
}
