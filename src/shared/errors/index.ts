import { constants } from 'http2'

export const INTERNAL_ERROR = constants.HTTP_STATUS_INTERNAL_SERVER_ERROR

export type ErrorLevel = 'warn' | 'error'

export abstract class AppError extends Error {
  public readonly level: ErrorLevel

  constructor(
    message: string,
    public readonly status: number = INTERNAL_ERROR,
    level?: ErrorLevel,
  ) {
    super(message)
    this.level = level ?? (status >= INTERNAL_ERROR ? 'error' : 'warn')
  }
}

export class Unexpected extends AppError {
  override readonly cause: unknown

  public constructor(cause: unknown) {
    super('An unexpected error occurred', INTERNAL_ERROR)
    this.cause = cause
  }
}

export class Unauthorized extends AppError {
  constructor() {
    super('Unauthorized', constants.HTTP_STATUS_UNAUTHORIZED)
  }
}

export class RouteNotFound extends AppError {
  constructor(path: string) {
    super(
      `The requested path '${path}' was not found.`,
      constants.HTTP_STATUS_NOT_FOUND,
    )
  }
}
