import pino from 'pino'
import { ILogger } from '../types'

class PinoLogger implements ILogger {
  private readonly _logger: pino.Logger

  constructor() {
    // Use process.env instead of Environment.instance.get('ENVIRONMENT') to avoid circular dependency
    // and to ensure that the logger is initialized before any other services that might depend on it.
    // TODO: Should we validate the environment variable here?
    const minimumLogLevel = process.env.LOG_LEVEL ?? 'info'

    this._logger = pino({
      level: minimumLogLevel,
    })
  }

  info(message: string, metadata?: object): void {
    this._logger.info({ metadata }, message)
  }

  error(message: string, metadata?: object): void {
    this._logger.error({ metadata }, message)
  }
  warn(message: string, metadata?: object): void {
    this._logger.warn({ metadata }, message)
  }
  debug(message: string, metadata?: object): void {
    this._logger.debug({ metadata }, message)
  }
  fatal(message: string, metadata?: object): void {
    this._logger.fatal({ metadata }, message)
  }

  trace(message: string, metadata?: object): void {
    this._logger.trace({ metadata }, message)
  }
}

export { PinoLogger }
