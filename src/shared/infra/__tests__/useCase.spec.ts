import { UseCase } from '../useCase'

class StubUseCase extends UseCase<void, string> {
  constructor() {
    super(StubUseCase.name)
  }

  execute = jest.fn().mockImplementation(() => Promise.resolve('result'))
}

describe('UseCase', () => {
  let useCase: UseCase<void, string>

  beforeEach(() => {
    useCase = new StubUseCase()
  })

  it('should have a name', () => {
    expect(useCase.name).toBeDefined()
    expect(useCase.name).toBe('StubUseCase')
  })

  it('should have a execute method', () => {
    expect(useCase.execute).toBeDefined()
  })

  it('should be able to execute a request', async () => {
    const result = await useCase.execute()

    expect(result).toBe('result')
    expect(useCase.execute).toHaveBeenCalled()
  })
})
