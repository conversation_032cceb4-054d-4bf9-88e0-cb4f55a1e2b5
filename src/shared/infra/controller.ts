import { Schema, ValidationError } from 'yup'
import { InvalidInput } from '../errors/useCaseErrors'
import { Handler, Method, Result } from './http/routes/route'
import { UseCase } from './useCase'

interface RequestInput<T extends Record<string, string>, U, Q = undefined> {
  params: T
  body?: U
  query?: Q
}

abstract class Controller<
  ReqParams extends Record<string, string>,
  ReqBody,
  QueryParams,
  Input,
  Output = void,
> implements Handler
{
  constructor(
    public readonly method: Method,
    public readonly route: string,
    private readonly successStatus: number,
    private readonly useCase: UseCase<Input, Output>,
    private readonly schema: Schema<
      RequestInput<ReqParams, ReqBody, QueryParams>
    >,
  ) {}

  protected async validateInput(
    input: unknown,
  ): Promise<RequestInput<ReqParams, ReqBody, QueryParams>> {
    return await this.schema.validate(input, { stripUnknown: true })
  }

  protected abstract parseInput(
    request: RequestInput<ReqParams, ReqBody, QueryParams>,
  ): Input

  public async handle(request: unknown): Promise<Result<Output>> {
    let input
    try {
      input = this.parseInput(await this.validateInput(request))
    } catch (err) {
      throw new InvalidInput(
        err instanceof ValidationError ? err.message : 'Invalid Request',
        this.useCase.name,
        err,
      )
    }

    const data = await this.useCase.execute(input)
    return { status: this.successStatus, data }
  }
}

export { Controller }
export type { RequestInput }
