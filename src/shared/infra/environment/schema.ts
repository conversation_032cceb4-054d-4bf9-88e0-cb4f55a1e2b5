import { InferType, object, string } from 'yup'

const schema = object({
  ENVIRONMENT: string().oneOf(['develop', 'test']).required(),
  DATABASE_URL: string().required(),
  DATABASE_TYPE: string().oneOf(['postgres', 'pglite']).default('postgres'),
  JWT_AUTHORIZATION_SERVICE_SECRET: string().required(),
  LOG_LEVEL: string().oneOf(['info', 'debug', 'warn', 'error']).default('info'),
})

type TEnvironmentSchema = InferType<typeof schema>

export { schema }
export type { TEnvironmentSchema }
