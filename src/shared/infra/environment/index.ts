import { ValidationError } from 'yup'
import { logger } from '../logger'
import { schema, TEnvironmentSchema } from './schema'

class Environment {
  static #instance: Environment
  #values: TEnvironmentSchema

  private constructor(input: Record<string, unknown>) {
    this.#values = Object.keys(schema.describe().fields).reduce(
      (prev, curr) => ({ ...prev, [curr]: '' }),
      {} as TEnvironmentSchema,
    )

    try {
      this.#values = schema.validateSync(input, {
        abortEarly: false,
        stripUnknown: true,
      })
    } catch (error) {
      if (error instanceof ValidationError) {
        for (const _error of error.errors) {
          logger.error(`Environment validation error: ${_error}`)
        }
        process.exit(1)
      }

      throw error
    }
  }

  static get instance(): Environment {
    if (!Environment.#instance) {
      Environment.#instance = new Environment(process.env)
    }

    return Environment.#instance
  }

  get<Key extends keyof TEnvironmentSchema>(key: Key): TEnvironmentSchema[Key] {
    return this.#values[key]
  }
}

export { Environment }
