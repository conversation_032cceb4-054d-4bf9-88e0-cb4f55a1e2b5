// src/middleware/authMiddleware.ts
import { Request, Response, NextFunction } from 'express'
import { User } from '../../../modules/users/domain/user'
import { userRepository } from '../../../modules/users/repositories'
import { usersServices } from '../../../modules/users/services'
import { UniqueEntityID } from '../../domain/uniqueEntityID'
import { AuthContext } from './authContext'

async function authMiddleware(req: Request, res: Response, next: NextFunction) {
  let user: User | undefined
  const header = req.headers.authorization

  if (header) {
    const authService = usersServices.get('authorization')
    // TODO check, try catch
    const token = await authService.verifyToken(header.split(' ')[1]!)
    const userId = new UniqueEntityID(token.sub)
    user = await userRepository.getById(userId)
  }

  AuthContext.run(user, () => {
    next()
  })
}

export { authMiddleware }
