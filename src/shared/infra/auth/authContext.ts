import { AsyncLocalStorage } from 'async_hooks'
import { User } from '../../../modules/users/domain/user'
import { Unauthorized } from '../../errors'

class AuthContext {
  private static readonly storage = new AsyncLocalStorage<User | undefined>()

  /**
   * Executes a callback function within a specific user context.
   *
   * @param user - The user to set in the context, or null to clear the context.
   * @param callback - The function to execute within the user context.
   */

  static run<T = void>(user: User | undefined, callback: () => T) {
    return this.storage.run(user, callback)
  }

  static getUser(): User {
    const user = this.storage.getStore()

    if (!user) {
      throw new Unauthorized()
    }

    return user
  }

  static isAuthenticated() {
    const user = this.getUser()
    return Boolean(user)
  }
}

export { AuthContext }
