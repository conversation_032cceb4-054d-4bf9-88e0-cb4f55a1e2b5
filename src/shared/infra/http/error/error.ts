import { NextFunction, Request, Response } from 'express'
import { AppError, RouteNotFound, Unexpected } from '../../../errors'
import { logger } from '../../logger'

export function notFoundHandler(req: Request) {
  throw new RouteNotFound(req.url)
}

export function errorHandler(
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction,
) {
  if (res.headersSent) {
    return next(err)
  }

  const error = err instanceof AppError ? err : new Unexpected(err)

  res.status(error.status).json({ error: error.message })
  logger[error.level](error.message)
}
