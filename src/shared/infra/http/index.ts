import http from 'http'
import { ListenOptions } from 'net'
import bodyParser from 'body-parser'
import cors from 'cors'
import express from 'express'
import helmet from 'helmet'
import <PERSON>wagger<PERSON> from 'swagger-ui-express'
import APISpec from '../../../../spec/dist/openapi.v1.json'
import { authMiddleware } from '../auth/middleware'
import { Configuration } from '../configuration'
import { Environment } from '../environment'
import { logger } from '../logger'
import { errorHandler, notFoundHandler } from './error/error'
import { v1Router } from './routes/v1'

let server: http.Server

function getConfig() {
  return Configuration.instance.get('http')
}

function getEnvironment() {
  return Environment.instance.get('ENVIRONMENT')
}

function initSwaggerUI(app: express.Application) {
  const environment = getEnvironment()

  if (environment === 'develop') {
    app.use('/api-docs', SwaggerUI.serve, SwaggerUI.setup(APISpec))
  }
}

function initMiddlewares(app: express.Application) {
  const { cors: corsConfig } = getConfig()

  app.use(helmet())
  app.use(cors(corsConfig))
  app.use(bodyParser.json({ limit: '2mb' }))
  app.use(authMiddleware)
}

function initRoutes(app: express.Application) {
  app.use('/v1', v1Router)
}

function initErrorHandler(app: express.Application) {
  app.use(notFoundHandler)
  app.use(errorHandler)
  app.all(/(.*)/, notFoundHandler)
}

export function initApp(): express.Application {
  const app = express()

  initMiddlewares(app)
  initRoutes(app)
  initSwaggerUI(app)
  initErrorHandler(app)

  return app
}

function startHTTPServer() {
  const app = initApp()
  server = http.createServer(app)
  const { port, hostname } = getConfig()

  const serverOptions: ListenOptions = {
    host: hostname,
    port: port,
  }

  server.listen(serverOptions, () => {
    logger.info(`HTTP server listening on http://${hostname}:${port}`)
  })
}

async function stopHTTPServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (server.listening) {
      server.close((error) => {
        if (error) reject(error)

        logger.info(`HTTP server closed`)
        resolve()
      })
    }

    resolve()
  })
}

export { startHTTPServer, stopHTTPServer }
