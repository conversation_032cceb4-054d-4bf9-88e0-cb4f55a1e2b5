import { Router } from 'express'
import { notificationsRoute } from '../../../../modules/activities/infra/http/routes'
import {
  collectionsRoute,
  fieldGroupsRoute,
  fieldsRoute,
  servicesRoute,
} from '../../../../modules/collections/infra/http/routes'
import {
  changesRoute,
  entriesRoute,
} from '../../../../modules/entries/infra/routes'
import {
  invitesRoute,
  usersRoute,
} from '../../../../modules/users/infra/routes'
import { healthRoute } from '../probes/health/route'
import { Route } from './route'

const routes: Route[] = [
  healthRoute,
  notificationsRoute,
  usersRoute,
  invitesRoute,
  collectionsRoute,
  servicesRoute,
  fieldsRoute,
  fieldGroupsRoute,
  entriesRoute,
  changesRoute,
]

const v1Router: Router = Router()
for (const { route, router } of routes) {
  v1Router.use(route, router)
}

export { v1Router }
