import { Request, Router } from 'express'

type Method = 'get' | 'post' | 'patch' | 'delete'

interface Result<T> {
  status: number
  data: T
}

type HandlerInput = {
  params: Request['params']
  body: Request['body']
  query: Request['query']
}

interface Handler {
  method: Method
  route: string
  handle: (input: HandlerInput) => Promise<Result<unknown>>
}

class Route {
  public readonly route: string
  public readonly router: Router = Router()

  constructor(route: string, ...handlers: Handler[]) {
    this.route = `/${route}`
    for (const handler of handlers) {
      this.router[handler.method](
        `/${handler.route}`,
        async ({ params, body, query }, response) => {
          const { status, data } = await handler.handle({ params, body, query })
          response.status(status).send(data)
        },
      )
    }
  }
}

export { Route }
export type { Method, Result, Handler, HandlerInput }
