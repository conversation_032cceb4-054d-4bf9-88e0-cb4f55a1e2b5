import { DeepPartial, Paths } from 'ts-essentials'
import { InferType, number, object, string } from 'yup'
import { TimeUnit } from '../../types/time'

const schema = object({
  http: object({
    port: number()
      .transform((value) => Number(value))
      .required(),
    hostname: string().required(),
    cors: object({
      origin: string(),
    }).optional(),
  }),
  services: object({
    authorization: object({
      enabled: string().oneOf(['jwt']).required(),
      configuration: object({
        jwt: object({
          // Ensures the value is compatible with JOSE's time format
          expiration: string<`${number}${TimeUnit}`>().required(),
        }),
      }),
    }),
  }),
  application: object({
    pagination: object({
      defaultPageSize: number().required().positive().integer(),
      maxPageSize: number().required().positive().integer(),
    }),
  }),
})

type TConfigurationSchema = InferType<typeof schema>
type TLenientConfigurationSchema = DeepPartial<TConfigurationSchema>
type TConfigurationPaths = Paths<TConfigurationSchema>

export { schema }
export type {
  TConfigurationSchema,
  TLenientConfigurationSchema,
  TConfigurationPaths,
}
