import { merge } from 'lodash'
import { PathValue } from 'ts-essentials'
import { ValidationError } from 'yup'
import { Environment } from '../environment'
import { TEnvironmentSchema } from '../environment/schema'
import { logger } from '../logger'
import { InvalidConfigurationPath } from './error'
import {
  schema,
  TConfigurationPaths,
  TConfigurationSchema,
  TLenientConfigurationSchema,
} from './schema'
import defaultConfiguration from './values/default'
import develop from './values/develop'

class Configuration {
  static #instance: Configuration
  readonly #values: TConfigurationSchema

  private constructor(configuration: TConfigurationSchema) {
    this.#values = Object.keys(schema.describe().fields).reduce(
      (prev, curr) => ({ ...prev, [curr]: '' }),
      {} as TConfigurationSchema,
    )

    try {
      this.#values = schema.validateSync(configuration, {
        abortEarly: false,
        stripUnknown: true,
      })
    } catch (error) {
      if (error instanceof ValidationError) {
        for (const _error of error.errors) {
          logger.error(`Configuration validation error: ${_error}`)
        }
        return
      }

      throw error
    }
  }

  static get instance(): Configuration {
    if (!Configuration.#instance) {
      const configurationMapping: Record<
        TEnvironmentSchema['ENVIRONMENT'],
        TLenientConfigurationSchema
      > = {
        develop,
        test: {},
      }

      // Build the configuration object by merging the default configuration with the environment-specific configuration
      const environment = Environment.instance.get('ENVIRONMENT')
      const configuration = merge(
        {},
        defaultConfiguration,
        configurationMapping[environment],
      )

      Configuration.#instance = new Configuration(configuration)
    }

    return Configuration.#instance
  }

  get<Path extends TConfigurationPaths>(
    path: Path,
  ): PathValue<TConfigurationSchema, Path> {
    return path.split('.').reduce((obj, key) => {
      if (obj && typeof obj === 'object' && key in obj) {
        return obj[key as keyof typeof obj]
      }

      throw new InvalidConfigurationPath()
    }, this.#values as unknown) as PathValue<TConfigurationSchema, Path>
  }
}

export { Configuration }
