import { TConfigurationSchema } from '../schema'

export default {
  http: {
    port: 3000,
    hostname: '0.0.0.0',
  },
  services: {
    authorization: {
      enabled: 'jwt',
      configuration: {
        jwt: {
          expiration: '7D',
        },
      },
    },
  },
  application: {
    pagination: {
      defaultPageSize: 10,
      maxPageSize: 100,
    },
  },
} satisfies TConfigurationSchema
