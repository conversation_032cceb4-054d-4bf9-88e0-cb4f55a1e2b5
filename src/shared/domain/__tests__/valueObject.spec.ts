import { ValueObject } from '../valueObject'

class StubValueObject extends ValueObject<string> {
  constructor(props: string) {
    super(props)
  }
}

class AnotherStubValueObject extends ValueObject<string> {
  constructor(props: string) {
    super(props)
  }
}

describe('ValueObject', () => {
  it('should return true if two ValueObject are equal', () => {
    const a = new StubValueObject('test')
    const b = new StubValueObject('test')

    expect(a.equals(b)).toBe(true)
  })

  it('should return false if two entities are unequal', () => {
    const a = new StubValueObject('test')
    const b = new StubValueObject('test2')

    expect(a.equals(b)).toBe(false)
    expect(a.equals(undefined)).toBe(false)
  })

  it('should return true if two objects have different types but the same props', () => {
    const a = new StubValueObject('test')
    const b = new AnotherStubValueObject('test')

    expect(a.equals(b)).toBe(true)
  })
})
