import { stubLink } from '../__stubs__/link.stub'

describe('Link', () => {
  it('should create a new Link with a valid URL', () => {
    const link = stubLink('https://acme.tld')

    expect(link.value).toStrictEqual(new URL('https://acme.tld'))
  })

  it('should throw an error if the URL is invalid', () => {
    expect(() => stubLink('invalid-url')).toThrow()
    expect(() => stubLink('')).toThrow()
    expect(() => stubLink('https://acme.tld/ invalid-path')).toThrow()
    expect(() => stubLink('https:// acme.tld/')).toThrow()
    expect(() => stubLink('https://?acme.tld')).toThrow()
  })

  it('should create a new Link with a URL instance', () => {
    const url = new URL('https://acme.tld')
    const link = stubLink(url)

    expect(link.value).toStrictEqual(url)
  })

  it('should not throw an error if the URL is valid', () => {
    expect(() => stubLink('https://acme.tld')).not.toThrow()
    expect(() => stubLink('https://acme.tld/path?a=1&b=2')).not.toThrow()
    expect(() => stubLink('https://acme.tld/path#fragment')).not.toThrow()
    expect(() => stubLink('https://subdomainacme.tld/')).not.toThrow()
    expect(() => stubLink('ftp://subdomainacme.tld/')).not.toThrow()
  })

  it('should cleanup the URL by trimming whitespace', () => {
    const link = stubLink('  https://acme.tld  ')

    expect(link.value).toStrictEqual(new URL('https://acme.tld'))
  })

  it('should be case insensitive when creating a new Link', () => {
    const link = stubLink('https://ACME.tld')

    expect(link.value).toStrictEqual(new URL('https://acme.tld'))
  })
})
