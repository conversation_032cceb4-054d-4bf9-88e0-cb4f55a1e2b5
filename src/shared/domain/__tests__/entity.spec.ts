import { AlreadyDeleted } from '../../errors/domainErrors'
import { Entity } from '../entity'
import { UniqueEntityID } from '../uniqueEntityID'

class StubEntity extends Entity<{ value: string }> {
  constructor(
    props: string,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, { value: props }, createdAt, updatedAt, deleted)
  }

  get value() {
    return this.getProp('value')
  }
}

describe('Entity', () => {
  it('should return true if two Entity are equal', () => {
    const entityA = new StubEntity('test')
    const entityB = new StubEntity('test', entityA.id)

    expect(entityA.equals(entityA)).toBe(true)
    expect(entityA.equals(entityB)).toBe(true)
  })

  it('should return false if two Entity are unequal', () => {
    const entityA = new StubEntity('test')
    const entityB = new StubEntity('test')

    expect(entityA.equals(entityB)).toBe(false)
    expect(entityA.equals(undefined)).toBe(false)
  })

  it('should set updatedAt to undefined if not provided', () => {
    const entityA = new StubEntity('test')
    expect(entityA.updatedAt).toBeUndefined()

    const createdAt = new Date()
    // set updatedAt to createdAt + 5 days
    const updatedAt = new Date(createdAt)
    updatedAt.setDate(updatedAt.getDate() + 5)

    const entityB = new StubEntity('test', undefined, createdAt, updatedAt)

    expect(entityB.updatedAt).toBe(updatedAt)
  })

  it('should not be able to call `delete` if App is deleted', () => {
    const entity = new StubEntity('test', undefined, undefined, undefined, true)
    expect(() => entity.delete()).toThrow(AlreadyDeleted)
  })

  it('should update entity properties and updatedAt timestamp', () => {
    const entity = new StubEntity('test')
    const initialUpdatedAt = entity.updatedAt

    expect(entity.value).toBe('test')
    expect(initialUpdatedAt).toBeUndefined()

    entity.update({ value: 'updated' })

    expect(entity.value).toBe('updated')
    expect(initialUpdatedAt).not.toBe(entity.updatedAt)
  })
})
