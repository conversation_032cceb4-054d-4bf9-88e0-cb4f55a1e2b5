import { stubEmail } from '../__stubs__/email.stub'

describe('Email', () => {
  it('should create a Email', () => {
    const email = stubEmail({ value: '<EMAIL>' })

    expect(email.value).toBe('<EMAIL>')
  })

  it('should normalize the email', () => {
    const email = stubEmail({ value: '  <EMAIL>  ' })
    expect(email.value).toBe('<EMAIL>')
  })

  it('should throw an error if the email is invalid', () => {
    expect(() => stubEmail({ value: '@acme.tld' })).toThrow()
    expect(() => stubEmail({ value: 'acme.tld' })).toThrow()
    expect(() => stubEmail({ value: 'email@<EMAIL>' })).toThrow()
    // expect(() => stubEmail({ value: '.<EMAIL>' })).toThrow() // This is a valid email for yup
    // expect(() => stubEmail({ value: '<EMAIL>' })).toThrow() // This is a valid email for yup
    expect(() => stubEmail({ value: '<EMAIL>' })).toThrow()
  })
})
