import { stubWatchedList } from '../__stubs__/watchedList.stub'

describe('WatchedList', () => {
  describe('initialization', () => {
    it('should initialize with empty arrays when no items provided', () => {
      const watchedList = stubWatchedList()

      expect(watchedList.getItems()).toEqual([])
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual([])
    })

    it('should initialize with provided items', () => {
      const initialItems = ['item1', 'item2']
      const watchedList = stubWatchedList(initialItems)

      expect(watchedList.getItems()).toEqual(initialItems)
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual([])
    })
  })

  describe('add', () => {
    it('should add an item to the current and new items lists', () => {
      const watchedList = stubWatchedList()
      watchedList.add('item1')

      expect(watchedList.getItems()).toEqual(['item1'])
      expect(watchedList.getNewItems()).toEqual(['item1'])
      expect(watchedList.getRemovedItems()).toEqual([])
    })

    it('should not duplicate items when adding the same item twice', () => {
      const watchedList = stubWatchedList()
      watchedList.add('item1')
      watchedList.add('item1')

      expect(watchedList.getItems()).toEqual(['item1'])
      expect(watchedList.getNewItems()).toEqual(['item1'])
      expect(watchedList.getRemovedItems()).toEqual([])
    })

    it('should move an item from removed to current if it was previously removed', () => {
      const watchedList = stubWatchedList(['item1'])
      watchedList.remove('item1')
      watchedList.add('item1')

      expect(watchedList.getItems()).toEqual(['item1'])
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual([])
    })
  })

  describe('remove', () => {
    it('should remove an item from current items and add to removed items', () => {
      const watchedList = stubWatchedList(['item1'])
      watchedList.remove('item1')

      expect(watchedList.getItems()).toEqual([])
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual(['item1'])
    })

    it('should remove an item from current and new items if it was newly added', () => {
      const watchedList = stubWatchedList()
      watchedList.add('item1')
      watchedList.remove('item1')

      expect(watchedList.getItems()).toEqual([])
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual([])
    })

    it('should add to removed list even if item is not in current items', () => {
      const watchedList = stubWatchedList()
      watchedList.remove('nonexistent')

      expect(watchedList.getItems()).toEqual([])
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual(['nonexistent'])
    })
  })

  describe('exists', () => {
    it('should return true if item exists in current items', () => {
      const watchedList = stubWatchedList(['item1'])

      expect(watchedList.exists('item1')).toBe(true)
      expect(watchedList.exists('item2')).toBe(false)
    })
  })

  describe('update', () => {
    it('should update the list with new items', () => {
      const watchedList = stubWatchedList(['item1', 'item2'])
      watchedList.update(['item2', 'item3'])

      expect(watchedList.getItems()).toEqual(['item2', 'item3'])
      expect(watchedList.getNewItems()).toEqual(['item3'])
      expect(watchedList.getRemovedItems()).toEqual(['item1'])
    })

    it('should handle empty array updates', () => {
      const watchedList = stubWatchedList(['item1', 'item2'])
      watchedList.update([])

      expect(watchedList.getItems()).toEqual([])
      expect(watchedList.getNewItems()).toEqual([])
      expect(watchedList.getRemovedItems()).toEqual(['item1', 'item2'])
    })

    it('should correctly identify all items as new when updating an empty list', () => {
      const watchedList = stubWatchedList()
      watchedList.update(['item1', 'item2'])

      expect(watchedList.getItems()).toEqual(['item1', 'item2'])
      expect(watchedList.getNewItems()).toEqual(['item1', 'item2'])
      expect(watchedList.getRemovedItems()).toEqual([])
    })
  })
})
