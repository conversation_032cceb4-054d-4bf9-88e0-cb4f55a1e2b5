import { InvalidValue } from '../../errors/domainErrors'
import { UniqueEntityID } from '../uniqueEntityID'

describe('UniqueEntityID', () => {
  it("should create a new UniqueEntityID if a value isn't passed", () => {
    const id = new UniqueEntityID()
    expect(id).toBeInstanceOf(UniqueEntityID)
  })

  it('should create a new UniqueEntityID if a value is passed', () => {
    const id = new UniqueEntityID('279986a8-5595-4306-9f08-5190b9ba156e')
    expect(id).toBeInstanceOf(UniqueEntityID)
    expect(id.value).toBe('279986a8-5595-4306-9f08-5190b9ba156e')
  })

  it('should throw an error if the value is not a valid UUID', () => {
    expect(() => new UniqueEntityID('invalid-uuid')).toThrow(InvalidValue)
  })

  it('should return true if two UniqueEntityID are equal', () => {
    const a = new UniqueEntityID('279986a8-5595-4306-9f08-5190b9ba156e')
    const b = new UniqueEntityID('279986a8-5595-4306-9f08-5190b9ba156e')

    expect(a.equals(b)).toBe(true)
  })

  it('should return false if two UniqueEntityID are unequal', () => {
    const a = new UniqueEntityID()
    const b = new UniqueEntityID()

    expect(a.equals(b)).toBe(false)
    expect(a.equals(undefined)).toBe(false)
  })
})
