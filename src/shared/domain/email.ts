import { string } from 'yup'
import { InvalidValue } from '../errors/domainErrors'
import { ValueObject, ValueObjectProps } from './valueObject'

class Email extends ValueObject {
  constructor(props: ValueObjectProps) {
    const normalizedValue = Email.normalize(props.value)

    Email.validate(normalizedValue)

    super({ value: Email.normalize(normalizedValue) })
  }

  private static normalize(email: string): string {
    return email.trim().toLowerCase()
  }

  private static validate(email: string): void {
    const schema = string().email().required()
    try {
      schema.validateSync(email)
    } catch {
      throw new InvalidValue(Email.name, 'email', email)
    }
  }

  get value(): string {
    return this.props.value
  }
}

export { Email }
