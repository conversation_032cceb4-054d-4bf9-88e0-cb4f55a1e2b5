import { string } from 'yup'
import { InvalidValue } from '../errors/domainErrors'

const ID_SCHEMA = string().uuid().required()

class UniqueEntityID {
  public readonly value: string

  constructor(id?: string) {
    if (!id) this.value = crypto.randomUUID()
    else {
      try {
        this.value = ID_SCHEMA.validateSync(id)
      } catch {
        throw new InvalidValue(UniqueEntityID.name, 'id', id)
      }
    }
  }

  equals(id?: UniqueEntityID) {
    if (!id) return false
    return this.value === id.value
  }
}

export { UniqueEntityID }
