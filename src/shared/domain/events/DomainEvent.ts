import { UniqueEntityID } from '../uniqueEntityID'
import { IDomainEvent } from './interfaces/IDomainEvent'

/**
 * Base class for domain events, providing common functionality.
 */
export abstract class DomainEvent implements IDomainEvent {
  public readonly dateTimeOccurred: Date
  public readonly eventName: string

  constructor(eventName: string) {
    this.dateTimeOccurred = new Date()
    this.eventName = eventName
  }

  public abstract getEntityId(): UniqueEntityID
}
