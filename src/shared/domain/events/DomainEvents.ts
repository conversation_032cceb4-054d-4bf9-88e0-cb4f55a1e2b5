/* eslint-disable @typescript-eslint/no-explicit-any */
import { logger } from '../../infra/logger'
import { UniqueEntityID } from '../uniqueEntityID'
import { IDomainEvent } from './interfaces/IDomainEvent'
import { IDomainEventHandler } from './interfaces/IDomainEventHandler'

export type EventHandlerCallback<TOutput = void> = (
  event: IDomainEvent,
) => Promise<TOutput>

/**
 * Manages the registration and dispatching of domain events.
 * This is an in-process, synchronous dispatcher.
 */
export class DomainEvents {
  private handlerExecutionWarningThresholdMs = 1000 // Default to 1000ms
  private handlersMap = new Map<string, EventHandlerCallback<unknown>[]>()
  private markedEntities = new Map<string, IDomainEvent[]>()

  constructor() {
    logger.info('DomainEvents service instance created.')
  }

  /**
   * Sets the warning threshold for slow event handler execution.
   * @param {number} thresholdMs The threshold in milliseconds.
   */
  public setHandlerExecutionWarningThreshold(thresholdMs: number): void {
    if (thresholdMs > 0) {
      this.handlerExecutionWarningThresholdMs = thresholdMs
      logger.info(
        `Handler execution warning threshold set to ${thresholdMs}ms.`,
      )
    } else {
      logger.warn(
        `Invalid handler execution warning threshold: ${thresholdMs}ms. Must be > 0.`,
      )
    }
  }

  /**
   * Registers a handler for a specific domain event type.
   * It's recommended to use named functions for callbacks to improve logging and debugging.
   * @param callback The function to execute when the event is dispatched.
   * @param eventClass The class constructor of the domain event to subscribe to.
   */
  public register(
    callback: EventHandlerCallback<unknown>,
    eventClass: { new (...args: any[]): IDomainEvent; eventIdentifier: string },
  ): void {
    const eventClassName = eventClass.eventIdentifier
    if (!this.handlersMap.has(eventClassName)) {
      this.handlersMap.set(eventClassName, [])
    }
    this.handlersMap.get(eventClassName)!.push(callback)
  }

  /**
   * Marks an entity as having domain events to be dispatched.
   * This is typically called from an Entity's `addDomainEvent` method.
   * @param event The domain event to mark.
   */
  public markEntityForDispatch(event: IDomainEvent): void {
    const entityIdStr = event.getEntityId().value
    if (!this.markedEntities.has(entityIdStr)) {
      this.markedEntities.set(entityIdStr, [])
    }
    this.markedEntities.get(entityIdStr)!.push(event)
    logger.debug(
      `Entity ID: ${entityIdStr} marked for dispatch with event: ${event.eventName}`,
      { entityId: entityIdStr, eventName: event.eventName },
    )
  }

  /**
   * Dispatches all events for a given entity ID.
   * This should be called after an entity's state has been successfully persisted.
   * @param id The ID of the entity whose events are to be dispatched.
   */
  public async dispatchEventsForEntity(id: UniqueEntityID): Promise<void> {
    const entityIdStr = id.value
    const events = this.markedEntities.get(entityIdStr)

    if (events?.length) {
      logger.info(
        `Attempting to dispatch events for entity ID: ${entityIdStr}`,
        {
          entityId: entityIdStr,
          eventCount: events.length,
        },
      )
      try {
        for (const event of events) {
          await this.dispatch(event)
        }
      } catch (error) {
        logger.error(
          `A critical error occurred during the event dispatch loop for entity ID: ${entityIdStr}.`,
          {
            entityId: entityIdStr,
            error:
              error instanceof Error
                ? {
                    message: error.message,
                    name: error.name,
                    stack: error.stack,
                  }
                : { detail: String(error) },
          },
        )
      } finally {
        this.markedEntities.delete(entityIdStr)
        logger.info(
          `Finished dispatching for entity ID: ${entityIdStr} and cleared marked events.`,
          { entityId: entityIdStr, eventsDispatchedCount: events.length },
        )
      }
    } else {
      logger.info(`No events found to dispatch for entity ID: ${entityIdStr}`, {
        entityId: entityIdStr,
      })
    }
  }

  /**
   * Clears all registered handlers. Useful for testing or application shutdown.
   */
  public clearHandlers(): void {
    this.handlersMap.clear()
  }

  /**
   * Clears all marked entities. Useful for testing.
   */
  public clearMarkedEntities(): void {
    this.markedEntities.clear()
  }

  /**
   * Dispatches a single domain event to all registered handlers for its type.
   * @param event The domain event to dispatch.
   */
  private async dispatch(event: IDomainEvent): Promise<void> {
    const eventClassName = event.eventName
    const entityIdStr = event.getEntityId().value

    if (this.handlersMap.has(eventClassName)) {
      const handlers = this.handlersMap.get(eventClassName)
      logger.info(
        `Dispatching event: '${eventClassName}' for entity ID: ${entityIdStr}`,
        { eventName: eventClassName, entityId: entityIdStr },
      )
      for (const handler of handlers!) {
        const handlerName = handler.name || 'AnonymousHandler'
        const startTime = performance.now()
        try {
          await handler(event)
          const executionTime = performance.now() - startTime
          const executionTimeMs = Math.round(executionTime * 100) / 100
          logger.info(
            `Handler '${handlerName}' for event '${eventClassName}' (Entity ID: ${entityIdStr}) executed in ${executionTimeMs}ms`,
            {
              eventName: eventClassName,
              entityId: entityIdStr,
              handlerName,
              executionTimeMs,
            },
          )

          if (executionTime > this.handlerExecutionWarningThresholdMs) {
            logger.warn(
              `SLOW HANDLER: Handler '${handlerName}' for event '${eventClassName}' (Entity ID: ${entityIdStr}) took ${executionTimeMs}ms (threshold: ${this.handlerExecutionWarningThresholdMs}ms)`,
              {
                eventName: eventClassName,
                entityId: entityIdStr,
                handlerName,
                executionTimeMs,
                thresholdMs: this.handlerExecutionWarningThresholdMs,
              },
            )
          }
        } catch (err) {
          const executionTime = performance.now() - startTime
          const executionTimeMs = Math.round(executionTime * 100) / 100
          logger.error(
            `Error in handler '${handlerName}' for event '${eventClassName}' (Entity ID: ${entityIdStr}) after ${executionTimeMs}ms`,
            {
              eventName: eventClassName,
              entityId: entityIdStr,
              handlerName,
              executionTimeMs,
              error:
                err instanceof Error
                  ? {
                      message: err.message,
                      name: err.name,
                      stack: err.stack,
                    }
                  : {
                      detail: String(err),
                    },
            },
          )
        }
      }
    }
  }

  /**
   * Bootstraps the domain event system by registering all provided handlers.
   * This is typically called once during application initialization.
   * @param handlers An array of domain event handlers to register.
   */
  public bootstrap(handlers: IDomainEventHandler<IDomainEvent>[]): void {
    handlers.forEach((handler) => {
      handler.setupSubscriptions()
    })
  }
}
