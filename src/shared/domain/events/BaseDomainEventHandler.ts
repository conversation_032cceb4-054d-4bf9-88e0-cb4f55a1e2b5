/* eslint-disable @typescript-eslint/no-explicit-any */
import { UseCase } from '../../infra/useCase'
import { IDomainEvent } from './interfaces/IDomainEvent'
import { IDomainEventHandler } from './interfaces/IDomainEventHandler'
import { domainEvents } from './index'

/**
 * @class BaseDomainEventHandler
 * @desc An abstract base class for domain event handlers to reduce boilerplate.
 * It handles the subscription, maps the event to UseCase input, and executes the UseCase.
 */
export abstract class BaseDomainEventHandler<
  TEvent extends IDomainEvent,
  TUseCaseInput,
  TUseCaseOutput = void,
> implements IDomainEventHandler<TEvent, TUseCaseOutput>
{
  protected readonly useCase: UseCase<TUseCaseInput, TUseCaseOutput>
  protected readonly eventConstructor: new (...args: any[]) => TEvent
  protected readonly eventIdentifier: string

  protected constructor(
    useCase: UseCase<TUseCaseInput, TUseCaseOutput>,
    eventConstructor: new (...args: any[]) => TEvent,
    eventIdentifier: string,
  ) {
    this.useCase = useCase
    this.eventConstructor = eventConstructor
    this.eventIdentifier = eventIdentifier
  }

  /**
   * Maps the incoming domain event to the input DTO required by the UseCase.
   * Must be implemented by the subclass.
   * @param {TEvent} event - The domain event instance.
   * @returns {Promise<TUseCaseInput> | TUseCaseInput} The input for the UseCase.
   */
  protected abstract parseInput(
    event: TEvent,
  ): Promise<TUseCaseInput> | TUseCaseInput

  /**
   * Registers the handler with the central dispatcher.
   * This is called by the bootstrap process.
   */
  public setupSubscriptions(): void {
    // Create proper event class descriptor without mutating the constructor
    const eventClassDescriptor = Object.assign(this.eventConstructor, {
      eventIdentifier: this.eventIdentifier,
    }) as {
      new (...args: any[]): IDomainEvent
      eventIdentifier: string
    }

    const boundHandle = this.handle.bind(this)
    Object.defineProperty(boundHandle, 'name', {
      value: this.constructor.name,
      writable: false,
      configurable: true,
    })

    domainEvents.register(boundHandle, eventClassDescriptor)
  }

  /**
   * The generic handle method that receives the event from the dispatcher.
   * It maps the event to UseCase input and then executes the UseCase.
   * @param {IDomainEvent} event - The generic domain event.
   */
  public async handle(event: IDomainEvent): Promise<TUseCaseOutput> {
    const useCaseInput = await this.parseInput(event as TEvent)
    return this.useCase.execute(useCaseInput)
  }
}
