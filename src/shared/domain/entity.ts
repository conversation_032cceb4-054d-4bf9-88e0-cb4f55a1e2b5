import { AlreadyDeleted } from '../errors/domainErrors'
import { domainEvents, IDomainEvent } from './events'
import { UniqueEntityID } from './uniqueEntityID'

// TODO adicionar a lógica repetida do yup para não adicionar em todas as entities
export abstract class Entity<Props> {
  private _domainEvents: IDomainEvent[] = []
  constructor(
    public readonly id: UniqueEntityID = new UniqueEntityID(),
    private readonly props: Props,
    public readonly createdAt: Date = new Date(),
    protected _updatedAt?: Date,
    private _deleted: boolean = false,
  ) {}

  get updatedAt(): Date | undefined {
    return this._updatedAt
  }

  get deleted(): boolean {
    return this._deleted
  }

  public delete(): void {
    if (this.deleted) throw new AlreadyDeleted(this.constructor.name)
    this._deleted = true
  }

  public equals(entity?: Entity<Props>) {
    if (!entity) return false
    return this === entity || this.id.equals(entity.id)
  }

  protected getProp<K extends keyof Props>(key: K): Props[K] {
    return this.props[key]
  }

  // TODO provavelmente um check genérico do yup
  protected setProp<K extends keyof Props>(key: K, value: Props[K]): void {
    this.props[key] = value
  }

  public update(props: Partial<Props>) {
    this.validate?.(props)

    for (const key in props) {
      if (props[key] !== undefined) {
        this.setProp(key as keyof Props, props[key] as Props[keyof Props])
      }
    }

    this._updatedAt = new Date()
  }

  protected validate?(props: Partial<Props>): void

  public getDomainEvents(): IDomainEvent[] {
    return this._domainEvents
  }

  public addDomainEvent(domainEvent: IDomainEvent): void {
    this._domainEvents.push(domainEvent)
    domainEvents.markEntityForDispatch(domainEvent) // Mark this entity's events for dispatch
  }

  public clearDomainEvents(): void {
    this._domainEvents = []
  }
}
