import { string } from 'yup'
import { InvalidValue } from '../errors/domainErrors'
import { ValueObject, ValueObjectProps } from './valueObject'

class Link extends ValueObject<ValueObjectProps<URL>> {
  constructor(url: string | URL) {
    if (typeof url === 'string') {
      const cleanURL = Link.cleanup(url)

      Link.validate(cleanURL)
      super({ value: new URL(Link.cleanup(cleanURL)) })
      return
    }

    // If the URL is already an instance of URL, we don't need to validate it
    super({ value: url })
  }

  private static cleanup(urlString: string): string {
    return urlString.trim().toLowerCase()
  }

  private static validate(urlString: string): void {
    const schema = string().defined().url()

    try {
      schema.validateSync(urlString)
    } catch {
      throw new InvalidValue(Link.name, 'url', urlString)
    }
  }

  get value(): URL {
    return this.props.value
  }
}

export { Link }
