import { eq, and, sql, SQL, getTableName, asc, desc } from 'drizzle-orm'
import {
  PgTable,
  PgColumn,
  PgUpdateSetSource,
  PgInsertValue,
} from 'drizzle-orm/pg-core'
import { DatabaseClient } from '../../database'
import { Entity } from '../../domain/entity'
import { domainEvents } from '../../domain/events'
import { UniqueEntityID } from '../../domain/uniqueEntityID'
import {
  EntityNotFound,
  RepositoryError,
  CannotModifyDeletedEntity,
  UniqueConstraintViolation,
} from '../../errors/repositoryErrors'
import { Configuration } from '../../infra/configuration'
import { Mapper } from '../../mappers'
import { GenericModel } from '../../models/generic.model'
import {
  BaseFindQuery,
  GenericRepository,
  SoftDeleteFilter,
  FindPageParams,
} from '../genericRepository'

type RelationOptions = SoftDeleteFilter & {
  with?: Record<string, boolean | RelationOptions>
}

type ExtendedFindQuery = BaseFindQuery & {
  customWhere?: SQL
  with?: Record<string, boolean | RelationOptions>
}

type QueryOptions = {
  where?: SQL
  limit?: number
  offset?: number
  orderBy?: SQL[]
  with?: Record<string, unknown>
}

/**
 * An abstract generic repository implementation using Drizzle ORM for PostgreSQL.
 * Provides common CRUD operations and query building capabilities.
 *
 * Subclasses should provide specific Drizzle table schema, domain entity, model, and mapper.
 *
 * Key extensibility points:
 * - Override protected column name properties (`idColumnName`, `deletedColumnName`, `createdAtColumnName`)
 *   in subclasses if the database schema uses different names for these common columns.
 * - Override `processRelatedEntities` to handle specific relational data persistence logic.
 *
 * @template TTable - The Drizzle ORM table schema type (extends PgTable).
 * @template TDomain - The domain entity type (extends Entity<unknown>).
 * @template TModel - The database model type (extends GenericModel).
 * @template TDto - The Data Transfer Object type (defaults to never).
 * @template TToDTOOptions - Options for converting domain entity to DTO (defaults to never).
 */
abstract class AbstractDrizzleRepository<
  TTable extends PgTable,
  TDomain extends Entity<unknown>,
  TModel extends GenericModel,
  TDto = never,
  TToDTOOptions = never,
> implements GenericRepository<TDomain>
{
  private static readonly DELETED_CHECK = (
    table: GenericModel,
    { eq }: { eq: (a: boolean, b: boolean) => boolean },
  ) => eq(table.deleted, false)

  protected readonly entityName: string
  protected readonly db: DatabaseClient
  protected readonly mapper: Mapper<TDomain, TModel, TDto, TToDTOOptions>
  protected readonly table: TTable
  protected readonly tableName: string
  protected readonly tableVariableName: string
  protected readonly idColumnName: string = 'id'
  protected readonly deletedColumnName: string = 'deleted'
  protected readonly createdAtColumnName: string = 'created_at'

  constructor(
    entityName: string,
    db: DatabaseClient,
    mapper: Mapper<TDomain, TModel, TDto, TToDTOOptions>,
    table: TTable,
    tableVariableName: string,
  ) {
    this.entityName = entityName
    this.db = db
    this.mapper = mapper
    this.table = table
    this.tableName = getTableName(this.table)
    this.tableVariableName = tableVariableName
  }

  // PRIVATE METHODS

  /**
   * Saves a single domain entity by either creating it if it doesn't exist,
   * or updating it if it does. This implements an application-level upsert behavior.
   *
   * @param entity - The domain entity to save.
   * @param dbClient - The database client to use for the operation.
   */
  private async _saveSingleEntity(
    entity: TDomain,
    dbClient: DatabaseClient,
  ): Promise<void> {
    const exists = await this.entityExists(entity.id, dbClient, true)
    if (exists) {
      await this.updateEntity(entity, dbClient)
    } else {
      await this.createEntity(entity, dbClient)
    }
  }

  private combineWhereConditions(whereConditions: SQL[]): SQL | undefined {
    return whereConditions.length > 0
      ? whereConditions.length > 1
        ? and(...whereConditions)
        : whereConditions[0]
      : undefined
  }

  private buildWhereConditions(
    conditions: SQL[] = [],
    includeDeleted = false,
  ): SQL | undefined {
    if (!includeDeleted) {
      conditions.push(eq(this.getDeletedColumn(), false))
    }
    return this.combineWhereConditions(conditions)
  }

  private buildIdWhereCondition(
    id: UniqueEntityID,
    includeDeleted = false,
  ): SQL | undefined {
    const idCondition = eq(this.getIdColumn(), id.value)
    return this.buildWhereConditions([idCondition], includeDeleted)
  }

  private async entityExists(
    id: UniqueEntityID,
    dbClient: DatabaseClient = this.db,
    includeDeleted = false,
  ): Promise<boolean> {
    const whereCondition = this.buildIdWhereCondition(id, includeDeleted)

    if (!whereCondition) {
      return false
    }

    const result = await dbClient
      .select({ count: sql<number>`count(*)::int` })
      .from(this.table as PgTable)
      .where(whereCondition)

    return result && result[0] ? result[0].count > 0 : false
  }

  // PUBLIC METHODS

  async exists(
    id: UniqueEntityID,
    options?: SoftDeleteFilter,
  ): Promise<boolean> {
    try {
      return await this.entityExists(id, this.db, options?.includeDeleted)
    } catch (error) {
      this.handleError(
        error,
        `checking existence for entity with ID ${id.value}`,
      )
    }
  }

  async getById(
    id: UniqueEntityID,
    options?: RelationOptions,
  ): Promise<TDomain> {
    try {
      const whereConditions = [eq(this.getIdColumn(), id.value)]
      const queryOptions = this.buildQueryOptions(options, whereConditions)
      // Type assertion to allow dynamic table access via tableVariableName
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const queryClient = this.db.query as any
      const result =
        await queryClient[this.tableVariableName].findFirst(queryOptions)

      if (!result) {
        throw new EntityNotFound(this.entityName, id.value)
      }

      return this.mapper.toDomain(result)
    } catch (error) {
      this.handleError(error, `getting entity by ID ${id.value}`)
    }
  }

  async save(entity: TDomain, dbClient?: DatabaseClient): Promise<void> {
    try {
      if (dbClient) {
        await this._saveSingleEntity(entity, dbClient)
        await this.processRelatedEntities(entity, dbClient)
      } else {
        await this.db.transaction(async (tx) => {
          await this._saveSingleEntity(entity, tx)
          await this.processRelatedEntities(entity, tx)
        })
      }
    } catch (error) {
      this.handleError(error, `saving entity with ID ${entity.id.value}`)
    }
  }

  async saveMany(
    entities: TDomain[],
    dbClient?: DatabaseClient,
  ): Promise<void> {
    try {
      if (dbClient) {
        for (const entity of entities) {
          await this._saveSingleEntity(entity, dbClient)
          await this.processRelatedEntities(entity, dbClient)
        }
      } else {
        await this.db.transaction(async (tx) => {
          for (const entity of entities) {
            await this._saveSingleEntity(entity, tx)
            await this.processRelatedEntities(entity, tx)
          }
        })
      }
    } catch (error) {
      const ids = entities.map((e) => e.id.value).join(', ')
      this.handleError(error, `saving multiple entities with IDs [${ids}]`)
    }
  }

  async delete(id: UniqueEntityID, dbClient?: DatabaseClient): Promise<void> {
    try {
      // Use the private helper for existence check
      const exists = await this.entityExists(id, dbClient, false)
      if (!exists) {
        throw new EntityNotFound(this.entityName, id.value)
      }
      const client = dbClient ?? this.db
      await client
        .update(this.table)
        .set({
          [this.deletedColumnName]: true,
          updatedAt: new Date(),
        } as PgUpdateSetSource<TTable>)
        .where(eq(this.getIdColumn(), id.value))
        .execute()
      await domainEvents.dispatchEventsForEntity(id)
    } catch (error) {
      this.handleError(error, `deleting entity with ID ${id.value}`)
    }
  }

  // PROTECTED METHODS

  protected async createEntity(
    entity: TDomain,
    dbClient: DatabaseClient,
  ): Promise<void> {
    try {
      const model = this.mapper.toModel(entity)
      await dbClient
        .insert(this.table)
        .values(model as PgInsertValue<TTable, false>)
        .execute()
      await domainEvents.dispatchEventsForEntity(entity.id)
    } catch (error) {
      this.handleError(error, `creating entity with ID ${entity.id.value}`)
    }
  }

  protected async updateEntity(
    entity: TDomain,
    dbClient: DatabaseClient,
  ): Promise<void> {
    try {
      // Check if the entity is already soft-deleted before attempting to update
      const existingEntityQuery = dbClient
        .select({
          id: this.getIdColumn(),
          deleted: this.getDeletedColumn(),
        })
        .from(this.table as PgTable)
        .where(eq(this.getIdColumn(), entity.id.value))
        .limit(1)
        .execute()

      const existingEntities = await existingEntityQuery
      const existingEntity = existingEntities[0]

      if (existingEntity?.deleted) {
        throw new CannotModifyDeletedEntity(this.entityName, entity.id.value)
      }

      const model = this.mapper.toModel(entity)

      const updateData = this.extractUpdateData(model)

      if (Object.keys(updateData).length === 0) {
        return
      }

      await dbClient
        .update(this.table)
        .set(updateData)
        .where(eq(this.getIdColumn(), entity.id.value))
        .execute()
      await domainEvents.dispatchEventsForEntity(entity.id)
    } catch (error) {
      this.handleError(error, `updating entity with ID ${entity.id.value}`)
    }
  }

  protected extractUpdateData(model: TModel): Partial<TModel> {
    const updateData: Partial<TModel> = {}

    for (const key in model) {
      if (
        key !== this.idColumnName &&
        key !== this.createdAtColumnName &&
        Object.prototype.hasOwnProperty.call(model, key)
      ) {
        updateData[key as keyof TModel] = model[key as keyof TModel]
      }
    }

    return updateData
  }

  protected async processRelatedEntities(
    /* eslint-disable @typescript-eslint/no-unused-vars */
    entity: TDomain,
    dbClient: DatabaseClient,
    /* eslint-enable @typescript-eslint/no-unused-vars */
  ): Promise<void> {
    // Subclasses should override this to handle their specific relations
  }

  protected buildQueryOptions<P extends ExtendedFindQuery>(
    params?: P & RelationOptions,
    whereConditions: SQL[] = [],
  ): QueryOptions {
    if (params?.customWhere) {
      whereConditions.push(params.customWhere)
    }

    const whereClause = this.buildWhereConditions(
      whereConditions,
      params?.includeDeleted,
    )

    let limit: number | undefined = undefined
    let offset: number | undefined = undefined
    if (params?.includeAll === false) {
      const defaultPageSize = Configuration.instance.get(
        'application.pagination.defaultPageSize',
      )
      const maxPageSize = Configuration.instance.get(
        'application.pagination.maxPageSize',
      )
      limit = (params as FindPageParams)?.limit ?? defaultPageSize
      if (limit > maxPageSize) limit = maxPageSize
      if (limit <= 0) limit = defaultPageSize
      offset = (params as FindPageParams)?.offset ?? 0
      if (offset < 0) offset = 0
    }

    const queryOptions: QueryOptions = {
      where: whereClause,
      limit,
      offset,
    }

    if (params?.orderBy) {
      const orderFn = params.orderDirection === 'DESC' ? desc : asc
      const orderByArray = Array.isArray(params.orderBy)
        ? params.orderBy
        : [params.orderBy]
      queryOptions.orderBy = orderByArray
        .filter((col) => this.table[col as keyof TTable])
        .map((col) => orderFn(this.table[col as keyof TTable] as PgColumn))
    }

    // Handle relations
    if (params?.with) {
      queryOptions.with = this.processRelations(
        params.with,
        params.includeDeleted,
      )
    }

    return queryOptions
  }

  protected processRelations(
    relationsConfig: Record<string, boolean | RelationOptions>,
    includeDeleted = false, // This applies to the relations being processed at this level
  ): Record<string, unknown> {
    const processedRelations: Record<string, unknown> = {}

    for (const [key, value] of Object.entries(relationsConfig)) {
      if (value === true) {
        // If true, include the relation, applying soft-delete filter by default
        processedRelations[key] = !includeDeleted
          ? {
              where: AbstractDrizzleRepository.DELETED_CHECK,
            }
          : true
      } else if (typeof value === 'object' && value !== null) {
        // If it's an object, it's a nested RelationOptions
        const nestedIncludeDeleted =
          value.includeDeleted === undefined
            ? includeDeleted
            : value.includeDeleted

        const nestedWithConfig = value.with
          ? this.processRelations(value.with, nestedIncludeDeleted) // Recursive call for nested relations
          : undefined

        processedRelations[key] = !nestedIncludeDeleted
          ? {
              where: AbstractDrizzleRepository.DELETED_CHECK,
              with: nestedWithConfig,
            }
          : {
              with: nestedWithConfig,
            } // If including deleted, no where clause for soft delete needed here
      }
    }
    return processedRelations
  }

  protected handleError(error: unknown, action: string): never {
    // TODO 23503 - Constraint reference does not exist
    if (
      error &&
      typeof error === 'object' &&
      'code' in error &&
      error.code === '23505'
    ) {
      const constraintName =
        'constraint' in error && typeof error.constraint === 'string'
          ? error.constraint
          : 'unknown_constraint'
      throw new UniqueConstraintViolation(
        this.entityName,
        constraintName,
        error,
      )
    }

    if (error instanceof RepositoryError) {
      throw error
    }

    throw new RepositoryError(
      `An error occurred while ${action}`,
      undefined,
      error,
    )
  }
  protected getIdColumn(): PgColumn {
    return this.table[this.idColumnName as keyof TTable] as PgColumn
  }

  protected getDeletedColumn(): PgColumn {
    if (this.deletedColumnName in this.table) {
      return this.table[this.deletedColumnName as keyof TTable] as PgColumn
    }
    throw new RepositoryError(
      `Deleted column '${this.deletedColumnName}' not found on table ${this.tableName}. All tables must support soft deletes.`,
    )
  }
}

export { AbstractDrizzleRepository }
