import { Entity } from '../domain/entity'
import { UniqueEntityID } from '../domain/uniqueEntityID'

type SoftDeleteFilter = {
  includeDeleted?: boolean
}

type SortOptions = {
  orderBy?: string | string[]
  orderDirection?: 'ASC' | 'DESC'
}

type PageOptions = {
  limit?: number
  offset?: number
}

type FindPageParams = SoftDeleteFilter &
  SortOptions &
  PageOptions & {
    includeAll?: false
  }

type FindAllParams = SoftDeleteFilter &
  SortOptions & {
    includeAll?: true
  }

type BaseFindQuery = FindPageParams | FindAllParams

interface GenericRepository<Domain extends Entity<unknown>> {
  exists(id: UniqueEntityID, options?: SoftDeleteFilter): Promise<boolean>
  getById(id: UniqueEntityID, options?: SoftDeleteFilter): Promise<Domain>
  save(entity: Domain): Promise<void>
  saveMany(entities: Domain[]): Promise<void>
  delete(id: UniqueEntityID): Promise<void>
}

export type {
  GenericRepository,
  SoftDeleteFilter,
  FindPageParams,
  FindAllParams,
  BaseFindQuery,
}
