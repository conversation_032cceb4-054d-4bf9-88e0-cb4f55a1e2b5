import * as crypto from 'crypto'

/**
 * Generates a consistent, v4-formatted UUID-like identifier based on a seed string.
 * This ensures the same input always produces the same UUID.
 * Note: This is not a standard RFC 4122 Version 4 UUID (which involves randomness),
 * but a deterministic ID formatted to resemble one.
 */
export function generateConsistentUUID(seed: string): string {
  const hash = crypto.createHash('sha256').update(seed).digest('hex')
  return `${hash.slice(0, 8)}-${hash.slice(8, 12)}-4${hash.slice(13, 16)}-8${hash.slice(17, 20)}-${hash.slice(20, 32)}`
}

/**
 * Batch process an array of items in parallel with a concurrency limit
 */
export async function batchProcess<T, R>(
  items: T[],
  processFn: (item: T) => Promise<R>,
  batchSize = 10,
): Promise<R[]> {
  const results: R[] = []

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await Promise.all(batch.map((item) => processFn(item)))
    results.push(...batchResults)
  }

  return results
}
