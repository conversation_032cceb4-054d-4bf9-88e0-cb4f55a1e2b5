import { drizzle } from 'drizzle-orm/node-postgres'
import { reset } from 'drizzle-seed'
import { Pool } from 'pg'
import { Environment } from '../../infra/environment'
import * as schema from '../schema'

export interface SeedConfig {
  db: ReturnType<typeof drizzle<typeof schema>>
  pool: Pool
}

export async function createSeedConfig(resetFlag = false): Promise<SeedConfig> {
  const environment = Environment.instance.get('ENVIRONMENT')
  if (!['develop', 'test'].includes(environment)) {
    throw new Error(
      `🚨 Database seeding is only allowed in development or test environments. Current environment: ${environment}`,
    )
  }

  const pool = new Pool({
    connectionString: Environment.instance.get('DATABASE_URL'),
  })

  const db = drizzle(pool, { schema })

  if (resetFlag) {
    console.log('🧹 Cleaning up existing data...')

    try {
      await reset(db, schema)
      console.log('✅ Database cleaned up successfully')
    } catch (error) {
      console.error('❌ Error cleaning up database:', error)
      throw error
    }
  }

  return { db, pool }
}

export async function closeSeedConfig(config: SeedConfig): Promise<void> {
  await config.pool.end()
}
