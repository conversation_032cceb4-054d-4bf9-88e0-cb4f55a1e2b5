import dotenv from 'dotenv'
import { seedActivities } from './data/activities/activities.seed'
import { seedCollections } from './data/collections/collections.seed'
import { seedEntries } from './data/entries/entries.seed'
import { seedFieldGroups } from './data/fields/fields.seed'
import { seedServices } from './data/services/services.seed'
import { seedUsers } from './data/users/users.seed'
import {
  createSeedConfig,
  closeSeedConfig,
  type SeedConfig,
} from './seed.config'

dotenv.config()

async function executeSeeding(config: SeedConfig): Promise<void> {
  const startTime = Date.now()
  console.log('🌱 Starting database seeding...')
  console.log('================================')

  const collectionIds = await seedCollections(config)
  const userIds = await seedUsers(config, collectionIds)

  await seedFieldGroups(config, collectionIds)
  await seedServices(config, collectionIds)
  await seedEntries(config, collectionIds, userIds)
  await seedActivities(config, collectionIds, userIds)

  const endTime = Date.now()
  console.log('================================')
  console.log(
    `✅ Database seeding completed successfully in ${(endTime - startTime).toFixed(2)}ms!`,
  )
  console.log('================================')
}

async function seed() {
  const resetFlag = process.argv.includes('--reset')
  let config: SeedConfig | null = null

  try {
    config = await createSeedConfig(resetFlag)
    await executeSeeding(config)
    process.exit(0)
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    process.exit(1)
  } finally {
    if (config) {
      await closeSeedConfig(config).catch((error) => {
        console.error('❌ Error closing seed configuration:', error)
      })
    }
  }
}

seed()
