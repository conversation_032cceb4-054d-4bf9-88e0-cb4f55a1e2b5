import { generateConsistentUUID } from '../../seed.utils'
import { predefinedUsers, type UserSeedData } from '../users/users.data'
import {
  ACTIVITY_TYPES,
  HOUR_MS,
  MAIN_COLLECTION_ACTIVITY_COUNT,
  ADDITIONAL_COLLECTION_ACTIVITY_COUNT,
  NOTIFICATION_ROLES,
  UNREAD_ACTIVITY_THRESHOLD,
  type ActivityData,
  type ActivityType,
  type ActivityTypeData,
  type CreateNotificationParams,
  type NotificationData,
} from './activities.data'

// ============================================
// Logic Helpers
// ============================================

function getActivityType(index: number): ActivityTypeData {
  const activityType = ACTIVITY_TYPES[Math.abs(index) % ACTIVITY_TYPES.length]
  if (!activityType) {
    throw new Error(`No activity type found for index ${index}`)
  }
  return activityType
}

function generateActivityDetails(
  type: ActivityType,
  index: number,
  collectionId: string,
  userId: string,
): Record<string, unknown> {
  switch (type) {
    case 'ENTRY_CREATE':
    case 'ENTRY_UPDATE':
    case 'ENTRY_DELETE':
      return {
        entryId: generateConsistentUUID(`entry-${collectionId}-${index}`),
        entryTitle: `Sample Entry ${index + 1}`,
      }
    case 'USER_INVITE':
      return {
        invitedUserId: generateConsistentUUID(`user-invited-${index}`),
        invitedUserEmail: `invited.user.${index}@example.com`,
      }
    case 'ROLE_CHANGE':
      return {
        targetUserId: userId,
        oldRole: 'CONTRIBUTOR',
        newRole: 'EDITOR',
      }
    default:
      return {}
  }
}

function getUsersForCollection(
  collectionIndex: number,
  allUserIds: string[],
  allPredefinedUsers: UserSeedData[],
): UserSeedData[] {
  const activeUsers = allPredefinedUsers.filter((user) =>
    allUserIds.includes(user.id),
  )

  const platformManagers = activeUsers.filter(
    (user) => user.role === 'PLATFORM_MANAGER',
  )
  const otherUsers = activeUsers.filter(
    (user) => user.role !== 'PLATFORM_MANAGER',
  )
  const usersForCollection = otherUsers.filter(
    (_, index) => index % 6 === collectionIndex,
  )
  return [...platformManagers, ...usersForCollection]
}

function createCollectionActivities(
  collectionId: string,
  count: number,
  collectionIndex: number,
  baseTimestamp: number,
  userIds: string[],
): ActivityData[] {
  const activities: ActivityData[] = []
  const availableUsers = getUsersForCollection(
    collectionIndex,
    userIds,
    predefinedUsers,
  )

  if (availableUsers.length === 0) {
    console.warn(`No users available for collection ${collectionId}`)
    return []
  }

  for (let i = 0; i < count; i++) {
    const userIndex = (collectionIndex + i) % availableUsers.length
    const user = availableUsers[userIndex]
    if (!user) {
      throw new Error(
        `No user found at index ${userIndex} for collection ${collectionId}`,
      )
    }

    const activityType = getActivityType(collectionIndex + i)
    const timestamp = new Date(
      baseTimestamp - i * HOUR_MS - collectionIndex * 5 * 60 * 1000,
    )
    const details = generateActivityDetails(
      activityType.type,
      collectionIndex * 100 + i,
      collectionId,
      user.id,
    )

    activities.push({
      id: generateConsistentUUID(`activity-${collectionId}-${i}`),
      collectionId,
      userId: user.id,
      type: activityType.type,
      data: {
        message: activityType.message,
        timestamp: timestamp.toISOString(),
        ...details,
      },
      deleted: false,
      createdAt: timestamp,
      updatedAt: timestamp,
    })
  }
  return activities
}

export async function createActivities(
  collectionIds: string[],
  userIds: string[],
): Promise<ActivityData[]> {
  if (!collectionIds?.length) {
    return []
  }

  const activities: ActivityData[] = []
  const now = Date.now()

  for (const [index, collectionId] of collectionIds.entries()) {
    if (!collectionId) continue

    const isMainCollection = index === 0
    const activityCount = isMainCollection
      ? MAIN_COLLECTION_ACTIVITY_COUNT
      : ADDITIONAL_COLLECTION_ACTIVITY_COUNT

    const collectionActivities = createCollectionActivities(
      collectionId,
      activityCount,
      index,
      now,
      userIds, // Pass userIds
    )
    activities.push(...collectionActivities)
  }

  return activities
}

function createNotification(
  params: CreateNotificationParams,
): NotificationData {
  const now = params.timestamp
  return {
    id: generateConsistentUUID(
      `notification-${params.activityId}-${params.userId}`,
    ),
    activityId: params.activityId,
    userId: params.userId,
    read: params.isRead,
    sent: params.sent ?? true,
    deleted: false,
    createdAt: now,
    updatedAt: now,
  }
}

export function createNotifications(
  activities: ActivityData[],
  allPredefinedUsers: UserSeedData[],
): NotificationData[] {
  if (!activities?.length) {
    return []
  }

  const allNotifications: NotificationData[] = []
  const activitiesByCollection = activities.reduce<
    Record<string, ActivityData[]>
  >((acc, activity) => {
    const collectionId = activity.collectionId
    if (!acc[collectionId]) {
      acc[collectionId] = []
    }
    acc[collectionId].push(activity)
    return acc
  }, {})

  for (const [collectionId, collectionActivities] of Object.entries(
    activitiesByCollection,
  )) {
    const collectionIndex = activities.findIndex(
      (act) => act.collectionId === collectionId,
    )
    const collectionUsersForNotifications = getUsersForCollection(
      collectionIndex,
      allPredefinedUsers.map((u) => u.id),
      allPredefinedUsers,
    )

    if (collectionUsersForNotifications.length === 0) {
      continue
    }

    collectionActivities.forEach((activity, activityIndex) => {
      const activityTimestamp = new Date(activity.data.timestamp)
      allNotifications.push(
        createNotification({
          activityId: activity.id,
          userId: activity.userId,
          isRead:
            (allPredefinedUsers[0] &&
              activity.userId === allPredefinedUsers[0].id) ||
            activityIndex < UNREAD_ACTIVITY_THRESHOLD,
          timestamp: activityTimestamp,
        }),
      )

      collectionUsersForNotifications.forEach((user, receiverIndex) => {
        if (user.id === activity.userId) return

        const shouldNotify = NOTIFICATION_ROLES.includes(user.role)
        if (!shouldNotify) return

        const notificationTime = new Date(
          activityTimestamp.getTime() + receiverIndex * 30 * 1000,
        )

        allNotifications.push(
          createNotification({
            activityId: activity.id,
            userId: user.id,
            isRead: receiverIndex % 3 !== 0,
            timestamp: notificationTime,
          }),
        )
      })
    })
  }

  return allNotifications
}
