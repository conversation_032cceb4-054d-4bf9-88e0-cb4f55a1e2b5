import { activitiesTable, notificationsTable } from '../../../schema/activities'
import type { SeedConfig } from '../../seed.config'
import { batchProcess } from '../../seed.utils'
import { predefinedUsers } from '../users/users.data'
import type { ActivityData, NotificationData } from './activities.data'
import { createActivities, createNotifications } from './activities.logic'

// ============================================
// Main Seed Function
// ============================================

export async function seedActivities(
  config: SeedConfig,
  collectionIds: string[],
  userIds: string[],
): Promise<void> {
  console.log('🌱 Seeding activities...')

  try {
    const activities = await createActivities(collectionIds, userIds)
    await saveActivities(config, activities)

    if (activities.length > 0) {
      await seedNotifications(config, activities)
    } else {
      console.log('No activities to create notifications for')
    }
  } catch (error) {
    console.error('Error seeding activities:', error)
    throw error
  }
}

// ============================================
// Database Operations
// ============================================

async function saveActivities(
  config: SeedConfig,
  activities: ActivityData[],
): Promise<void> {
  if (!activities?.length) {
    console.log('No activities to save')
    return
  }

  try {
    await batchProcess(
      activities,
      async (activity) => {
        await config.db
          .insert(activitiesTable)
          .values(activity)
          .onConflictDoUpdate({
            target: activitiesTable.id,
            set: {
              collectionId: activity.collectionId,
              userId: activity.userId,
              type: activity.type,
              data: activity.data,
              updatedAt: new Date(),
            },
          })
      },
      50,
    )

    console.log(`✅ Successfully seeded ${activities.length} activities`)
  } catch (error) {
    console.error('Error saving activities:', error)
    throw error
  }
}

async function saveNotifications(
  config: SeedConfig,
  notifications: NotificationData[],
): Promise<void> {
  if (!notifications.length) {
    console.log('No notifications to save')
    return
  }

  try {
    await batchProcess(
      notifications,
      async (notification) => {
        await config.db
          .insert(notificationsTable)
          .values(notification)
          .onConflictDoUpdate({
            target: notificationsTable.id,
            set: {
              ...notification,
              updatedAt: new Date(),
            },
          })
      },
      50,
    )
  } catch (error) {
    console.error('Error saving notifications:', error)
    throw error
  }
}

// ============================================
// Notification Seeding
// ============================================

async function seedNotifications(
  config: SeedConfig,
  activities: ActivityData[],
): Promise<void> {
  if (!activities?.length) {
    console.log('No activities to create notifications for')
    return
  }

  console.log('🌱 Seeding notifications...')

  try {
    const notifications = createNotifications(activities, predefinedUsers)
    if (notifications.length > 0) {
      await saveNotifications(config, notifications)
      console.log(
        `✅ Successfully seeded ${notifications.length} notifications`,
      )
    } else {
      console.log('No notifications were created')
    }
  } catch (error) {
    console.error('❌ Error seeding notifications:', error)
    throw error
  }
}
