import type { Role } from '../users/users.data'

// ============================================
// Constants
// ============================================

export const MAIN_COLLECTION_ACTIVITY_COUNT = 5
export const ADDITIONAL_COLLECTION_ACTIVITY_COUNT = 3
export const HOUR_MS = 3600000
export const UNREAD_ACTIVITY_THRESHOLD = 10

export const NOTIFICATION_ROLES: Role[] = [
  'PLATFORM_MANAGER',
  'COLLECTION_MANAGER',
  'EDITOR',
]

// ============================================
// Types
// ============================================

export type ActivityType =
  | 'ENTRY_CREATE'
  | 'ENTRY_UPDATE'
  | 'ENTRY_DELETE'
  | 'USER_INVITE'
  | 'ROLE_CHANGE'

export interface ActivityTypeData {
  type: ActivityType
  message: string
}

export interface ActivityData {
  id: string
  collectionId: string
  userId: string
  type: string
  data: {
    message: string
    timestamp: string
    [key: string]: unknown
  }
  deleted?: boolean
  createdAt: Date
  updatedAt: Date
}

export interface NotificationData {
  id: string
  activityId: string
  userId: string
  read: boolean
  sent: boolean
  deleted: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateNotificationParams {
  activityId: string
  userId: string
  isRead: boolean
  timestamp: Date
  sent?: boolean
}

// ============================================
// Activity Type Definitions
// ============================================

export const ACTIVITY_TYPES: readonly ActivityTypeData[] = [
  { type: 'ENTRY_CREATE', message: 'Created new entry' },
  { type: 'ENTRY_UPDATE', message: 'Updated entry' },
  { type: 'ENTRY_DELETE', message: 'Deleted entry' },
  { type: 'USER_INVITE', message: 'Invited new user' },
  { type: 'ROLE_CHANGE', message: 'Role permissions changed' },
] as const
