import {
  fieldGroupsTable,
  fieldsTable,
  collectionFieldGroupsTable,
} from '../../../schema/collections'
import type { SeedConfig } from '../../seed.config'
import { batchProcess } from '../../seed.utils'
import {
  fieldGroupsData,
  FIELD_GROUP_INDICES,
  type FieldData,
} from './fields.data'

// ============================================
// Field and Field Group Seeding
// ============================================

export async function seedFieldGroups(
  config: SeedConfig,
  collectionIds: string[],
): Promise<Record<string, Record<string, string>>> {
  console.log('🌱 Seeding field groups and fields...')
  const fieldIds: Record<string, Record<string, string>> = {}
  const now = new Date()

  try {
    await batchProcess(
      fieldGroupsData,
      async (groupData) => {
        await config.db.transaction(async (tx) => {
          await tx
            .insert(fieldGroupsTable)
            .values({
              id: groupData.id,
              name: groupData.name,
              position: groupData.position,
              createdAt: now,
              updatedAt: now,
              deleted: false,
            })
            .onConflictDoUpdate({
              target: fieldGroupsTable.id,
              set: {
                name: groupData.name,
                position: groupData.position,
                updatedAt: now,
                deleted: false,
              },
            })

          if (!fieldIds[groupData.name]) {
            fieldIds[groupData.name] = {}
          }

          await batchProcess(
            groupData.fields,
            async (field: FieldData) => {
              const fieldNow = new Date()
              await tx
                .insert(fieldsTable)
                .values({
                  id: field.id,
                  name: field.name,
                  type: field.type,
                  position: field.position,
                  mandatory: field.mandatory,
                  visibility: field.visibility,
                  preview: field.preview,
                  maxLength: field.maxLength,
                  values: field.values ? JSON.stringify(field.values) : null,
                  fieldGroupId: groupData.id,
                  createdAt: fieldNow,
                  updatedAt: fieldNow,
                  deleted: false,
                })
                .onConflictDoUpdate({
                  target: fieldsTable.id,
                  set: {
                    name: field.name,
                    type: field.type,
                    position: field.position,
                    mandatory: field.mandatory,
                    visibility: field.visibility,
                    preview: field.preview,
                    maxLength: field.maxLength,
                    values: field.values ? JSON.stringify(field.values) : null,
                    fieldGroupId: groupData.id,
                    updatedAt: fieldNow,
                    deleted: false,
                  },
                })
              if (fieldIds[groupData.name]) {
                fieldIds[groupData.name]![field.name] = field.id
              }
            },
            10,
          )
        })
      },
      5,
    )

    console.log(`✅ Field groups and fields seeded successfully`)

    console.log('🌱 Associating field groups with collections...')

    const associations: Array<{
      collectionId: string
      fieldGroupId: string
    }> = []

    if (collectionIds.length > 0) {
      const firstCollectionId = collectionIds[0]
      const secondCollectionId = collectionIds[1]

      if (firstCollectionId) {
        associations.push(
          {
            collectionId: firstCollectionId,
            fieldGroupId: fieldGroupsData[FIELD_GROUP_INDICES.BASIC_INFO]!.id,
          },
          {
            collectionId: firstCollectionId,
            fieldGroupId:
              fieldGroupsData[FIELD_GROUP_INDICES.COLLECTION_DETAILS]!.id,
          },
          {
            collectionId: firstCollectionId,
            fieldGroupId: fieldGroupsData[FIELD_GROUP_INDICES.STORAGE_INFO]!.id,
          },
          {
            collectionId: firstCollectionId,
            fieldGroupId:
              fieldGroupsData[FIELD_GROUP_INDICES.QUALITY_CONTROL]!.id,
          },
        )
      }

      if (secondCollectionId) {
        associations.push(
          {
            collectionId: secondCollectionId,
            fieldGroupId: fieldGroupsData[FIELD_GROUP_INDICES.BASIC_INFO]!.id,
          },
          {
            collectionId: secondCollectionId,
            fieldGroupId:
              fieldGroupsData[FIELD_GROUP_INDICES.ALGAE_CULTURE]!.id,
          },
          {
            collectionId: secondCollectionId,
            fieldGroupId:
              fieldGroupsData[FIELD_GROUP_INDICES.QUALITY_CONTROL]!.id,
          },
        )
      }

      for (let i = 2; i < collectionIds.length; i++) {
        const collectionId = collectionIds[i]
        if (collectionId) {
          associations.push(
            {
              collectionId,
              fieldGroupId: fieldGroupsData[FIELD_GROUP_INDICES.BASIC_INFO]!.id,
            },
            {
              collectionId,
              fieldGroupId:
                fieldGroupsData[FIELD_GROUP_INDICES.COLLECTION_DETAILS]!.id,
            },
          )
        }
      }
    }

    if (associations.length > 0) {
      await batchProcess(
        associations,
        async (association) => {
          await config.db
            .insert(collectionFieldGroupsTable)
            .values({
              collectionId: association.collectionId,
              fieldGroupId: association.fieldGroupId,
            })
            .onConflictDoNothing()
        },
        20,
      )
    }

    console.log(`✅ Field groups associated with collections successfully`)

    return fieldIds
  } catch (error) {
    console.error('❌ Error in seedFieldGroups:', error)
    throw error
  }
}
