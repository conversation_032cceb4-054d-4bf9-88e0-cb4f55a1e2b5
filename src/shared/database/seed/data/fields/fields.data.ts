import { generateConsistentUUID } from '../../seed.utils'

// ============================================
// Types
// ============================================

export type FieldType =
  | 'TEXT'
  | 'TEXTAREA'
  | 'NUMBER'
  | 'SELECT'
  | 'SELECT-MANY'
  | 'IMAGE-UPLOAD'

export type Visibility = 'PUBLIC' | 'PRIVATE'

export interface FieldGroupData {
  id: string
  name: string
  position: number
  fields: FieldData[]
}

export interface FieldData {
  id: string
  name: string
  type: FieldType
  position: number
  mandatory: boolean
  visibility: Visibility
  preview: boolean
  maxLength?: number
  values?: Array<{ label: string; value: string }>
}

// ============================================
// Constants
// ============================================

export const FIELD_GROUP_INDICES = {
  BASIC_INFO: 0,
  COLLECTION_DETAILS: 1,
  STORAGE_INFO: 2,
  QUALITY_CONTROL: 3,
  ALGAE_CULTURE: 4,
} as const

export const fieldGroupsData: FieldGroupData[] = [
  // Basic Information Group
  {
    id: generateConsistentUUID('field-group-basic-information'),
    name: 'Basic Information',
    position: 1,
    fields: [
      {
        id: generateConsistentUUID('field-sample-id'),
        name: 'Sample ID',
        type: 'TEXT',
        position: 1,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
        maxLength: 50,
      },
      {
        id: generateConsistentUUID('field-scientific-name'),
        name: 'Scientific Name',
        type: 'TEXT',
        position: 2,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
        maxLength: 100,
      },
      {
        id: generateConsistentUUID('field-common-name'),
        name: 'Common Name',
        type: 'TEXT',
        position: 3,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
        maxLength: 100,
      },
      {
        id: generateConsistentUUID('field-sample-type'),
        name: 'Sample Type',
        type: 'SELECT',
        position: 4,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
        values: [
          { label: 'DNA', value: 'dna' },
          { label: 'Tissue', value: 'tissue' },
          { label: 'Protein', value: 'protein' },
          { label: 'Whole Organism', value: 'whole_organism' },
        ],
      },
    ],
  },
  {
    id: generateConsistentUUID('field-group-collection-details'),
    name: 'Collection Details',
    position: 2,
    fields: [
      {
        id: generateConsistentUUID('field-collection-date'),
        name: 'Collection Date',
        type: 'TEXT',
        position: 1,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-collection-location'),
        name: 'Collection Location',
        type: 'TEXT',
        position: 2,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
        maxLength: 200,
      },
      {
        id: generateConsistentUUID('field-gps-coordinates'),
        name: 'GPS Coordinates',
        type: 'TEXT',
        position: 3,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-depth'),
        name: 'Depth (m)',
        type: 'NUMBER',
        position: 4,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-collector-name'),
        name: 'Collector Name',
        type: 'TEXT',
        position: 5,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
      },
    ],
  },
  {
    id: generateConsistentUUID('field-group-storage-info'),
    name: 'Storage Information',
    position: 3,
    fields: [
      {
        id: generateConsistentUUID('field-storage-method'),
        name: 'Storage Method',
        type: 'SELECT',
        position: 1,
        mandatory: true,
        visibility: 'PRIVATE',
        preview: false,
        values: [
          { label: 'Frozen -80°C', value: 'frozen_80' },
          { label: 'Frozen -20°C', value: 'frozen_20' },
          { label: 'Refrigerated 4°C', value: 'refrigerated_4' },
          { label: 'Room Temperature', value: 'room_temp' },
          { label: 'Liquid Nitrogen', value: 'liquid_nitrogen' },
        ],
      },
      {
        id: generateConsistentUUID('field-storage-location'),
        name: 'Storage Location',
        type: 'TEXT',
        position: 2,
        mandatory: true,
        visibility: 'PRIVATE',
        preview: false,
      },
      {
        id: generateConsistentUUID('field-container-type'),
        name: 'Container Type',
        type: 'SELECT',
        position: 3,
        mandatory: true,
        visibility: 'PRIVATE',
        preview: false,
        values: [
          { label: 'Cryovial', value: 'cryovial' },
          { label: 'Tube', value: 'tube' },
          { label: 'Plate', value: 'plate' },
          { label: 'Other', value: 'other' },
        ],
      },
      {
        id: generateConsistentUUID('field-status'),
        name: 'Status',
        type: 'SELECT',
        position: 5,
        mandatory: true,
        visibility: 'PRIVATE',
        preview: false,
        values: [
          { label: 'In Storage', value: 'in_storage' },
          { label: 'In Use', value: 'in_use' },
          { label: 'Disposed', value: 'disposed' },
        ],
      },
    ],
  },
  // Quality Control Group
  {
    id: generateConsistentUUID('field-group-quality-control'),
    name: 'Quality Control',
    position: 4,
    fields: [
      {
        id: generateConsistentUUID('field-quality-score'),
        name: 'Quality Score',
        type: 'SELECT',
        position: 1,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
        values: [
          { label: 'Excellent', value: 'excellent' },
          { label: 'Good', value: 'good' },
          { label: 'Fair', value: 'fair' },
          { label: 'Poor', value: 'poor' },
        ],
      },
      {
        id: generateConsistentUUID('field-dna-concentration'),
        name: 'DNA Concentration (ng/µL)',
        type: 'NUMBER',
        position: 2,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-notes'),
        name: 'Notes',
        type: 'TEXT',
        position: 3,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
        maxLength: 500,
      },
      {
        id: generateConsistentUUID('field-detailed-analysis'),
        name: 'Detailed Analysis',
        type: 'TEXTAREA',
        position: 4,
        mandatory: false,
        visibility: 'PRIVATE',
        preview: false,
        maxLength: 2000,
      },
      {
        id: generateConsistentUUID('field-sample-characteristics'),
        name: 'Sample Characteristics',
        type: 'SELECT-MANY',
        position: 5,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
        values: [
          { label: 'Fragile', value: 'fragile' },
          { label: 'Light Sensitive', value: 'light-sensitive' },
          { label: 'Temperature Sensitive', value: 'temp-sensitive' },
          { label: 'Hazardous', value: 'hazardous' },
          { label: 'Rare', value: 'rare' },
          { label: 'Endangered', value: 'endangered' },
        ],
      },
      {
        id: generateConsistentUUID('field-sample-photo'),
        name: 'Sample Photo',
        type: 'IMAGE-UPLOAD',
        position: 6,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
      },
    ],
  },
  // Algae Culture Info Group
  {
    id: generateConsistentUUID('field-group-algae-culture'),
    name: 'Algae Culture Info',
    position: 5,
    fields: [
      {
        id: generateConsistentUUID('field-strain-id'),
        name: 'Strain ID',
        type: 'TEXT',
        position: 1,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-species-name'),
        name: 'Species Name',
        type: 'TEXT',
        position: 2,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-isolation-date'),
        name: 'Isolation Date',
        type: 'TEXT',
        position: 3,
        mandatory: true,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-culture-conditions'),
        name: 'Culture Conditions',
        type: 'TEXT',
        position: 4,
        mandatory: false,
        visibility: 'PUBLIC',
        preview: true,
      },
      {
        id: generateConsistentUUID('field-growth-phase'),
        name: 'Growth Phase',
        type: 'SELECT',
        position: 5,
        mandatory: false,
        preview: true,
        visibility: 'PUBLIC',
        values: [
          { label: 'Exponential', value: 'exponential' },
          { label: 'Stationary', value: 'stationary' },
          { label: 'Decline', value: 'decline' },
        ],
      },
    ],
  },
]
