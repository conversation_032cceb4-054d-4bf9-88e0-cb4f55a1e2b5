import { generateConsistentUUID } from '../../seed.utils'

// ============================================
// Types
// ============================================

export interface EntryData {
  id: string
  collectionId: string
  creatorId: string
  data: Record<string, unknown>
  visibility: 'PUBLIC' | 'PRIVATE'
  createdAt?: Date
  updatedAt?: Date
}

export interface EditData {
  id: string
  entryId: string
  editorId: string
  reviewerId: string
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED'
  data: Record<string, unknown>
  deleted: boolean
  createdAt: Date
  updatedAt: Date
}

// ============================================
// Entry Definitions
// ============================================

export const MARINE_ORGANISM_ENTRIES = [
  {
    id: generateConsistentUUID('entry-marine-1'),
    collectionId: '',
    creatorId: '',
    visibility: 'PUBLIC' as const,
    data: {
      sampleId: 'MAR-1001',
      scientificName: 'Fucus vesiculosus',
      commonName: 'Bladder Wrack',
      sampleType: 'tissue',
      collectionDate: '2024-01-15',
      collectionLocation: 'Porto Coast',
      gpsCoordinates: '41.1496,-8.6110',
      depth: '12',
      collectorName: 'Maria Silva',
      storageMethod: 'frozen_80',
      storageLocation: 'Freezer 1, Shelf A',
      containerType: 'cryovial',
      qualityScore: 'good',
      dnaConcentration: '110.00',
      notes:
        'Sample collected during routine monitoring on 2024-01-15. Additional notes: Sample shows excellent quality.',
    },
  },
  {
    id: generateConsistentUUID('entry-marine-2'),
    collectionId: '',
    creatorId: '',
    visibility: 'PRIVATE' as const,
    data: {
      sampleId: 'MAR-1002',
      scientificName: 'Mytilus edulis',
      commonName: 'Blue Mussel',
      sampleType: 'dna',
      collectionDate: '2024-02-20',
      collectionLocation: 'Aveiro Lagoon',
      gpsCoordinates: '40.6412,-8.6536',
      depth: '5',
      collectorName: 'João Santos',
      storageMethod: 'frozen_80',
      storageLocation: 'Freezer 1, Shelf B',
      containerType: 'cryovial',
      qualityScore: 'good',
      dnaConcentration: '120.00',
      notes: 'Sample collected during routine monitoring on 2024-02-20.',
    },
  },
]

export const ALGAE_CULTURE_ENTRIES = [
  {
    id: generateConsistentUUID('entry-algae-1'),
    collectionId: '', // Will be set in seed function
    creatorId: '', // Will be set in seed function
    visibility: 'PUBLIC' as const,
    data: {
      strainId: 'ULVA-001',
      scientificName: 'Ulva lactuca',
      origin: 'Atlantic Ocean',
      collectionDate: '2024-03-10',
      cultureConditions: 'f/2 medium, 20°C, 12:12 light cycle',
      growthPhase: 'exponential',
      storageLocation: 'Freezer 2, Shelf A',
      notes: 'Culture started on 2024-03-10. Healthy growth observed.',
    },
  },
  {
    id: generateConsistentUUID('entry-algae-2'),
    collectionId: '',
    creatorId: '',
    visibility: 'PUBLIC' as const,
    data: {
      strainId: 'CHON-001',
      scientificName: 'Chondrus crispus',
      origin: 'North Atlantic',
      collectionDate: '2024-04-05',
      cultureConditions: 'f/2 medium, 20°C, 12:12 light cycle',
      growthPhase: 'stationary',
      storageLocation: 'Freezer 2, Shelf B',
      notes: 'Culture started on 2024-04-05. Requires subculturing soon.',
    },
  },
]
