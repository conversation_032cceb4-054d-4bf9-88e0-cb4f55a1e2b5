import { entriesTable, editsTable } from '../../../schema/entries'
import type { SeedConfig } from '../../seed.config'
import { batchProcess, generateConsistentUUID } from '../../seed.utils'
import { predefinedUsers } from '../users/users.data'
import {
  MARINE_ORGANISM_ENTRIES,
  ALGAE_CULTURE_ENTRIES,
  type EntryData,
  type EditData,
} from './entries.data'

// ============================================
// Edit Definitions
// ============================================

function createEditsForEntry(entry: EntryData, index: number): EditData[] {
  const edits: EditData[] = []
  const editorId = predefinedUsers[1]?.id || ''
  const reviewerId = predefinedUsers[0]?.id || ''

  if (!editorId || !reviewerId) return edits

  // Initial creation edit
  edits.push({
    id: generateConsistentUUID(`edit-${entry.id}-0`),
    entryId: entry.id,
    editorId: entry.creatorId,
    reviewerId,
    status: 'ACCEPTED' as const,
    data: { notes: 'Initial entry creation' },
    deleted: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  })

  if (index % 2 === 0) {
    edits.push({
      id: generateConsistentUUID(`edit-${entry.id}-1`),
      entryId: entry.id,
      editorId,
      reviewerId,
      status: index % 3 === 0 ? 'PENDING' : 'ACCEPTED',
      data: { notes: 'Updated sample information' },
      deleted: false,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
    })
  }

  return edits
}

// ============================================
// Main Seed Function
// ============================================

export async function seedEntries(
  config: SeedConfig,
  collectionIds: string[],
  userIds: string[],
): Promise<string[]> {
  console.log('🌱 Seeding entries...')
  const entryIds: string[] = []
  const now = new Date()

  try {
    if (collectionIds.length < 2) {
      console.error('❌ At least 2 collections are required')
      return entryIds
    }

    const marineOrganismCollection = collectionIds[0]
    const algaeCultureCollection = collectionIds[1]

    if (!marineOrganismCollection || !algaeCultureCollection) {
      console.error('❌ Missing required collections')
      return entryIds
    }

    const entries: EntryData[] = []

    MARINE_ORGANISM_ENTRIES.forEach((entry, index) => {
      const creatorIndex = 1 + (index % Math.max(1, userIds.length - 1))
      const creatorId = userIds[creatorIndex]
      if (creatorId) {
        entries.push({
          ...entry,
          collectionId: marineOrganismCollection,
          creatorId,
          createdAt: now,
          updatedAt: now,
        })
      }
    })

    ALGAE_CULTURE_ENTRIES.forEach((entry, index) => {
      const creatorIndex = 1 + (index % Math.max(1, userIds.length - 1))
      const creatorId = userIds[creatorIndex]
      if (creatorId) {
        entries.push({
          ...entry,
          collectionId: algaeCultureCollection,
          creatorId,
          createdAt: now,
          updatedAt: now,
        })
      }
    })

    if (entries.length > 0) {
      await batchProcess(entries, async (entry) => {
        await config.db
          .insert(entriesTable)
          .values(entry)
          .onConflictDoUpdate({
            target: entriesTable.id,
            set: { ...entry, updatedAt: new Date() },
          })
        entryIds.push(entry.id)
      })
    }

    const allEdits: EditData[] = []
    entries.forEach((entry, index) => {
      allEdits.push(...createEditsForEntry(entry, index))
    })

    if (allEdits.length > 0) {
      await batchProcess(allEdits, async (edit) => {
        await config.db
          .insert(editsTable)
          .values(edit)
          .onConflictDoUpdate({
            target: editsTable.id,
            set: { ...edit, updatedAt: new Date() },
          })
      })
      console.log(`✅ Successfully created ${allEdits.length} edits`)
    }

    console.log(`✅ Entries seeded successfully`)
  } catch (error) {
    console.error('❌ Error seeding entries:', error)
    throw error
  }

  return entryIds
}
