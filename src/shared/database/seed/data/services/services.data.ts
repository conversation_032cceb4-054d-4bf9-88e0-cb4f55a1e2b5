// ============================================
// Types
// ============================================

export interface ServiceTemplate {
  name: string
  description: string
  category: string
  availability: boolean
  link: string
}

export interface ServiceData extends ServiceTemplate {
  id: string
  collectionId: string
}

// ============================================
// Constants
// ============================================

export const SERVICE_TEMPLATES: readonly ServiceTemplate[] = [
  {
    name: 'DNA Extraction Service',
    description:
      'High-quality DNA extraction from marine organisms using specialized protocols',
    category: 'Laboratory Services',
    availability: true,
    link: 'https://biobank.example.com/services/dna-extraction',
  },
  {
    name: 'Sample Storage',
    description:
      'Long-term cryogenic storage in liquid nitrogen or -80°C freezers',
    category: 'Storage Services',
    availability: true,
    link: 'https://biobank.example.com/services/storage',
  },
  {
    name: 'Taxonomic Identification',
    description: 'Morphological and molecular identification of marine species',
    category: 'Identification Services',
    availability: true,
    link: 'https://biobank.example.com/services/taxonomy',
  },
  {
    name: 'Environmental DNA Analysis',
    description:
      'eDNA extraction and metabarcoding services for biodiversity assessment',
    category: 'Molecular Services',
    availability: false,
    link: 'https://biobank.example.com/services/edna',
  },
  {
    name: 'Biobanking Consultation',
    description:
      'Expert advice on sample collection, preservation, and management',
    category: 'Consulting',
    availability: true,
    link: 'https://biobank.example.com/services/consultation',
  },
  {
    name: 'RNA Sequencing',
    description: 'High-throughput RNA sequencing for gene expression analysis',
    category: 'Molecular Services',
    availability: true,
    link: 'https://biobank.example.com/services/rna-seq',
  },
  {
    name: 'Protein Analysis',
    description:
      'Proteomics services including mass spectrometry and Western blotting',
    category: 'Laboratory Services',
    availability: true,
    link: 'https://biobank.example.com/services/proteins',
  },
  {
    name: 'Histology Services',
    description:
      'Tissue processing, sectioning, and staining for microscopic analysis',
    category: 'Laboratory Services',
    availability: true,
    link: 'https://biobank.example.com/services/histology',
  },
  {
    name: 'Cell Culture Maintenance',
    description: 'Maintenance and propagation of marine cell lines',
    category: 'Culture Services',
    availability: false,
    link: 'https://biobank.example.com/services/cell-culture',
  },
  {
    name: 'Sample Shipping',
    description:
      'International shipping of biological samples with proper documentation',
    category: 'Logistics',
    availability: true,
    link: 'https://biobank.example.com/services/shipping',
  },
] as const

const SERVICE_INDEX = {
  DNA_EXTRACTION: 0,
  SAMPLE_STORAGE: 1,
  TAXONOMIC_ID: 2,
  EDNA_ANALYSIS: 3,
  BIOBANK_CONSULT: 4,
  RNA_SEQ: 5,
  PROTEIN_ANALYSIS: 6,
  HISTOLOGY: 7,
  CELL_CULTURE: 8,
  SAMPLE_SHIPPING: 9,
} as const

export const COLLECTION_SERVICES: ReadonlyArray<ReadonlyArray<number>> = [
  // Collection 0: Core services
  [
    SERVICE_INDEX.DNA_EXTRACTION,
    SERVICE_INDEX.SAMPLE_STORAGE,
    SERVICE_INDEX.TAXONOMIC_ID,
    SERVICE_INDEX.BIOBANK_CONSULT,
    SERVICE_INDEX.PROTEIN_ANALYSIS,
  ],
  // Collection 1: Molecular services focus
  [
    SERVICE_INDEX.EDNA_ANALYSIS,
    SERVICE_INDEX.HISTOLOGY,
    SERVICE_INDEX.SAMPLE_STORAGE,
    SERVICE_INDEX.DNA_EXTRACTION,
    SERVICE_INDEX.RNA_SEQ,
  ],
  // Collection 2: Basic services
  [
    SERVICE_INDEX.DNA_EXTRACTION,
    SERVICE_INDEX.CELL_CULTURE,
    SERVICE_INDEX.SAMPLE_STORAGE,
    SERVICE_INDEX.TAXONOMIC_ID,
  ],
  // Collection 3: Specialized services
  [
    SERVICE_INDEX.EDNA_ANALYSIS,
    SERVICE_INDEX.RNA_SEQ,
    SERVICE_INDEX.SAMPLE_SHIPPING,
  ],
  // Collection 4: Additional services (if more collections are added)
  [
    SERVICE_INDEX.BIOBANK_CONSULT,
    SERVICE_INDEX.HISTOLOGY,
    SERVICE_INDEX.SAMPLE_SHIPPING,
  ],
  // Collection 5: More services (if needed)
  [SERVICE_INDEX.PROTEIN_ANALYSIS, SERVICE_INDEX.RNA_SEQ],
]
