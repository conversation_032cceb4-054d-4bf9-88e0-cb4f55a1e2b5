import { servicesTable } from '../../../schema/services'
import type { SeedConfig } from '../../seed.config'
import { batchProcess, generateConsistentUUID } from '../../seed.utils'
import {
  SERVICE_TEMPLATES,
  COLLECTION_SERVICES,
  type ServiceTemplate,
  type ServiceData,
} from './services.data'

// ============================================
// Helper Functions
// ============================================

function createService(
  collectionId: string,
  template: ServiceTemplate,
): ServiceData {
  return {
    id: generateConsistentUUID(
      `service-${template.name.toLowerCase().replace(/\s+/g, '-')}-${collectionId}`,
    ),
    collectionId,
    ...template,
  }
}

function getServiceTemplatesByIndexes(
  indexes: readonly number[],
): ServiceTemplate[] {
  return indexes.map((index) => {
    const template = SERVICE_TEMPLATES[index]
    if (!template) {
      throw new Error(`Invalid service template index: ${index}`)
    }
    return template
  })
}

// ============================================
// Main Seed Function
// ============================================

export async function seedServices(
  config: SeedConfig,
  collectionIds: string[],
): Promise<void> {
  console.log('🌱 Seeding services...')

  try {
    const allServices: ServiceData[] = []

    collectionIds.forEach((collectionId, index) => {
      const serviceIndexes = COLLECTION_SERVICES[index]
      if (serviceIndexes) {
        const templates = getServiceTemplatesByIndexes(serviceIndexes)
        const services = templates.map((template) =>
          createService(collectionId, template),
        )
        allServices.push(...services)
      }
    })

    if (allServices.length > 0) {
      await batchProcess(
        allServices,
        async (service) => {
          await config.db
            .insert(servicesTable)
            .values(service)
            .onConflictDoUpdate({
              target: servicesTable.id,
              set: { ...service, updatedAt: new Date() },
            })
        },
        50,
      )
    }

    console.log(
      `✅ Successfully seeded ${allServices.length} services across ${
        collectionIds.length
      } collections`,
    )
  } catch (error) {
    console.error('Error seeding services:', error)
    throw error
  }
}
