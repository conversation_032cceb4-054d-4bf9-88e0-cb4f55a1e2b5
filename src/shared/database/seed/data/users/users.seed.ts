import { usersTable, rolesTable } from '../../../schema/users'
import type { SeedConfig } from '../../seed.config'
import { batchProcess, generateConsistentUUID } from '../../seed.utils'
import { predefinedUsers, type Role, type UserSeedData } from './users.data'

// ============================================
// User Seeding
// ============================================

/**
 * Seeds users in the database
 */
export async function seedUsers(
  config: SeedConfig,
  collectionIds: string[],
): Promise<string[]> {
  if (!predefinedUsers?.length) {
    console.log('⚠️ No predefined users found')
    return []
  }

  console.log('🌱 Seeding users...')
  const userIds: string[] = []
  const now = new Date()

  try {
    const insertedUserIds = await batchProcess(
      predefinedUsers,
      async (user: UserSeedData) => {
        const result = await config.db
          .insert(usersTable)
          .values({
            id: user.id,
            email: user.email.toLowerCase().trim(),
            name: user.name,
            emailNotifications: true,
            createdAt: now,
            updatedAt: now,
            deleted: false,
          })
          .onConflictDoUpdate({
            target: usersTable.email,
            set: {
              name: user.name,
              emailNotifications: true,
              updatedAt: now,
              deleted: false,
            },
          })
          .returning({ id: usersTable.id })
          .then(([r]) => r?.id)

        if (!result) {
          throw new Error(`Failed to insert/update user: ${user.email}`)
        }
        return result
      },
      50,
    )
    userIds.push(...insertedUserIds)

    console.log(`✅ Successfully seeded ${userIds.length} users`)
    await seedRoles(config, collectionIds)
    return userIds
  } catch (error) {
    console.error('❌ Error in seedUsers:', error)
    throw error
  }
}

/**
 * Assigns roles to users in a deterministic way.
 * - PLATFORM_MANAGER roles are assigned to all collections
 * - Other roles are distributed evenly across collections
 */
async function seedRoles(
  config: SeedConfig,
  collectionIds: string[],
): Promise<void> {
  if (!predefinedUsers?.length) {
    console.log('⚠️ No users found to assign roles')
    return
  }

  console.log('🌱 Seeding user roles...')
  const now = new Date()
  let rolesAssigned = 0
  const errors: string[] = []

  try {
    const usersByRole = predefinedUsers.reduce<Record<Role, UserSeedData[]>>(
      (acc, user) => {
        if (!acc[user.role]) acc[user.role] = []
        acc[user.role].push(user)
        return acc
      },
      {} as Record<Role, UserSeedData[]>,
    )

    for (const [role, users] of Object.entries(usersByRole)) {
      const roleTyped = role as Role

      for (let i = 0; i < users.length; i++) {
        const user = users[i]
        if (!user) continue

        try {
          const collectionIdsToAssign =
            roleTyped === 'PLATFORM_MANAGER'
              ? [null, ...collectionIds]
              : [collectionIds[i % collectionIds.length]]

          for (const collectionId of collectionIdsToAssign) {
            const roleId = generateConsistentUUID(
              `role-${user.id}-${collectionId || 'platform'}`,
            )

            await config.db
              .insert(rolesTable)
              .values({
                id: roleId,
                userId: user.id,
                role: roleTyped,
                collectionId,
                createdAt: now,
                updatedAt: now,
                deleted: false,
              })
              .onConflictDoUpdate({
                target: [rolesTable.userId, rolesTable.collectionId],
                set: {
                  role: roleTyped,
                  updatedAt: now,
                  deleted: false,
                },
              })

            rolesAssigned++
          }
        } catch (error) {
          const errorMsg = `Error assigning role ${roleTyped} to user ${user.email}: ${error instanceof Error ? error.message : String(error)}`
          console.error(`❌ ${errorMsg}`)
          errors.push(errorMsg)
        }
      }
    }

    if (errors.length > 0) {
      console.warn(`⚠️ Completed with ${errors.length} errors`)
      throw new Error(`Failed to assign some roles: ${errors.join('; ')}`)
    }

    console.log(`✅ Successfully assigned ${rolesAssigned} roles`)
  } catch (error) {
    console.error('❌ Error in seedRoles:', error)
    throw error
  }
}
