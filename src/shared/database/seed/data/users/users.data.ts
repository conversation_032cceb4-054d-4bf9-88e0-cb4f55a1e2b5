import { generateConsistentUUID } from '../../seed.utils'

// ============================================
// Types
// ============================================

export type Role =
  | 'PLATFORM_MANAGER'
  | 'COLLECTION_MANAGER'
  | 'EDITOR'
  | 'CONTRIBUTOR'
  | 'VIEWER'

export interface UserSeedData {
  id: string
  name: string
  email: string
  role: Role
  collectionId?: string | null
}

// ============================================
// Constants
// ============================================

export const predefinedUsers: UserSeedData[] = [
  // Platform Manager (no collection)
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Platform Admin',
    email: '<EMAIL>',
    role: 'PLATFORM_MANAGER',
    collectionId: null,
  },
  // Collection Managers
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Maria Silva',
    email: '<EMAIL>',
    role: 'COLLECTION_MANAGER',
  },
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Carolina Ferreira',
    email: '<EMAIL>',
    role: 'COLLECTION_MANAGER',
  },
  // Editors
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'João Santos',
    email: '<EMAIL>',
    role: 'EDITOR',
  },
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Ricardo Almeida',
    email: '<EMAIL>',
    role: 'EDITOR',
  },
  // Contributors
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Ana Costa',
    email: '<EMAIL>',
    role: 'CONTRIBUTOR',
  },
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Sofia Rodrigues',
    email: '<EMAIL>',
    role: 'CONTRIBUTOR',
  },
  // Viewers
  {
    id: generateConsistentUUID('<EMAIL>'),
    name: 'Pedro Oliveira',
    email: '<EMAIL>',
    role: 'VIEWER',
  },
]
