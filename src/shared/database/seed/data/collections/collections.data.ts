import { generateConsistentUUID } from '../../seed.utils'

// ============================================
// Types
// ============================================

export interface CollectionSeedData {
  id: string
  name: string
  description?: string
  createdAt?: Date
  updatedAt?: Date
}

// ============================================
// Constants
// ============================================

export const predefinedCollections: CollectionSeedData[] = [
  {
    id: generateConsistentUUID('collection-marine-organisms-biobank'),
    name: 'Marine Organisms Biobank',
    description:
      'A comprehensive repository of marine biological specimens used for biodiversity and genetic research.',
  },
  {
    id: generateConsistentUUID('collection-algae-culture'),
    name: 'Algae Culture Collection',
    description:
      'A curated collection of live algae strains maintained for ecological, industrial, and pharmaceutical studies.',
  },
  {
    id: generateConsistentUUID('collection-fish-tissue'),
    name: 'Fish Tissue Repository',
    description:
      'An archive of fish tissue samples collected for molecular, genetic, and toxicological analyses.',
  },
  {
    id: generateConsistentUUID('collection-env-dna'),
    name: 'Environmental DNA Library',
    description:
      'A digital and physical resource containing DNA extracted from environmental samples for biodiversity monitoring.',
  },
  {
    id: generateConsistentUUID('collection-plankton'),
    name: 'Plankton Sample Archive',
    description:
      'A historical and contemporary reference of plankton specimens collected from diverse marine environments.',
  },
  {
    id: generateConsistentUUID('collection-coral-fragment'),
    name: 'Coral Fragment Bank',
    description:
      'A preservation bank of coral fragments supporting reef restoration and conservation research.',
  },
]
