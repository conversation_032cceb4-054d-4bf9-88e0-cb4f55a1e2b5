import { collectionsTable } from '../../../schema/collections'
import type { SeedConfig } from '../../seed.config'
import { batchProcess } from '../../seed.utils'
import { predefinedCollections } from './collections.data'

// ============================================
// Collection Seeding
// ============================================

/**
 * Seeds collections in the database
 */
export async function seedCollections(config: SeedConfig): Promise<string[]> {
  if (!predefinedCollections?.length) {
    console.log('⚠️ No predefined collections found')
    return []
  }

  console.log('🌱 Seeding collections...')
  const collectionIds: string[] = []
  const now = new Date()

  try {
    const insertedIds = await batchProcess(
      predefinedCollections,
      async (collection) => {
        try {
          const [inserted] = await config.db
            .insert(collectionsTable)
            .values({
              id: collection.id,
              name: collection.name.trim(),
              description: collection.description,
              createdAt: now,
              updatedAt: now,
              deleted: false,
            })
            .onConflictDoUpdate({
              target: collectionsTable.id,
              set: {
                name: collection.name.trim(),
                updatedAt: now,
                deleted: false,
              },
            })
            .returning({ id: collectionsTable.id })

          if (!inserted) {
            throw new Error(`Failed to upsert collection: ${collection.name}`)
          }
          return inserted.id
        } catch (error) {
          console.error(
            `❌ Error seeding collection ${collection.name}:`,
            error,
          )
          throw error
        }
      },
      10,
    )

    collectionIds.push(...insertedIds.filter((id): id is string => Boolean(id)))

    console.log(`✅ Successfully seeded ${collectionIds.length} collections`)

    return collectionIds
  } catch (error) {
    console.error('❌ Error in seedCollections:', error)
    throw error
  }
}
