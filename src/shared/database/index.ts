import { PGlite } from '@electric-sql/pglite'
import {
  drizzle as drizzleNodePostgres,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import { drizzle as drizzlePgLite, PgliteDatabase } from 'drizzle-orm/pglite'
import { Pool } from 'pg'

import { Environment } from '../infra/environment'
import * as schema from './schema'

export type DatabaseClient =
  | NodePgDatabase<typeof schema>
  | PgliteDatabase<typeof schema>

export interface IDatabaseWrapper {
  client: DatabaseClient
  close: () => Promise<void>
}

function createDatabaseClient(): IDatabaseWrapper {
  const environment = Environment.instance.get('ENVIRONMENT')
  const databaseUrl = Environment.instance.get('DATABASE_URL')

  if (environment === 'test') {
    const pgLiteClient = new PGlite()
    const db = drizzlePgLite({ client: pgLiteClient, schema })
    return {
      client: db,
      close: async () => {
        await pgLiteClient.close()
      },
    }
  } else {
    const pool = new Pool({ connectionString: databaseUrl })
    const db = drizzleNodePostgres({ client: pool, schema })
    return {
      client: db,
      close: async () => {
        await pool.end()
      },
    }
  }
}

const databaseWrapper = createDatabaseClient()
const database = databaseWrapper.client

export { database, databaseWrapper }
