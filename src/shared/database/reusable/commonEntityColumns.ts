import { sql } from 'drizzle-orm'
import { timestamp, uuid, boolean } from 'drizzle-orm/pg-core'

const commonEntityColumns = {
  id: uuid('id').primaryKey(),
  createdAt: timestamp('created_at')
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at').$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  deleted: boolean('deleted').notNull().default(false),
}

export { commonEntityColumns }
