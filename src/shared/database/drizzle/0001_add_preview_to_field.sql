ALTER TABLE "fields" ALTER COLUMN "type" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."field_type";--> statement-breakpoint
CREATE TYPE "public"."field_type" AS ENUM('TEXT', 'TEXTAREA', 'NUMBER', 'SELECT', 'SELECT-MANY', 'IMAGE-UPLOAD');--> statement-breakpoint
ALTER TABLE "fields" ALTER COLUMN "type" SET DATA TYPE "public"."field_type" USING "type"::"public"."field_type";--> statement-breakpoint
ALTER TABLE "fields" ADD COLUMN "preview" boolean NOT NULL;