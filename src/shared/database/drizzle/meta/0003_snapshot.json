{"id": "5eac6c65-38e6-4c34-8211-06086286cdba", "prevId": "919037d5-8c4a-4785-884c-22a40e86f08f", "version": "7", "dialect": "postgresql", "tables": {"public.activities": {"name": "activities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"activities_user_id_users_id_fk": {"name": "activities_user_id_users_id_fk", "tableFrom": "activities", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "activities_collection_id_collections_id_fk": {"name": "activities_collection_id_collections_id_fk", "tableFrom": "activities", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "activity_id": {"name": "activity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "read": {"name": "read", "type": "boolean", "primaryKey": false, "notNull": true}, "sent": {"name": "sent", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "notifications_activity_id_activities_id_fk": {"name": "notifications_activity_id_activities_id_fk", "tableFrom": "notifications", "tableTo": "activities", "columnsFrom": ["activity_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collection_field_groups": {"name": "collection_field_groups", "schema": "", "columns": {"collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": true}, "field_group_id": {"name": "field_group_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"collection_field_groups_collection_id_collections_id_fk": {"name": "collection_field_groups_collection_id_collections_id_fk", "tableFrom": "collection_field_groups", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "collection_field_groups_field_group_id_field_groups_id_fk": {"name": "collection_field_groups_field_group_id_field_groups_id_fk", "tableFrom": "collection_field_groups", "tableTo": "field_groups", "columnsFrom": ["field_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"collection_field_groups_collection_id_field_group_id_pk": {"name": "collection_field_groups_collection_id_field_group_id_pk", "columns": ["collection_id", "field_group_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collections": {"name": "collections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.field_groups": {"name": "field_groups", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fields": {"name": "fields", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "field_group_id": {"name": "field_group_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "field_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "maxLength": {"name": "max<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "values": {"name": "values", "type": "jsonb", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "mandatory": {"name": "mandatory", "type": "boolean", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "visibility", "typeSchema": "public", "primaryKey": false, "notNull": true}, "preview": {"name": "preview", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"fields_field_group_id_field_groups_id_fk": {"name": "fields_field_group_id_field_groups_id_fk", "tableFrom": "fields", "tableTo": "field_groups", "columnsFrom": ["field_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.edits": {"name": "edits", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "entry_id": {"name": "entry_id", "type": "uuid", "primaryKey": false, "notNull": true}, "editor_id": {"name": "editor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reviewer_id": {"name": "reviewer_id", "type": "uuid", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reviewer_message": {"name": "reviewer_message", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"edits_entry_id_entries_id_fk": {"name": "edits_entry_id_entries_id_fk", "tableFrom": "edits", "tableTo": "entries", "columnsFrom": ["entry_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "edits_editor_id_users_id_fk": {"name": "edits_editor_id_users_id_fk", "tableFrom": "edits", "tableTo": "users", "columnsFrom": ["editor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "edits_reviewer_id_users_id_fk": {"name": "edits_reviewer_id_users_id_fk", "tableFrom": "edits", "tableTo": "users", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.entries": {"name": "entries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "visibility", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"entries_collection_id_collections_id_fk": {"name": "entries_collection_id_collections_id_fk", "tableFrom": "entries", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "entries_creator_id_users_id_fk": {"name": "entries_creator_id_users_id_fk", "tableFrom": "entries", "tableTo": "users", "columnsFrom": ["creator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invites": {"name": "invites", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "invite_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"invites_collection_id_collections_id_fk": {"name": "invites_collection_id_collections_id_fk", "tableFrom": "invites", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"roles_user_id_users_id_fk": {"name": "roles_user_id_users_id_fk", "tableFrom": "roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "roles_collection_id_collections_id_fk": {"name": "roles_collection_id_collections_id_fk", "tableFrom": "roles", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_collection_role_unique_idx": {"name": "user_collection_role_unique_idx", "nullsNotDistinct": true, "columns": ["user_id", "collection_id"]}}, "policies": {}, "checkConstraints": {"collection_id_role_check": {"name": "collection_id_role_check", "value": "(\"roles\".\"collection_id\" IS NOT NULL) OR (\"roles\".\"role\" = 'PLATFORM_MANAGER')"}}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_notifications": {"name": "email_notifications", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "availability": {"name": "availability", "type": "boolean", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"services_collection_id_collections_id_fk": {"name": "services_collection_id_collections_id_fk", "tableFrom": "services", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.field_type": {"name": "field_type", "schema": "public", "values": ["TEXT", "TEXTAREA", "NUMBER", "SELECT", "SELECT-MANY", "IMAGE-UPLOAD"]}, "public.invite_status": {"name": "invite_status", "schema": "public", "values": ["PENDING", "EXPIRED", "REGISTERED", "CANCELLED"]}, "public.role": {"name": "role", "schema": "public", "values": ["PLATFORM_MANAGER", "COLLECTION_MANAGER", "EDITOR", "CONTRIBUTOR", "VIEWER"]}, "public.status": {"name": "status", "schema": "public", "values": ["PENDING", "ACCEPTED", "REJECTED"]}, "public.visibility": {"name": "visibility", "schema": "public", "values": ["PUBLIC", "PRIVATE"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}