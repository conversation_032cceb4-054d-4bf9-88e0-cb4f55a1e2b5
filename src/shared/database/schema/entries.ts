import { relations } from 'drizzle-orm'
import { jsonb, pgTable, uuid, varchar } from 'drizzle-orm/pg-core'
import { commonEntityColumns } from '../reusable/commonEntityColumns'
import { collectionsTable } from './collections'
import { statusEnum, visibilityEnum } from './enums'
import { usersTable } from './users'

const entriesTable = pgTable('entries', {
  ...commonEntityColumns,
  collectionId: uuid('collection_id')
    .references(() => collectionsTable.id)
    .notNull(),
  creatorId: uuid('creator_id')
    .references(() => usersTable.id)
    .notNull(),
  data: jsonb('data').notNull(),
  visibility: visibilityEnum().notNull(),
})

const editsTable = pgTable('edits', {
  ...commonEntityColumns,
  entryId: uuid('entry_id')
    .references(() => entriesTable.id)
    .notNull(),
  editorId: uuid('editor_id').references(() => usersTable.id),
  reviewerId: uuid('reviewer_id').references(() => usersTable.id),
  data: jsonb('data').notNull(),
  status: statusEnum().notNull(),
  reviewerMessage: varchar('reviewer_message', { length: 255 }),
})

// --- RELATIONS ---

const entriesRelations = relations(entriesTable, ({ one, many }) => ({
  collection: one(collectionsTable, {
    fields: [entriesTable.collectionId],
    references: [collectionsTable.id],
  }),
  creator: one(usersTable, {
    fields: [entriesTable.creatorId],
    references: [usersTable.id],
    relationName: 'createdEntries',
  }),
  edits: many(editsTable),
}))

const editsRelations = relations(editsTable, ({ one }) => ({
  entry: one(entriesTable, {
    fields: [editsTable.entryId],
    references: [entriesTable.id],
  }),
  editor: one(usersTable, {
    fields: [editsTable.editorId],
    references: [usersTable.id],
    relationName: 'editedEntries',
  }),
  reviewer: one(usersTable, {
    fields: [editsTable.reviewerId],
    references: [usersTable.id],
    relationName: 'reviewedEntries',
  }),
}))

export { entriesTable, editsTable, entriesRelations, editsRelations }
