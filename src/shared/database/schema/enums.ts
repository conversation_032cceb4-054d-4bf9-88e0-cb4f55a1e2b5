import { pgEnum } from 'drizzle-orm/pg-core'

const roleEnum = pgEnum('role', [
  'PLATFORM_MANAGER',
  'COLLECTION_MANAGER',
  'EDITOR',
  'CONTRIBUTOR',
  'VIEWER',
])

const visibilityEnum = pgEnum('visibility', ['PUBLIC', 'PRIVATE'])

const statusEnum = pgEnum('status', ['PENDING', 'ACCEPTED', 'REJECTED'])

const inviteStatusEnum = pgEnum('invite_status', [
  'PENDING',
  'EXPIRED',
  'REGISTERED',
  'CANCELLED',
])

const fieldTypesEnum = pgEnum('field_type', [
  'TEXT',
  'TEXTAREA',
  'NUMBER',
  'SELECT',
  'SELECT-MANY',
  'IMAGE-UPLOAD',
])

export {
  roleEnum,
  visibilityEnum,
  statusEnum,
  fieldTypesEnum,
  inviteStatusEnum,
}
