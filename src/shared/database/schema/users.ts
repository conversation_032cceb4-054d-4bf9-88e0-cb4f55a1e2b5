import { relations, sql } from 'drizzle-orm'
import {
  uuid,
  varchar,
  boolean,
  pgTable,
  check,
  unique,
} from 'drizzle-orm/pg-core'
import { commonEntityColumns } from '../reusable/commonEntityColumns'
import { activitiesTable, notificationsTable } from './activities'
import { collectionsTable } from './collections'
import { entriesTable, editsTable } from './entries'
import { inviteStatusEnum, roleEnum } from './enums'

const usersTable = pgTable('users', {
  ...commonEntityColumns,
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  emailNotifications: boolean('email_notifications').notNull(), // TODO: pode ser uma tabela separada para as preferências ou jsonb preferences column caso justifique
})

const rolesTable = pgTable(
  'roles',
  {
    ...commonEntityColumns,
    userId: uuid('user_id')
      .notNull()
      .references(() => usersTable.id),
    collectionId: uuid('collection_id').references(() => collectionsTable.id),
    role: roleEnum().notNull(),
  },
  (table) => [
    check(
      'collection_id_role_check',
      sql`(${table.collectionId} IS NOT NULL) OR (${table.role} = 'PLATFORM_MANAGER')`,
    ),
    unique('user_collection_role_unique_idx')
      .on(table.userId, table.collectionId)
      .nullsNotDistinct(),
  ],
)

const invitesTable = pgTable('invites', {
  ...commonEntityColumns,
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  collectionId: uuid('collection_id').references(() => collectionsTable.id),
  status: inviteStatusEnum().notNull(),
  role: roleEnum().notNull(),
})

// --- RELATIONS ---

const usersRelations = relations(usersTable, ({ many }) => ({
  assignments: many(rolesTable),
  createdEntries: many(entriesTable, { relationName: 'createdEntries' }),
  editedEntries: many(editsTable, { relationName: 'editedEntries' }),
  reviewedEntries: many(editsTable, { relationName: 'reviewedEntries' }),
  activities: many(activitiesTable),
  notifications: many(notificationsTable),
}))

const rolesRelations = relations(rolesTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [rolesTable.userId],
    references: [usersTable.id],
  }),
  collection: one(collectionsTable, {
    fields: [rolesTable.collectionId],
    references: [collectionsTable.id],
  }),
}))

const invitesRelations = relations(invitesTable, ({ one }) => ({
  collection: one(collectionsTable, {
    fields: [invitesTable.collectionId],
    references: [collectionsTable.id],
  }),
}))

export {
  usersTable,
  rolesTable,
  invitesTable,
  usersRelations,
  rolesRelations,
  invitesRelations,
}
