import { relations } from 'drizzle-orm'
import { boolean, jsonb, pgTable, uuid, varchar } from 'drizzle-orm/pg-core'
import { commonEntityColumns } from '../reusable/commonEntityColumns'
import { collectionsTable } from './collections'
import { usersTable } from './users'

const activitiesTable = pgTable('activities', {
  ...commonEntityColumns,
  userId: uuid('user_id').references(() => usersTable.id),
  collectionId: uuid('collection_id').references(() => collectionsTable.id),
  type: varchar('type', { length: 255 }).notNull(), // TODO: Enum?
  data: jsonb('data').notNull(),
})

const notificationsTable = pgTable('notifications', {
  ...commonEntityColumns,
  userId: uuid('user_id').references(() => usersTable.id),
  activityId: uuid('activity_id').references(() => activitiesTable.id),
  read: boolean('read').notNull(),
  sent: boolean('sent').notNull(),
})

// --- RELATIONS ---

const activitiesRelations = relations(activitiesTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [activitiesTable.userId],
    references: [usersTable.id],
  }),
  notifications: many(notificationsTable),
  collection: one(collectionsTable, {
    fields: [activitiesTable.collectionId],
    references: [collectionsTable.id],
  }),
}))

const notificationsRelations = relations(notificationsTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [notificationsTable.userId],
    references: [usersTable.id],
  }),
  activity: one(activitiesTable, {
    fields: [notificationsTable.activityId],
    references: [activitiesTable.id],
  }),
}))

export {
  activitiesTable,
  notificationsTable,
  activitiesRelations,
  notificationsRelations,
}
