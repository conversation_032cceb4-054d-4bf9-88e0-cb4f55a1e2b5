import { relations } from 'drizzle-orm'
import {
  integer,
  jsonb,
  pgTable,
  primaryKey,
  uuid,
  varchar,
  text,
  boolean,
} from 'drizzle-orm/pg-core'
import { commonEntityColumns } from '../reusable/commonEntityColumns'
import { entriesTable } from './entries'
import { fieldTypesEnum, visibilityEnum } from './enums'
import { servicesTable } from './services'
import { rolesTable, invitesTable } from './users'

const collectionsTable = pgTable('collections', {
  ...commonEntityColumns,
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
})

const fieldGroupsTable = pgTable('field_groups', {
  ...commonEntityColumns,
  name: varchar('name', { length: 255 }).notNull(),
  position: integer('position').notNull(),
})

const fieldsTable = pgTable('fields', {
  ...commonEntityColumns,
  fieldGroupId: uuid('field_group_id')
    .notNull()
    .references(() => fieldGroupsTable.id),
  name: varchar('name', { length: 255 }).notNull(),
  type: fieldTypesEnum().notNull(),
  maxLength: integer('maxLength'),
  values: jsonb('values'),
  position: integer('position').notNull(),
  mandatory: boolean('mandatory').notNull(),
  visibility: visibilityEnum().notNull(),
  preview: boolean().notNull(),
})

const collectionFieldGroupsTable = pgTable(
  'collection_field_groups',
  {
    collectionId: uuid('collection_id')
      .notNull()
      .references(() => collectionsTable.id),
    fieldGroupId: uuid('field_group_id')
      .notNull()
      .references(() => fieldGroupsTable.id),
  },
  (table) => [
    primaryKey({ columns: [table.collectionId, table.fieldGroupId] }),
  ],
)

// --- RELATIONS ---

const collectionsRelations = relations(collectionsTable, ({ many }) => ({
  assignments: many(rolesTable),
  services: many(servicesTable),
  collectionToFieldGroups: many(collectionFieldGroupsTable),
  invites: many(invitesTable),
  entries: many(entriesTable),
}))

const fieldGroupsRelations = relations(fieldGroupsTable, ({ many }) => ({
  fields: many(fieldsTable),
  fieldGroupToCollections: many(collectionFieldGroupsTable),
}))

const fieldsRelations = relations(fieldsTable, ({ one }) => ({
  fieldGroup: one(fieldGroupsTable, {
    fields: [fieldsTable.fieldGroupId],
    references: [fieldGroupsTable.id],
  }),
}))

const collectionFieldGroupsRelations = relations(
  collectionFieldGroupsTable,
  ({ one }) => ({
    collection: one(collectionsTable, {
      fields: [collectionFieldGroupsTable.collectionId],
      references: [collectionsTable.id],
    }),
    fieldGroup: one(fieldGroupsTable, {
      fields: [collectionFieldGroupsTable.fieldGroupId],
      references: [fieldGroupsTable.id],
    }),
  }),
)

export {
  collectionsTable,
  fieldGroupsTable,
  fieldsTable,
  collectionFieldGroupsTable,
  collectionsRelations,
  fieldGroupsRelations,
  fieldsRelations,
  collectionFieldGroupsRelations,
}
