import { relations } from 'drizzle-orm'
import { boolean, pgTable, uuid, varchar } from 'drizzle-orm/pg-core'
import { commonEntityColumns } from '../reusable/commonEntityColumns'
import { collectionsTable } from './collections'

const servicesTable = pgTable('services', {
  ...commonEntityColumns,
  collectionId: uuid('collection_id')
    .notNull()
    .references(() => collectionsTable.id),
  name: varchar('name', { length: 255 }).notNull(),
  description: varchar('description', { length: 255 }).notNull(),
  category: varchar('category', { length: 255 }).notNull(),
  availability: boolean('availability').notNull(),
  link: varchar('link', { length: 255 }).notNull(),
})

// --- RELATIONS ---

const servicesRelations = relations(servicesTable, ({ one }) => ({
  collection: one(collectionsTable, {
    fields: [servicesTable.collectionId],
    references: [collectionsTable.id],
  }),
}))

export { servicesTable, servicesRelations }
