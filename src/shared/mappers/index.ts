import { PaginatedResponseDTO } from '../dto/paginatedResponseDTO'
import { PaginatedDomain } from '../types/pagination'

interface Mapper<TDomain, TModel, TDto = never, TToDTOOptions = never> {
  toDomain: (model: TModel) => TDomain
  toModel: (domain: TDomain) => TModel
  toDTO?: (
    domain: TDomain,
    options: TToDTOOptions extends object
      ? { [key in keyof TToDTOOptions]: TToDTOOptions[key] }
      : never,
  ) => TDto
  toPageDTO?: (domain: PaginatedDomain<TDomain>) => PaginatedResponseDTO<TDto>
}

export { type Mapper }
