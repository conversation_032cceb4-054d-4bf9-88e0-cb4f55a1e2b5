import { userEventHandlers } from './modules/users/handlers'
import { databaseWrapper } from './shared/database'
import { domainEvents } from './shared/domain/events'
import { Environment } from './shared/infra/environment'
import { startHTTPServer, stopHTTPServer } from './shared/infra/http'
import { logger } from './shared/infra/logger'
import { initializeAllServices } from './shared/services'

async function shutdown() {
  await stopHTTPServer()
  await databaseWrapper.close()
  process.exit(0)
}

async function unhandledShutdown() {
  await stopHTTPServer()
  await databaseWrapper.close()
  process.exit(1)
}

async function main() {
  await initializeAllServices()

  // Bootstrap domain event handlers
  domainEvents.bootstrap([
    ...userEventHandlers,
    // Add other module handler initializers here in the future
  ])

  const environment = Environment.instance.get('ENVIRONMENT')

  process.on('SIGINT', shutdown)
  process.on('SIGTERM', shutdown)

  process.on('uncaughtException', (error, data) => {
    if (error && error instanceof Error) {
      logger.error('Uncaught Exception', { error, data })
    }

    unhandledShutdown()
  })

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Promise Rejection:', { reason, promise })
    unhandledShutdown()
  })

  logger.info(`Application started (environment: ${environment})`)
  startHTTPServer()
}

main()
