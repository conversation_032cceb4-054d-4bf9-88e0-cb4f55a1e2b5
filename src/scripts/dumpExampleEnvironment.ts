import { writeFileSync } from 'fs'
import { isEmpty } from 'lodash'
import { SchemaDescription } from 'yup'
import { schema, TEnvironmentSchema } from '../shared/infra/environment/schema'

/**
 * Dumps an `.env.example` file based on the schema defined in `src/shared/utils/environment/schema.ts`.
 */
const main = () => {
  const fieldsDescription = schema.describe().fields as Record<
    keyof TEnvironmentSchema,
    SchemaDescription
  >

  const data = Object.entries(fieldsDescription)
    .map(
      ([key, description]) =>
        `${key}= # type:${description.type} ${isEmpty(description.oneOf) ? '' : `oneOf:${JSON.stringify(description.oneOf)}`}`,
    )
    .join('\n')

  // Write the `.env.example` file
  writeFileSync('.env.example', data, { encoding: 'utf-8' })
}

main()
