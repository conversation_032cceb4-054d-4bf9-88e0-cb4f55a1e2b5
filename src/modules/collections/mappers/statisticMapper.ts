import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Mapper } from '../../../shared/mappers'
import { Statistic as Domain } from '../domain/statistic'
import { StatisticDTO as DTO } from '../dto/statisticDTO'

type Model = {
  collectionId: string
  members: number
  entries: number
  publicEntries: number
}

class StatisticMapper implements Mapper<Domain, Model, DTO> {
  private static _instance: StatisticMapper

  private constructor() {}

  toDomain(model: Model): Domain {
    return new Domain({
      collectionId: new UniqueEntityID(model.collectionId),
      members: Number(model.members),
      entries: {
        total: Number(model.entries),
        public: Number(model.publicEntries),
      },
    })
  }
  toModel(domain: Domain): Model {
    throw new Error('Method not implemented.')
  }

  toDTO(domain: Domain): DTO {
    return {
      collectionId: domain.collectionId.value,
      entries: domain.entries,
      members: domain.members,
    }
  }

  static getInstance() {
    if (!StatisticMapper._instance) {
      StatisticMapper._instance = new StatisticMapper()
    }
    return StatisticMapper._instance
  }
}

export { StatisticMapper }
