import { Link } from '../../../shared/domain/link'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Service as Domain } from '../domain/service'
import { ServiceDTO as DTO } from '../dto/serviceDTO'
import { ServiceModel } from '../models/service.model'

class ServiceMapper implements Mapper<Domain, ServiceModel, DTO> {
  private static _instance: ServiceMapper

  toDomain(model: ServiceModel): Domain {
    return new Domain(
      {
        collectionId: new UniqueEntityID(model.collectionId),
        name: model.name,
        description: model.description,
        category: model.category,
        availability: model.availability,
        link: new Link(model.link),
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt || undefined,
      model.deleted,
    )
  }

  toModel(domain: Domain): ServiceModel {
    return {
      id: domain.id.value,
      collectionId: domain.collectionId.value,
      name: domain.name,
      description: domain.description,
      category: domain.category,
      availability: domain.availability,
      link: domain.link.value.toString(),
      deleted: domain.deleted,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt || null,
    }
  }

  toDTO(domain: Domain): DTO {
    return {
      collectionId: domain.collectionId.value,
      id: domain.id.value,
      name: domain.name,
      description: domain.description,
      availability: domain.availability,
      category: domain.category,
      link: domain.link.value.toString(),
      deleted: domain.deleted,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
    }
  }

  toPageDTO(domain: PaginatedDomain<Domain>): PaginatedResponseDTO<DTO> {
    return {
      items: domain.items.map(this.toDTO),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!ServiceMapper._instance) {
      ServiceMapper._instance = new ServiceMapper()
    }
    return ServiceMapper._instance
  }
}

export { ServiceMapper }
