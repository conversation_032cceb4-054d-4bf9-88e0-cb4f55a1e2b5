import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Mapper } from '../../../shared/mappers'
import { FieldGroup as Domain } from '../domain/fieldGroup'
import { Fields } from '../domain/fields'
import { FieldGroupDTO as DTO } from '../dto/fieldGroupDTO'
import { FieldGroupModel } from '../models/fieldGroup.model'
import { FieldMapper } from './fieldMapper'

class FieldGroupMapper implements Mapper<Domain, FieldGroupModel, DTO> {
  private static _instance: FieldGroupMapper

  private constructor() {}

  toDomain(model: FieldGroupModel): Domain {
    const fields = model.fields
      ? new Fields(
          model.fields?.map((field) =>
            FieldMapper.getInstance().toDomain(field),
          ),
        )
      : undefined

    const domainProps = {
      name: model.name,
      position: model.position,
      fields,
    }

    return new Domain(
      domainProps,
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt || undefined,
      model.deleted,
    )
  }

  toModel(domain: Domain): FieldGroupModel {
    const fields = domain.fields
      ?.getItems()
      .map((field) => FieldMapper.getInstance().toModel(field))

    return {
      id: domain.id.value,
      name: domain.name,
      position: domain.position,
      fields,
      deleted: domain.deleted,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt || null,
    }
  }

  toDTO(domain: Domain): DTO {
    const fields = domain.fields
      ?.getItems()
      .map((field) => FieldMapper.getInstance().toDTO(field))

    return {
      id: domain.id.value,
      name: domain.name,
      position: domain.position,
      fields,
      deleted: domain.deleted,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
    }
  }

  static getInstance() {
    if (!FieldGroupMapper._instance) {
      FieldGroupMapper._instance = new FieldGroupMapper()
    }
    return FieldGroupMapper._instance
  }
}

export { FieldGroupMapper }
