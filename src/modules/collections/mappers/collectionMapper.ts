import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Mapper } from '../../../shared/mappers'
import { Collection as Domain } from '../domain/collection'
import { FieldGroups } from '../domain/fieldGroups'
import { Services } from '../domain/services'
import { CollectionDTO as DTO } from '../dto/collectionDTO'
import { CollectionModel as Model } from '../models/collection.model'
import { FieldGroupMapper } from './fieldGroupMapper'
import { ServiceMapper } from './serviceMapper'

class CollectionMapper implements Mapper<Domain, Model, DTO> {
  private static _instance: CollectionMapper

  toDomain(model: Model): Domain {
    const services = model.services
      ? new Services(
          model.services.map((service) =>
            ServiceMapper.getInstance().toDomain(service),
          ),
        )
      : undefined

    const fieldGroups = model.collectionToFieldGroups
      ? new FieldGroups(
          model.collectionToFieldGroups
            ?.filter((fieldGroup) => fieldGroup.fieldGroup)
            .map((fieldGroup) =>
              FieldGroupMapper.getInstance().toDomain(fieldGroup.fieldGroup!),
            ),
        )
      : undefined

    const collectionDomain = new Domain(
      {
        name: model.name,
        services,
        fieldGroups,
        description: model.description ?? undefined,
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt || undefined,
      model.deleted,
    )

    return collectionDomain
  }

  toModel(domain: Domain): Model {
    const services = domain.services
      ?.getItems()
      .map((service) => ServiceMapper.getInstance().toModel(service))

    const collectionToFieldGroups = domain.fieldGroups
      ?.getItems()
      .map((fieldGroup) => {
        return {
          collectionId: domain.id.value,
          fieldGroupId: fieldGroup.id.value,
          fieldGroup: FieldGroupMapper.getInstance().toModel(fieldGroup),
        }
      })

    return {
      id: domain.id.value,
      name: domain.name,
      description: domain.description ?? null,
      services,
      collectionToFieldGroups,
      deleted: domain.deleted,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
    }
  }

  toDTO(domain: Domain): DTO {
    const services = domain.services
      ?.getItems()
      .map((service) => ServiceMapper.getInstance().toDTO(service))

    const fieldGroups = domain.fieldGroups
      ?.getItems()
      .map((fieldGroup) => FieldGroupMapper.getInstance().toDTO(fieldGroup))

    return {
      id: domain.id.value,
      name: domain.name,
      description: domain.description,
      services,
      fieldGroups,
      deleted: domain.deleted,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
    }
  }

  static getInstance() {
    if (!CollectionMapper._instance) {
      CollectionMapper._instance = new CollectionMapper()
    }
    return CollectionMapper._instance
  }
}

export { CollectionMapper }
