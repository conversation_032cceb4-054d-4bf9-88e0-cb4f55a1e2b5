import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Mapper } from '../../../shared/mappers'
import { Field as Domain, FieldProps, FieldOption } from '../domain/field'
import { FieldDTO as DTO } from '../dto/fieldDTO'
import { FieldModel } from '../models/field.model'

class FieldMapper implements Mapper<Domain, FieldModel, DTO> {
  private static _instance: FieldMapper

  private constructor() {}

  toDomain(model: FieldModel): Domain {
    let domainProps: FieldProps

    const commonProps = {
      name: model.name,
      maxLength: model.maxLength ?? undefined,
      position: model.position,
      mandatory: model.mandatory,
      visibility: model.visibility,
      preview: model.preview,
    }

    if (model.type === 'SELECT' || model.type === 'SELECT-MANY') {
      domainProps = {
        ...commonProps,
        type: model.type,
        options: (model.values as FieldOption[] | undefined | null) ?? [],
        fieldGroupId: new UniqueEntityID(model.fieldGroupId),
      }
    } else {
      domainProps = {
        ...commonProps,
        type: model.type,
        fieldGroupId: new UniqueEntityID(model.fieldGroupId),
      }
    }

    return new Domain(
      domainProps,
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt || undefined,
      model.deleted,
    )
  }

  toModel(domain: Domain): FieldModel {
    return {
      id: domain.id.value,
      fieldGroupId: domain.fieldGroupId.value,
      name: domain.name,
      type: domain.type,
      maxLength: domain.maxLength ?? null,
      values: domain.options ?? null,
      position: domain.position,
      mandatory: domain.mandatory,
      visibility: domain.visibility,
      preview: domain.preview,
      deleted: domain.deleted,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt || null,
    }
  }

  toDTO(domain: Domain): DTO {
    return {
      id: domain.id.value,
      fieldGroupId: domain.fieldGroupId.value,
      name: domain.name,
      position: domain.position,
      maxLength: domain.maxLength,
      mandatory: domain.mandatory,
      visibility: domain.visibility,
      type: domain.type,
      options: domain.options ?? [],
      preview: domain.preview,
      deleted: domain.deleted,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
    }
  }

  static getInstance() {
    if (!FieldMapper._instance) {
      FieldMapper._instance = new FieldMapper()
    }
    return FieldMapper._instance
  }
}

export { FieldMapper }
