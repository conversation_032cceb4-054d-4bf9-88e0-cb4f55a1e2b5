import { GenericModel } from '../../../shared/models/generic.model'
import { RoleModel } from '../../users/models/role.model'
import { FieldGroupModel } from './fieldGroup.model'
import { ServiceModel } from './service.model'

// Define the junction table model
export interface CollectionFieldGroupModel {
  collectionId: string
  fieldGroupId: string
  fieldGroup?: FieldGroupModel
}

export interface CollectionModel extends GenericModel {
  name: string
  description: string | null
  assignments?: RoleModel[]
  services?: ServiceModel[]
  collectionToFieldGroups?: CollectionFieldGroupModel[]
}
