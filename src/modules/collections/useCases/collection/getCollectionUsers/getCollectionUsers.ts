import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { QueryDTO } from '../../../../../shared/dto/queryDTO'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { UserDTO } from '../../../../users/dto/userDTO'
import { UserMapper } from '../../../../users/mappers'
import { GenericUserRepository } from '../../../../users/repositories/interfaces/genericUserRepository'
import { GetCollectionUsersDTO } from './getCollectionUsers.dto'

class GetCollectionUsers extends UseCase<
  GetCollectionUsersDTO,
  PaginatedResponseDTO<UserDTO>
> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(GetCollectionUsers.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const hasRoleList = [
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER'),
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId),
    ]

    const hasPermission = hasRoleList.some((hasRole) => hasRole === true)

    if (hasPermission) return

    throw new NotAllowed(GetCollectionUsers.name)
  }

  private getCollectionUsers(collectionId: string, queryParams?: QueryDTO) {
    const id = new UniqueEntityID(collectionId)
    return this._userRepository.getAllByCollectionId(id, queryParams)
  }

  override async execute(
    request: Readonly<GetCollectionUsersDTO>,
  ): Promise<PaginatedResponseDTO<UserDTO>> {
    const collectionId = new UniqueEntityID(request.collectionId)

    this.checkPermissions(collectionId)

    const result = await this.getCollectionUsers(request.collectionId, request)

    return UserMapper.getInstance().toPageDTO(result)
  }
}

export { GetCollectionUsers }
