import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { QueryDTO } from '../../../../../shared/dto/queryDTO'
import { Controller } from '../../../../../shared/infra/controller'
import { QuerySchema } from '../../../../../shared/types/query'
import { UserDTO } from '../../../../users/dto/userDTO'
import { GetCollectionUsers } from './getCollectionUsers'
import { GetCollectionUsersDTO } from './getCollectionUsers.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  query: QuerySchema,
})

class GetCollectionUsersController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryDTO,
  GetCollectionUsersDTO,
  PaginatedResponseDTO<UserDTO>
> {
  public constructor(useCase: GetCollectionUsers) {
    super(
      'get',
      ':collectionId/users',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): GetCollectionUsersDTO {
    const queryParams = QuerySchema.validateSync(request.query)

    return { collectionId: request.params.collectionId, ...queryParams }
  }
}

export { GetCollectionUsersController }
