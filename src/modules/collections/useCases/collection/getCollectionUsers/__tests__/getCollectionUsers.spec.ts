import { stubEmail } from '../../../../../../shared/domain/__stubs__/email.stub'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../repositories'
import { GetCollectionUsers } from '../getCollectionUsers'
import { GetCollectionUsersDTO } from '../getCollectionUsers.dto'

describe('GetCollectionUsers Use Case', () => {
  const getCollectionUsersUseCase = new GetCollectionUsers(userRepository)
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should return all users for a given collection', async () => {
    const collection = stubCollection({ name: 'Test Collection' })
    await collectionRepository.save(collection)

    const user1 = stubUser({
      name: 'User 1',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    const user2 = stubUser({
      name: 'User 2',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    const assignment1 = stubUserAssignment({
      userId: user1.id,
      collectionId: collection.id,
      role: 'COLLECTION_MANAGER',
    })
    const assignment2 = stubUserAssignment({
      userId: user2.id,
      collectionId: collection.id,
      role: 'EDITOR',
    })
    user1.userAssignments.push(assignment1)
    user2.userAssignments.push(assignment2)

    await userRepository.save(user1)
    await userRepository.save(user2)

    const getCollectionUsersDTO: GetCollectionUsersDTO = {
      collectionId: collection.id.value,
    }

    const resultDTO = await execute(
      getCollectionUsersUseCase,
      getCollectionUsersDTO,
      user,
    )

    expect(Array.isArray(resultDTO.items)).toBe(true)
    expect(resultDTO.items.length).toBeGreaterThanOrEqual(2)
    const names = resultDTO.items.map((u) => u.name)
    expect(names).toEqual(expect.arrayContaining(['User 1', 'User 2']))
  })

  it('should return an empty array if collection has no users', async () => {
    const collection = stubCollection({ name: 'Empty Collection' })
    await collectionRepository.save(collection)

    const getCollectionUsersDTO: GetCollectionUsersDTO = {
      collectionId: collection.id.value,
    }

    const resultDTO = await execute(
      getCollectionUsersUseCase,
      getCollectionUsersDTO,
      user,
    )

    expect(Array.isArray(resultDTO.items)).toBe(true)
    expect(resultDTO.items.length).toBe(0)
  })
})
