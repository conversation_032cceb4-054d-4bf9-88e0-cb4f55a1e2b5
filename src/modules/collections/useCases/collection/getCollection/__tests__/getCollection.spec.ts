import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../domain/__stubs__/field.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import { stubFields } from '../../../../domain/__stubs__/fields.stub'
import { Collection } from '../../../../domain/collection'
import { FieldGroup } from '../../../../domain/fieldGroup'
import { collectionRepository } from '../../../../repositories'
import { GetCollection } from '../getCollection'
import { GetCollectionInputDTO } from '../getCollection.dto'

describe('GetCollection Use Case', () => {
  const getCollectionUseCase = new GetCollection(collectionRepository)
  let user: User
  let testCollection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    testCollection = stubCollection({
      name: 'Test Collection',
      fieldGroups: stubFieldGroups(),
    })

    const testFieldGroup = new FieldGroup({
      name: 'Test Field Group',
      fields: stubFields(),
      position: 0,
    })

    const testField = stubSingleValueField({
      fieldGroupId: testFieldGroup.id,
    })

    testFieldGroup.fields?.add(testField)
    testCollection.fieldGroups?.add(testFieldGroup)

    await collectionRepository.save(testCollection)
  })
  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should return collection DTO if collection exists', async () => {
    const getCollectionDTO: GetCollectionInputDTO = {
      id: testCollection.id.value,
    }

    const resultDTO = await execute(
      getCollectionUseCase,
      getCollectionDTO,
      user,
    )

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(testCollection.id.value)
    expect(resultDTO.name).toBe(testCollection.name)
    expect(resultDTO.deleted).toBe(testCollection.deleted)
  })

  it('should throw EntityNotFound if collection does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const getCollectionDTO: GetCollectionInputDTO = {
      id: nonExistentId,
    }

    await expect(
      execute(getCollectionUseCase, getCollectionDTO, user),
    ).rejects.toThrow(new EntityNotFound('Collection', nonExistentId))
  })
})
