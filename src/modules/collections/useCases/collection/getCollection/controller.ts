import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { CollectionDTO } from '../../../dto/collectionDTO'
import { GetCollection } from './getCollection'
import { GetCollectionInputDTO } from './getCollection.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
})

class GetCollectionController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetCollectionInputDTO,
  CollectionDTO
> {
  public constructor(useCase: GetCollection) {
    super('get', ':collectionId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): GetCollectionInputDTO {
    return { id: request.params.collectionId }
  }
}

export { GetCollectionController }
