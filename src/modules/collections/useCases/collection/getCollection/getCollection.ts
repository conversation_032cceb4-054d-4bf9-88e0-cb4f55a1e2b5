import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { CollectionDTO } from '../../../dto/collectionDTO'
import { CollectionMapper } from '../../../mappers'
import { GenericCollectionRepository } from '../../../repositories/interfaces/genericCollectionRepository'
import { GetCollectionInputDTO } from './getCollection.dto'

class GetCollection extends UseCase<GetCollectionInputDTO, CollectionDTO> {
  constructor(
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super(GetCollection.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetCollection.name)
  }

  async execute(
    request: Readonly<GetCollectionInputDTO>,
  ): Promise<CollectionDTO> {
    const collectionId = new UniqueEntityID(request.id)
    this.checkPermissions(collectionId)

    const collection = await this._collectionRepository.getById(collectionId)
    return CollectionMapper.getInstance().toDTO(collection)
  }
}

export { GetCollection }
