import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { InviteDTO } from '../../../../users/dto/inviteDTO'
import { GetCollectionInvites } from './getCollectionInvites'
import { GetCollectionInvitesDTO } from './getCollectionInvites.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
})

class GetCollectionInvitesController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetCollectionInvitesDTO,
  InviteDTO[]
> {
  public constructor(useCase: GetCollectionInvites) {
    super(
      'get',
      ':collectionId/invites',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): GetCollectionInvitesDTO {
    return { collectionId: request.params.collectionId }
  }
}

export { GetCollectionInvitesController }
