import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { InviteDTO } from '../../../../users/dto/inviteDTO'
import { InviteMapper } from '../../../../users/mappers/inviteMapper'
import { GenericInviteRepository } from '../../../../users/repositories/interfaces/genericInviteRepository'
import { GetCollectionInvitesDTO } from './getCollectionInvites.dto'

class GetCollectionInvites extends UseCase<
  GetCollectionInvitesDTO,
  InviteDTO[]
> {
  constructor(private readonly _inviteRepository: GenericInviteRepository) {
    super(GetCollectionInvites.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()
    const hasRoleList = [
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER'),
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId),
    ]
    const hasPermission = hasRoleList.some((hasRole) => hasRole === true)

    if (hasPermission) return

    throw new NotAllowed(GetCollectionInvites.name)
  }

  override async execute(
    request: Readonly<GetCollectionInvitesDTO>,
  ): Promise<InviteDTO[]> {
    const collectionId = new UniqueEntityID(request.collectionId)

    this.checkPermissions(collectionId)

    const invites =
      await this._inviteRepository.getAllByCollectionId(collectionId)

    return invites.map(InviteMapper.getInstance().toDTO)
  }
}

export { GetCollectionInvites }
