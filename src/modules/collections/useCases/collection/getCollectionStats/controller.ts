import { constants } from 'http2'
import { InferType, object } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { StatisticDTO } from '../../../dto/statisticDTO'
import { GetCollectionStats } from './getCollectionStats'

const schema = object({ params: object() })

class GetStatsController extends Controller<
  InferType<typeof schema>['params'],
  void,
  void,
  void,
  StatisticDTO[]
> {
  public constructor(useCase: GetCollectionStats) {
    super('get', 'stats', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(): undefined {
    return
  }
}

export { GetStatsController }
