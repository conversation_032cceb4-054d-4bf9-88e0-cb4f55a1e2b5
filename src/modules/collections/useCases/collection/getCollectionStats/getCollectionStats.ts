import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { StatisticDTO } from '../../../dto/statisticDTO'
import { StatisticMapper } from '../../../mappers/statisticMapper'
import { GenericCollectionRepository } from '../../../repositories/interfaces/genericCollectionRepository'

class GetCollectionStats extends UseCase<void, StatisticDTO[]> {
  public constructor(
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super('GetStats')
  }
  override async execute(): Promise<StatisticDTO[]> {
    const currentUser = AuthContext.getUser()

    const stats = await this._collectionRepository.getStats(currentUser.id)

    return stats.map((stat) => StatisticMapper.getInstance().toDTO(stat))
  }
}

export { GetCollectionStats }
