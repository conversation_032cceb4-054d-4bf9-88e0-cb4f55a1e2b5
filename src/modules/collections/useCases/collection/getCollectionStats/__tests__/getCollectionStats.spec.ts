import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubEntry } from '../../../../../entries/domain/__stubs__/entry.stub'
import { entryRepository } from '../../../../../entries/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../domain/__stubs__/field.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import { stubFields } from '../../../../domain/__stubs__/fields.stub'
import { Collection } from '../../../../domain/collection'
import { FieldGroup } from '../../../../domain/fieldGroup'
import { collectionRepository } from '../../../../repositories'
import { GetCollectionStats } from '../getCollectionStats'

describe('GetCollectionStats Use Case', () => {
  const getStatsUseCase = new GetCollectionStats(collectionRepository)
  let user: User
  let testCollection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    testCollection = stubCollection({
      name: 'Test Collection',
      fieldGroups: stubFieldGroups(),
    })

    const testFieldGroup = new FieldGroup({
      name: 'Test Field Group',
      fields: stubFields(),
      position: 0,
    })

    const testField = stubSingleValueField({
      fieldGroupId: testFieldGroup.id,
    })

    testFieldGroup.fields?.add(testField)
    testCollection.fieldGroups?.add(testFieldGroup)

    await collectionRepository.save(testCollection)

    user.userAssignments.push(
      stubUserAssignment({
        collectionId: testCollection.id,
        userId: user.id,
        role: 'COLLECTION_MANAGER',
      }),
    )

    await userRepository.save(user)

    const entry1 = stubEntry({
      createdBy: user.id,
      collectionId: testCollection.id,
      visibility: 'PUBLIC',
    })

    const entry2 = stubEntry({
      createdBy: user.id,
      collectionId: testCollection.id,
      visibility: 'PRIVATE',
    })

    await entryRepository.saveMany([entry1, entry2])
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should return Stats DTO', async () => {
    const resultDTO = await execute(getStatsUseCase, undefined, user)

    const [collectionStats] = resultDTO

    expect(resultDTO).toHaveLength(1)
    expect(collectionStats?.collectionId).toBe(testCollection.id.value)
    expect(collectionStats?.entries.total).toBe(2)
    expect(collectionStats?.entries.public).toBe(1)
    expect(collectionStats?.members).toBe(1)
  })
})
