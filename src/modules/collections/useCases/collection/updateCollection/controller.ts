import { constants } from 'http2'
import { array, InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { CollectionDTO } from '../../../dto/collectionDTO'
import { UpdateCollection } from './updateCollection'
import { UpdateCollectionInputDTO } from './updateCollection.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  body: object({
    name: string(),
    description: string(),
    fieldIds: array(string().uuid().required()),
  }).required(),
})

class UpdateCollectionController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateCollectionInputDTO,
  CollectionDTO
> {
  public constructor(useCase: UpdateCollection) {
    super('patch', ':collectionId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): UpdateCollectionInputDTO {
    return {
      id: request.params.collectionId,
      data: request.body,
    }
  }
}

export { UpdateCollectionController }
