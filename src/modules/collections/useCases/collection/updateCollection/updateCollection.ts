import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { CollectionProps } from '../../../domain/collection'
import { CollectionDTO } from '../../../dto/collectionDTO'
import { CollectionMapper } from '../../../mappers'
import { GenericCollectionRepository } from '../../../repositories/interfaces/genericCollectionRepository'
import { UpdateCollectionInputDTO } from './updateCollection.dto'

class UpdateCollection extends UseCase<
  UpdateCollectionInputDTO,
  CollectionDTO
> {
  constructor(
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super(UpdateCollection.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const hasRoleList = [
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER'),
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId),
    ]
    const hasPermission = hasRoleList.some((hasRole) => hasRole === true)

    if (!hasPermission) {
      throw new NotAllowed(UpdateCollection.name)
    }
  }

  async execute(
    request: Readonly<UpdateCollectionInputDTO>,
  ): Promise<CollectionDTO> {
    const { id, data } = request
    const collection = await this._collectionRepository.getById(
      new UniqueEntityID(id),
    )

    this.checkPermissions(collection.id)

    const updates = this.buildUpdateProps(data, collection)

    if (Object.keys(updates).length > 0) {
      collection.update(updates)
      await this._collectionRepository.save(collection)
    }

    return CollectionMapper.getInstance().toDTO(collection)
  }

  private buildUpdateProps(
    data: UpdateCollectionInputDTO['data'],
    collection: Awaited<ReturnType<GenericCollectionRepository['getById']>>,
  ): Partial<CollectionProps> {
    const updates: Partial<CollectionProps> = {}

    if (data.name !== undefined && data.name !== collection.name) {
      updates.name = data.name
    }

    if (
      data.description !== undefined &&
      data.description !== collection.description
    ) {
      updates.description = data.description
    }

    return updates
  }
}

export { UpdateCollection }
