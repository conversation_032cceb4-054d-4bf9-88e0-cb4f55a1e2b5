import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import { Collection } from '../../../../domain/collection'
import { FieldGroup } from '../../../../domain/fieldGroup'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../repositories'
import { UpdateCollection } from '../updateCollection'
import { UpdateCollectionInputDTO } from '../updateCollection.dto'

describe('UpdateCollection Use Case', () => {
  const updateCollectionUseCase = new UpdateCollection(collectionRepository)
  let user: User
  let testCollection: Collection
  let originalFieldGroups: FieldGroup[]

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const fieldGroup = stubFieldGroup({
      name: 'Original Field Group',
      position: 0,
    })
    testCollection = stubCollection({
      name: 'Original Collection',
      fieldGroups: stubFieldGroups([fieldGroup]),
    })
    originalFieldGroups = testCollection.fieldGroups?.getItems() ?? []
    await collectionRepository.save(testCollection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should update the collection name', async () => {
    const updateDTO: UpdateCollectionInputDTO = {
      id: testCollection.id.value,
      data: { name: 'Updated Collection' },
    }
    const resultDTO = await execute(updateCollectionUseCase, updateDTO, user)
    const updatedCollection = await collectionRepository.getById(
      testCollection.id,
    )
    expect(updatedCollection.name).toBe('Updated Collection')
    expect(resultDTO).toBeDefined()
    expect(resultDTO.name).toBe('Updated Collection')
  })

  it('should throw EntityNotFound if collection does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const updateDTO: UpdateCollectionInputDTO = {
      id: nonExistentId,
      data: { name: 'Should Not Exist' },
    }
    await expect(
      execute(updateCollectionUseCase, updateDTO, user),
    ).rejects.toThrow(new EntityNotFound('Collection', nonExistentId))
  })

  it('should not alter fieldGroups when updating collection data', async () => {
    const updateDTO: UpdateCollectionInputDTO = {
      id: testCollection.id.value,
      data: { name: 'Another Name' },
    }
    await execute(updateCollectionUseCase, updateDTO, user)
    const fielGroups = await fieldGroupRepository.getAllByCollectionId(
      testCollection.id,
    )
    expect(fielGroups).toEqual(originalFieldGroups)
  })

  it('should not update the database when properties are unchanged', async () => {
    const input: UpdateCollectionInputDTO = {
      id: testCollection.id.value,
      data: {
        name: 'Original Collection',
      },
    }

    const saveSpy = jest.spyOn(collectionRepository, 'save')

    await execute(updateCollectionUseCase, input, user)
    expect(saveSpy).not.toHaveBeenCalled()

    saveSpy.mockRestore()
  })

  it('should ignore undefined values in the update request', async () => {
    const initialName = testCollection.name

    const input: UpdateCollectionInputDTO = {
      id: testCollection.id.value,
      data: {
        name: undefined,
      },
    }

    const saveSpy = jest.spyOn(collectionRepository, 'save')
    const result = await execute(updateCollectionUseCase, input, user)

    expect(result.name).toBe(initialName)
    expect(saveSpy).not.toHaveBeenCalled()

    saveSpy.mockRestore()
  })
})
