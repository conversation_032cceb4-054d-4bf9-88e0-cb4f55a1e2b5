import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { User } from '../../../../users/domain/user'
import { Collection } from '../../../domain/collection'
import { CollectionDTO } from '../../../dto/collectionDTO'
import { CollectionMapper } from '../../../mappers'
import { GenericCollectionRepository } from '../../../repositories/interfaces/genericCollectionRepository'

class GetCollections extends UseCase<void, CollectionDTO[]> {
  constructor(
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super(GetCollections.name)
  }

  private getCollections(user: User) {
    return this._collectionRepository.getAllForUser(user.id)
  }

  private getAllCollections() {
    return this._collectionRepository.getAll()
  }

  async execute(): Promise<CollectionDTO[]> {
    const currentUser = AuthContext.getUser()
    let collections: Collection[]

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) {
      collections = await this.getAllCollections()
    } else {
      collections = await this.getCollections(currentUser)
    }

    return collections.map(CollectionMapper.getInstance().toDTO)
  }
}

export { GetCollections }
