import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { userRepository } from '../../../../../users/repositories'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../repositories'
import { GetCollections } from '../getCollections'

describe('GetCollections Use Case', () => {
  const getCollectionsUseCase = new GetCollections(collectionRepository)

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should return all collections for a given user', async () => {
    const collection1 = stubCollection({ name: 'Collection 1' })
    const collection2 = stubCollection({ name: 'Collection 2' })
    const owner = stubUser()
    const role1 = stubUserAssignment({
      userId: owner.id,
      collectionId: collection1.id,
      role: 'COLLECTION_MANAGER',
    })
    const role2 = stubUserAssignment({
      userId: owner.id,
      collectionId: collection2.id,
      role: 'EDITOR',
    })
    owner.userAssignments.push(role1)
    owner.userAssignments.push(role2)

    await collectionRepository.saveMany([collection1, collection2])
    await userRepository.save(owner)

    const resultDTO = await execute(getCollectionsUseCase, undefined, owner)

    expect(Array.isArray(resultDTO)).toBe(true)
    expect(resultDTO.length).toBeGreaterThanOrEqual(2)
    const names = resultDTO.map((c) => c.name)
    expect(names).toEqual(
      expect.arrayContaining(['Collection 1', 'Collection 2']),
    )
  })

  it('should return an empty array if user has no collections', async () => {
    const owner = stubUser()
    const resultDTO = await execute(getCollectionsUseCase, undefined, owner)
    expect(Array.isArray(resultDTO)).toBe(true)
    expect(resultDTO.length).toBe(0)
  })
})
