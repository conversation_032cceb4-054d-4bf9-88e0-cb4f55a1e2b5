import { constants } from 'http2'
import { InferType, object } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { CollectionDTO } from '../../../dto/collectionDTO'
import { GetCollections } from './getCollections'

const schema = object({ params: object() })

class GetCollectionsController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  undefined,
  CollectionDTO[]
> {
  public constructor(useCase: GetCollections) {
    super('get', '', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(): undefined {
    return
  }
}

export { GetCollectionsController }
