import { constants } from 'http2'
import { array, boolean, InferType, mixed, number, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldType, FieldVisibility } from '../../../domain/field'
import { FieldDTO } from '../../../dto/fieldDTO'
import { CreateField } from './createField'
import { CreateFieldDTO } from './createField.dto'

const schema = object({
  params: object({
    fieldGroupId: string().uuid().required(),
  }).required(),
  body: object({
    name: string().required(),
    position: number().required(),
    maxLength: number(),
    mandatory: boolean().required(),
    visibility: mixed<FieldVisibility>()
      .oneOf(['PUBLIC', 'PRIVATE'])
      .required(),
    type: mixed<FieldType>()
      .oneOf(['TEXT', 'TEXTAREA', 'SELECT', 'SELECT-MANY', 'IMAGE-UPLOAD'])
      .required(),
    options: array(
      object({
        label: string().required(),
        value: string().required(),
      }),
    ),
    preview: boolean().required(),
    /* .when('type', {
      is: true, // Isto está errado
      then: (schema) => schema.required(),
    }) */
  }).required(),
})

class CreateFieldController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  CreateFieldDTO,
  FieldDTO
> {
  public constructor(useCase: CreateField) {
    super(
      'post',
      ':fieldGroupId/fields',
      constants.HTTP_STATUS_CREATED,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): CreateFieldDTO {
    return {
      fieldGroupId: request.params.fieldGroupId,
      ...request.body,
    }
  }
}

export { CreateFieldController }
