import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { fieldRepository, fieldGroupRepository } from '../../../../repositories'
import { CreateField } from '../createField'
import { CreateFieldDTO } from '../createField.dto'

describe('CreateField Use Case', () => {
  const createFieldUseCase = new CreateField(
    fieldRepository,
    fieldGroupRepository,
  )
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should create a single-value field and return the correct DTO', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Group 1' })
    await fieldGroupRepository.save(fieldGroup)

    const createFieldDTO: CreateFieldDTO = {
      name: 'Test Field',
      fieldGroupId: fieldGroup.id.value,
      position: 0,
      maxLength: 100,
      mandatory: true,
      visibility: 'PUBLIC',
      type: 'TEXT',
      preview: true,
    }

    const resultDTO = await execute(createFieldUseCase, createFieldDTO, user)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.name).toBe('Test Field')
    expect(resultDTO.fieldGroupId).toBe(fieldGroup.id.value)
    expect(resultDTO.type).toBe('TEXT')
    expect(resultDTO.options).toEqual([])
    expect(resultDTO.preview).toEqual(createFieldDTO.preview)
  })

  it('should create a multiple-value field (SELECT) and return the correct DTO', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Group 2' })
    await fieldGroupRepository.save(fieldGroup)

    const createFieldDTO: CreateFieldDTO = {
      name: 'Select Field',
      fieldGroupId: fieldGroup.id.value,
      position: 1,
      mandatory: false,
      visibility: 'PRIVATE',
      type: 'SELECT',
      preview: true,
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
    }

    const resultDTO = await execute(createFieldUseCase, createFieldDTO, user)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.name).toBe('Select Field')
    expect(resultDTO.fieldGroupId).toBe(fieldGroup.id.value)
    expect(resultDTO.type).toBe('SELECT')
    expect(resultDTO.options).toEqual([
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
    ])
    expect(resultDTO.preview).toEqual(createFieldDTO.preview)
  })
})
