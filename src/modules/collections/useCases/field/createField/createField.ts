import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { FieldUtils } from '../../../../../utils/fieldUtils'
import { UserUtils } from '../../../../../utils/userUtils'
import { Field } from '../../../domain/field'
import { FieldDTO } from '../../../dto/fieldDTO'
import { FieldMapper } from '../../../mappers'
import { GenericFieldGroupRepository } from '../../../repositories/interfaces/genericFieldGroupRepository'
import { GenericFieldRepository } from '../../../repositories/interfaces/genericFieldRepository'
import { CreateFieldDTO } from './createField.dto'

class CreateField extends UseCase<CreateFieldDTO, FieldDTO> {
  constructor(
    private readonly _fieldRepository: GenericFieldRepository,
    private readonly _fieldGroupRepository: GenericFieldGroupRepository,
  ) {
    super(CreateField.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(CreateField.name)
  }

  async execute(request: CreateFieldDTO): Promise<FieldDTO> {
    let field: Field

    const fieldGroup = await this._fieldGroupRepository.getById(
      new UniqueEntityID(request.fieldGroupId),
    )

    this.checkPermissions()

    if (FieldUtils.isSelectType(request.type)) {
      field = new Field({
        name: request.name,
        position: request.position,
        maxLength: request.maxLength,
        mandatory: request.mandatory,
        visibility: request.visibility,
        fieldGroupId: fieldGroup.id,
        type: request.type,
        preview: request.preview,
        options: request.options ?? [],
      })
    } else {
      field = new Field({
        name: request.name,
        position: request.position,
        maxLength: request.maxLength,
        mandatory: request.mandatory,
        visibility: request.visibility,
        fieldGroupId: fieldGroup.id,
        type: request.type,
        preview: request.preview,
      })
    }

    await this._fieldRepository.save(field)

    return FieldMapper.getInstance().toDTO(field)
  }
}

export { CreateField }
