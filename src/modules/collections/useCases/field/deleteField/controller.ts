import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { DeleteField } from './deleteField'
import { DeleteFieldDTO } from './deleteField.dto'

const schema = object({
  params: object({
    fieldId: string().uuid().required(),
  }).required(),
})

class DeleteFieldController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteFieldDTO
> {
  public constructor(useCase: DeleteField) {
    super(
      'delete',
      ':fieldId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): DeleteFieldDTO {
    return { id: request.params.fieldId }
  }
}

export { DeleteFieldController }
