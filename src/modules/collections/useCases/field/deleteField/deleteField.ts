import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericFieldRepository } from '../../../repositories/interfaces/genericFieldRepository'
import { DeleteFieldDTO } from './deleteField.dto'

class DeleteField extends UseCase<DeleteFieldDTO> {
  constructor(private readonly _fieldRepository: GenericFieldRepository) {
    super(DeleteField.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(DeleteField.name)
  }

  async execute(request: Readonly<DeleteFieldDTO>): Promise<undefined> {
    const fieldId = new UniqueEntityID(request.id)

    this.checkPermissions()

    await this._fieldRepository.delete(fieldId)
  }
}

export { DeleteField }
