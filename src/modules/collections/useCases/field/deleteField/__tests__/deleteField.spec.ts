import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubSingleValueField } from '../../../../domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../domain/__stubs__/fields.stub'
import { fieldGroupRepository, fieldRepository } from '../../../../repositories'
import { DeleteField } from '../deleteField'
import { DeleteFieldDTO } from '../deleteField.dto'

describe('DeleteField Use Case', () => {
  const deleteFieldUseCase = new DeleteField(fieldRepository)
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })
  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should delete an existing field', async () => {
    const fieldGroup = stubFieldGroup({
      name: 'Group 1',
      fields: stubFields([]),
    })
    const field = stubSingleValueField({
      name: 'Field to Delete',
      fieldGroupId: fieldGroup.id,
    })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    const deleteFieldDTO: DeleteFieldDTO = {
      id: field.id.value,
    }
    const entity = await fieldRepository.getById(field.id)
    expect(entity).toBeDefined()
    await expect(
      execute(deleteFieldUseCase, deleteFieldDTO, user),
    ).resolves.toBeUndefined()
    await expect(fieldRepository.getById(field.id)).rejects.toThrow()
  })

  it('should not throw when deleting a non-existent field', async () => {
    const nonExistentId = new UniqueEntityID().value
    const deleteFieldDTO: DeleteFieldDTO = {
      id: nonExistentId,
    }
    await expect(
      execute(deleteFieldUseCase, deleteFieldDTO, user),
    ).rejects.toThrow(new EntityNotFound('Field', nonExistentId))
  })
})
