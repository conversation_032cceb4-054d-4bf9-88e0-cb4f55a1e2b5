import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldDTO } from '../../../dto/fieldDTO'
import { GetField } from './getField'
import { GetFieldDTO } from './getField.dto'

const schema = object({
  params: object({
    fieldId: string().uuid().required(),
  }).required(),
})

class GetFieldController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetFieldDTO,
  FieldDTO
> {
  public constructor(useCase: GetField) {
    super('get', ':fieldId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetFieldDTO {
    return { id: request.params.fieldId }
  }
}

export { GetFieldController }
