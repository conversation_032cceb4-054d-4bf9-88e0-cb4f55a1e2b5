import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubSingleValueField } from '../../../../domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../domain/__stubs__/fields.stub'
import { fieldGroupRepository, fieldRepository } from '../../../../repositories'
import { GetField } from '../getField'
import { GetFieldDTO } from '../getField.dto'

describe('GetField Use Case', () => {
  const getFieldUseCase = new GetField(fieldRepository)
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })
  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should return the correct DTO for an existing field', async () => {
    const fieldGroup = stubFieldGroup({
      name: 'Test Field Group',
      fields: stubFields([]),
    })
    const field = stubSingleValueField({
      name: 'Test Field',
      fieldGroupId: fieldGroup.id,
    })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    const getFieldDTO: GetFieldDTO = {
      id: field.id.value,
    }

    const resultDTO = await execute(getFieldUseCase, getFieldDTO, user)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(field.id.value)
    expect(resultDTO.name).toBe('Test Field')
    expect(resultDTO.type).toBe('TEXT')
    expect(resultDTO.deleted).toBe(false)
  })

  it('should throw EntityNotFound if field does not exist', async () => {
    const getFieldDTO: GetFieldDTO = {
      id: new UniqueEntityID().value,
    }
    await expect(execute(getFieldUseCase, getFieldDTO, user)).rejects.toThrow(
      EntityNotFound,
    )
  })
})
