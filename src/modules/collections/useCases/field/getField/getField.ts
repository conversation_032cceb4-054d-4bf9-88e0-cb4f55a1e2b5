import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { FieldDTO } from '../../../dto/fieldDTO'
import { FieldMapper } from '../../../mappers'
import { GenericFieldRepository } from '../../../repositories/interfaces/genericFieldRepository'
import { GetFieldDTO } from './getField.dto'

class GetField extends UseCase<GetFieldDTO, FieldDTO> {
  constructor(private readonly _fieldRepository: GenericFieldRepository) {
    super(GetField.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(GetField.name)
  }

  async execute(request: Readonly<GetFieldDTO>): Promise<FieldDTO> {
    const { id } = request
    const field = await this._fieldRepository.getById(new UniqueEntityID(id))

    this.checkPermissions()

    return FieldMapper.getInstance().toDTO(field)
  }
}

export { GetField }
