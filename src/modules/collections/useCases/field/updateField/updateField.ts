import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { FieldUtils } from '../../../../../utils/fieldUtils'
import { UserUtils } from '../../../../../utils/userUtils'
import { Field, FieldProps } from '../../../domain/field'
import { FieldDTO } from '../../../dto/fieldDTO'
import { FieldMapper } from '../../../mappers'
import { GenericFieldRepository } from '../../../repositories/interfaces/genericFieldRepository'
import { UpdateFieldInputDTO } from './updateField.dto'
import { MaxLengthTooHigh } from './updateField.errors'

class UpdateField extends UseCase<UpdateFieldInputDTO, FieldDTO> {
  constructor(private readonly _fieldRepository: GenericFieldRepository) {
    super(UpdateField.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(UpdateField.name)
  }

  async execute(request: Readonly<UpdateFieldInputDTO>): Promise<FieldDTO> {
    const { id, data } = request
    const field = await this._fieldRepository.getById(new UniqueEntityID(id))

    this.checkPermissions()

    const shouldUnsetMaxLength =
      'maxLength' in data &&
      data.maxLength === undefined &&
      field.maxLength !== undefined
    const updates = this.buildUpdateProps(data, field)
    const hasChanges = Object.keys(updates).length > 0 || shouldUnsetMaxLength

    if (!hasChanges) {
      return FieldMapper.getInstance().toDTO(field)
    }

    field.update(updates)
    if (shouldUnsetMaxLength) {
      field.unsetMaxLength()
    }

    await this._fieldRepository.save(field)
    return FieldMapper.getInstance().toDTO(field)
  }

  private buildUpdateProps(
    data: UpdateFieldInputDTO['data'],
    field: Field,
  ): Partial<FieldProps> {
    const updates: Partial<FieldProps> = {}

    // Compare before adding to updates to avoid unnecessary updates
    if (data.name !== undefined && data.name !== field.name) {
      updates.name = data.name
    }

    if (data.position !== undefined && data.position !== field.position) {
      updates.position = data.position
    }

    if (data.visibility !== undefined && data.visibility !== field.visibility) {
      updates.visibility = data.visibility
    }

    if (data.mandatory !== undefined && data.mandatory !== field.mandatory) {
      updates.mandatory = data.mandatory
    }

    // Handle maxLength which can be explicitly unset
    if (
      'maxLength' in data &&
      data.maxLength !== undefined &&
      data.maxLength !== field.maxLength
    ) {
      if (field.maxLength === undefined || data.maxLength < field.maxLength) {
        throw new MaxLengthTooHigh()
      }
      updates.maxLength = data.maxLength
    }

    if (
      data.fieldGroupId !== undefined &&
      data.fieldGroupId !== field.fieldGroupId.value
    ) {
      updates.fieldGroupId = new UniqueEntityID(data.fieldGroupId)
    }

    if (data.preview !== undefined && data.preview !== field.preview) {
      updates.preview = data.preview
    }

    if (
      data.options !== undefined &&
      FieldUtils.isSelectType(field.type) &&
      JSON.stringify(data.options) !== JSON.stringify(field.options)
    ) {
      updates.options = data.options
    }

    return updates
  }
}

export { UpdateField }
