import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import {
  EntityNotFound,
  RepositoryError,
} from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import {
  stubMultiValueField,
  stubSingleValueField,
} from '../../../../domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../domain/__stubs__/fields.stub'
import { Field } from '../../../../domain/field'
import { fieldGroupRepository, fieldRepository } from '../../../../repositories'
import { UpdateField } from '../updateField'
import { UpdateFieldInputDTO } from '../updateField.dto'
import { MaxLengthTooHigh } from '../updateField.errors'

describe('UpdateField Use Case', () => {
  const updateFieldUseCase = new UpdateField(fieldRepository)
  let user: User
  let field: Field
  let multiField: Field

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const fieldGroup = stubFieldGroup({
      name: 'Test Field Group',
      fields: stubFields([]),
    })
    field = stubSingleValueField({
      name: 'Old Field',
      fieldGroupId: fieldGroup.id,
      position: 0,
      mandatory: false,
      visibility: 'PUBLIC',
      type: 'TEXT',
      maxLength: 16,
    })
    multiField = stubMultiValueField({
      name: 'Multi Value Field',
      fieldGroupId: fieldGroup.id,
      position: 0,
      mandatory: false,
      visibility: 'PUBLIC',
      type: 'SELECT',
      maxLength: 16,
    })
    fieldGroup.fields?.add(field)
    fieldGroup.fields?.add(multiField)
    await fieldGroupRepository.save(fieldGroup)
  })
  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should update an existing single value field and return the correct DTO', async () => {
    const updateFieldDTO: UpdateFieldInputDTO = {
      id: field.id.value,
      data: {
        name: 'Updated Field',
        position: 1,
        maxLength: 50,
        mandatory: true,
        visibility: 'PRIVATE',
        preview: false,
      },
    }

    const resultDTO = await execute(updateFieldUseCase, updateFieldDTO, user)
    const updatedField = await fieldRepository.getById(field.id)

    expect(updatedField.name).toBe('Updated Field')
    expect(updatedField.position).toBe(1)
    expect(updatedField.maxLength).toBe(50)
    expect(updatedField.mandatory).toBe(true)
    expect(updatedField.visibility).toBe('PRIVATE')
    expect(updatedField.type).toBe('TEXT')
    expect(updatedField.preview).toBe(updateFieldDTO.data.preview)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(field.id.value)
    expect(resultDTO.name).toBe('Updated Field')
    expect(resultDTO.position).toBe(1)
    expect(resultDTO.maxLength).toBe(50)
    expect(resultDTO.mandatory).toBe(true)
    expect(resultDTO.visibility).toBe('PRIVATE')
    expect(resultDTO.type).toBe('TEXT')
  })

  it('should update an existing multi value field and return the correct DTO', async () => {
    const updateFieldDTO: UpdateFieldInputDTO = {
      id: multiField.id.value,
      data: {
        name: 'Updated Multi Value Field',
        position: 1,
        maxLength: 50,
        mandatory: true,
        visibility: 'PRIVATE',
        options: [{ label: 'New Option', value: 'new-opt' }],
      },
    }

    const resultDTO = await execute(updateFieldUseCase, updateFieldDTO, user)
    const updatedField = await fieldRepository.getById(multiField.id)

    expect(updatedField.name).toBe('Updated Multi Value Field')
    expect(updatedField.position).toBe(1)
    expect(updatedField.maxLength).toBe(50)
    expect(updatedField.mandatory).toBe(true)
    expect(updatedField.visibility).toBe('PRIVATE')
    expect(updatedField.type).toBe('SELECT')
    expect(updatedField.options).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          label: expect.stringMatching('New Option'),
          value: expect.stringMatching('new-opt'),
        }),
      ]),
    )

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(multiField.id.value)
    expect(resultDTO.name).toBe('Updated Multi Value Field')
    expect(resultDTO.position).toBe(1)
    expect(resultDTO.maxLength).toBe(50)
    expect(resultDTO.mandatory).toBe(true)
    expect(resultDTO.visibility).toBe('PRIVATE')
    expect(resultDTO.type).toBe('SELECT')
    expect(updatedField.options).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          label: expect.stringMatching('New Option'),
          value: expect.stringMatching('new-opt'),
        }),
      ]),
    )
  })

  it('should fail to update if field group does not exist', async () => {
    const updateFieldDTO: UpdateFieldInputDTO = {
      id: field.id.value,
      data: {
        name: 'Updated Field',
        position: 1,
        maxLength: 50,
        mandatory: true,
        visibility: 'PRIVATE',
        fieldGroupId: new UniqueEntityID().value,
      },
    }
    await expect(
      execute(updateFieldUseCase, updateFieldDTO, user),
    ).rejects.toThrow(RepositoryError)
  })

  it('should fail to update if attempting to reduce an existing fields maxLength', async () => {
    const updateFieldDTO: UpdateFieldInputDTO = {
      id: field.id.value,
      data: {
        name: 'Updated Field',
        position: 1,
        maxLength: 3,
        mandatory: true,
        visibility: 'PRIVATE',
      },
    }
    await expect(
      execute(updateFieldUseCase, updateFieldDTO, user),
    ).rejects.toThrow(MaxLengthTooHigh)
  })

  it('should unset optional property of maxLength', async () => {
    const updateFieldDTO: UpdateFieldInputDTO = {
      id: field.id.value,
      data: {
        name: 'Updated Field',
        position: 1,
        maxLength: undefined,
        mandatory: true,
        visibility: 'PRIVATE',
      },
    }
    const resultDTO = await execute(updateFieldUseCase, updateFieldDTO, user)
    const updatedField = await fieldRepository.getById(field.id)

    expect(updatedField.maxLength).toBeUndefined()
    expect(resultDTO.maxLength).toBeUndefined()
  })

  it('should throw EntityNotFound if field does not exist', async () => {
    const updateFieldDTO: UpdateFieldInputDTO = {
      id: new UniqueEntityID().value,
      data: {
        name: 'Should Not Exist',
      },
    }
    await expect(
      execute(updateFieldUseCase, updateFieldDTO, user),
    ).rejects.toThrow(EntityNotFound)
  })

  it('should not update the database when properties are unchanged', async () => {
    const input: UpdateFieldInputDTO = {
      id: field.id.value,
      data: {
        name: 'Old Field',
        position: 0,
        mandatory: false,
        visibility: 'PUBLIC',
      },
    }

    const saveSpy = jest.spyOn(fieldRepository, 'save')

    await execute(updateFieldUseCase, input, user)
    expect(saveSpy).not.toHaveBeenCalled()

    saveSpy.mockRestore()
  })
})
