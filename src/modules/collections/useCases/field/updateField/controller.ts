import { constants } from 'http2'
import { array, boolean, InferType, mixed, number, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldVisibility } from '../../../domain/field'
import { FieldDTO } from '../../../dto/fieldDTO'
import { UpdateField } from './updateField'
import { UpdateFieldInputDTO } from './updateField.dto'

const schema = object({
  params: object({
    fieldId: string().uuid().required(),
  }).required(),
  body: object({
    name: string(),
    position: number(),
    maxLength: number(),
    mandatory: boolean(),
    visibility: mixed<FieldVisibility>().oneOf(['PUBLIC', 'PRIVATE']),
    options: array(
      object({
        label: string().required(),
        value: string().required(),
      }),
    ),
  }),
})

class UpdateFieldController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateFieldInputDTO,
  FieldDTO
> {
  public constructor(useCase: UpdateField) {
    super('patch', ':fieldId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): UpdateFieldInputDTO {
    return {
      id: request.params.fieldId,
      data: request.body,
    }
  }
}

export { UpdateFieldController }
