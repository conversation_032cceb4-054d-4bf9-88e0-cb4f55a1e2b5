import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import {
  NotAllowed,
  NotFound,
} from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericUserRepository } from '../../../../users/repositories/interfaces/genericUserRepository'
import { DeleteUserAssignmentDTO } from './deleteUserAssignment.dto'

class DeleteUserAssignment extends UseCase<DeleteUserAssignmentDTO, void> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(DeleteUserAssignment.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const hasPermission =
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER') ||
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId)

    if (!hasPermission) {
      throw new NotAllowed(DeleteUserAssignment.name)
    }
  }

  override async execute(
    request: Readonly<DeleteUserAssignmentDTO>,
  ): Promise<void> {
    const collectionId = new UniqueEntityID(request.collectionId)
    const userId = new UniqueEntityID(request.userId)

    this.checkPermissions(collectionId)

    // Prevent users from deleting their own assignments
    const currentUser = AuthContext.getUser()
    if (currentUser.id.equals(userId)) {
      throw new NotAllowed(
        `${DeleteUserAssignment.name}: Users cannot remove their own assignments`,
      )
    }

    const user = await this._userRepository.getById(userId)

    const wasRemoved = user.removeAssignment(collectionId, currentUser.id)

    if (!wasRemoved) {
      throw new NotFound(
        `User assignment for collection ${collectionId.value} not found`,
      )
    }

    await this._userRepository.save(user)
  }
}

export { DeleteUserAssignment }
