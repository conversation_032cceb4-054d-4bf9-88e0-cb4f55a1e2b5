import { domainEvents } from '../../../../../shared/domain/events'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import {
  NotAllowed,
  NotFound,
} from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { UserRoleChangedEvent } from '../../../../activities/domain/events'
import { GenericUserRepository } from '../../../../users/repositories/interfaces/genericUserRepository'
import { Role } from '../../../../users/types/user'
import { DeleteUserAssignmentDTO } from './deleteUserAssignment.dto'

class DeleteUserAssignment extends UseCase<DeleteUserAssignmentDTO, void> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(DeleteUserAssignment.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const hasPermission =
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER') ||
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId)

    if (!hasPermission) {
      throw new NotAllowed(DeleteUserAssignment.name)
    }
  }

  override async execute(
    request: Readonly<DeleteUserAssignmentDTO>,
  ): Promise<void> {
    const collectionId = new UniqueEntityID(request.collectionId)
    const userId = new UniqueEntityID(request.userId)

    this.checkPermissions(collectionId)

    // Prevent users from deleting their own assignments
    const currentUser = AuthContext.getUser()
    if (currentUser.id.equals(userId)) {
      throw new NotAllowed(
        `${DeleteUserAssignment.name}: Users cannot remove their own assignments`,
      )
    }

    const user = await this._userRepository.getById(userId)

    // Check if the assignment exists and get the old role
    const existingAssignment = user.userAssignments.find(
      (assignment) =>
        assignment.collectionId && assignment.collectionId.equals(collectionId),
    )

    if (!existingAssignment) {
      throw new NotFound(
        `User assignment for collection ${collectionId.value} not found`,
      )
    }

    const oldRole = existingAssignment.role

    const updatedAssignments = user.userAssignments.filter(
      (assignment) => !assignment.collectionId?.equals(collectionId),
    )

    user.userAssignments = updatedAssignments

    await this._userRepository.save(user)

    // Dispatch domain event for role removal
    // Note: Using a special activity type to indicate user removal from collection
    const event = new UserRoleChangedEvent(
      user,
      collectionId,
      currentUser.id,
      oldRole as Role,
      oldRole as Role, // Keep same role but the activity type will indicate removal
    )
    // Override the activity type to indicate removal
    ;(event as any).activityType = 'USER_REMOVED'
    ;(event as any).activityData = {
      targetUserId: user.id.value,
      collectionId: collectionId.value,
      changedBy: currentUser.id.value,
      removedRole: oldRole,
    }
    domainEvents.markEntityForDispatch(event)
    await domainEvents.dispatchEventsForEntity(user.id)
  }
}

export { DeleteUserAssignment }
