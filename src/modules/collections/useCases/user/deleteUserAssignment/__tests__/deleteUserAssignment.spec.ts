import { stubEmail } from '../../../../../../shared/domain/__stubs__/email.stub'
import { domainEvents } from '../../../../../../shared/domain/events'
import {
  NotAllowed,
  NotFound,
} from '../../../../../../shared/errors/useCaseErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { UserRemovedEvent } from '../../../../../activities/domain/events/users/UserRemovedEvent'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { Collection } from '../../../../domain/collection'
import { collectionRepository } from '../../../../repositories'
import { DeleteUserAssignment } from '../deleteUserAssignment'

describe('Delete User Assignment Use Case', () => {
  let user: User
  let targetUser: User
  let collection: Collection
  let domainEventsSpy: jest.SpyInstance
  let markEntitySpy: jest.SpyInstance

  beforeAll(async () => {
    await setupTestDatabase()

    collection = stubCollection({
      name: 'Test Collection',
    })

    user = stubUser({
      name: 'Collection Manager',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'COLLECTION_MANAGER',
        collectionId: collection.id,
      }),
    )

    targetUser = stubUser({
      name: 'Collection Contributor',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    targetUser.userAssignments.push(
      stubUserAssignment({
        userId: targetUser.id,
        role: 'CONTRIBUTOR',
        collectionId: collection.id,
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    // Save the collection and users to the database
    await collectionRepository.save(collection)
    await userRepository.saveMany([user, targetUser])

    // Clear any existing domain events and set up spies
    jest.restoreAllMocks()
    domainEvents.clearMarkedEntities()
    domainEventsSpy = jest.spyOn(domainEvents, 'dispatchEventsForEntity')
    markEntitySpy = jest.spyOn(domainEvents, 'markEntityForDispatch')
  })

  afterEach(() => {
    // Clean up spies
    if (domainEventsSpy) {
      domainEventsSpy.mockRestore()
    }
    if (markEntitySpy) {
      markEntitySpy.mockRestore()
    }
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should delete a user assignment from a collection', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    let fetchUser = await userRepository.getById(targetUser.id)

    expect(fetchUser.userAssignments.length).toBe(1)

    await execute(
      useCase,
      { collectionId: collection.id.value, userId: targetUser.id.value },
      user,
    )

    fetchUser = await userRepository.getById(targetUser.id)

    expect(fetchUser.userAssignments.length).toBe(0)
  })

  it('should dispatch UserRemovedEvent when deleting a user assignment', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    await execute(
      useCase,
      { collectionId: collection.id.value, userId: targetUser.id.value },
      user,
    )

    // Verify that domain events were dispatched
    expect(domainEventsSpy).toHaveBeenCalledWith(targetUser.id)
    expect(markEntitySpy).toHaveBeenCalledTimes(1)

    // Verify UserRemovedEvent was marked for dispatch
    const markedEvent = markEntitySpy.mock.calls[0][0] as UserRemovedEvent
    expect(markedEvent).toBeInstanceOf(UserRemovedEvent)
    expect(markedEvent.user.id.equals(targetUser.id)).toBe(true)
    expect(markedEvent.collectionId.equals(collection.id)).toBe(true)
    expect(markedEvent.removedBy.equals(user.id)).toBe(true)
    expect(markedEvent.removedRole).toBe('CONTRIBUTOR')
    expect(markedEvent.activityType).toBe('USER_REMOVED')
    expect(markedEvent.activityData).toEqual({
      targetUserId: targetUser.id.value,
      collectionId: collection.id.value,
      changedBy: user.id.value,
      removedRole: 'CONTRIBUTOR',
    })
  })

  it('should not dispatch UserRemovedEvent when user assignment does not exist', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    const userWithoutAssignment = stubUser({
      name: 'User Without Assignment',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(userWithoutAssignment)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: userWithoutAssignment.id.value,
        },
        user,
      ),
    ).rejects.toThrow(NotFound)

    // Verify no domain events were marked for dispatch since the operation failed
    expect(markEntitySpy).not.toHaveBeenCalled()
  })

  it('should throw NotFound when deleting a non-existent user assignment', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    const userWithoutAssignment = stubUser({
      name: 'User Without Assignment',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(userWithoutAssignment)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: userWithoutAssignment.id.value,
        },
        user,
      ),
    ).rejects.toThrow(NotFound)
  })

  it('should throw NotAllowed when user does not have permission to delete assignments', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    const regularUser = stubUser({
      name: 'Regular User',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    regularUser.userAssignments.push(
      stubUserAssignment({
        userId: regularUser.id,
        role: 'VIEWER',
        collectionId: collection.id,
      }),
    )

    await userRepository.save(regularUser)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: targetUser.id.value,
        },
        regularUser,
      ),
    ).rejects.toThrow(NotAllowed)
  })

  it('should allow PLATFORM_MANAGER to delete user assignments even without collection assignment', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    const platformManager = stubUser({
      name: 'Platform Manager',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    platformManager.userAssignments.push(
      stubUserAssignment({
        userId: platformManager.id,
        role: 'PLATFORM_MANAGER',
      }),
    )

    await userRepository.save(platformManager)

    let fetchUser = await userRepository.getById(targetUser.id)
    expect(fetchUser.userAssignments.length).toBe(1)

    await execute(
      useCase,
      {
        collectionId: collection.id.value,
        userId: targetUser.id.value,
      },
      platformManager,
    )

    fetchUser = await userRepository.getById(targetUser.id)
    expect(fetchUser.userAssignments.length).toBe(0)

    // Verify UserRemovedEvent was dispatched with correct data
    expect(markEntitySpy).toHaveBeenCalledTimes(1)
    const markedEvent = markEntitySpy.mock.calls[0][0] as UserRemovedEvent
    expect(markedEvent).toBeInstanceOf(UserRemovedEvent)
    expect(markedEvent.removedBy.equals(platformManager.id)).toBe(true)
  })

  it('should prevent a user from deleting their own assignment', async () => {
    const useCase = new DeleteUserAssignment(userRepository)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: user.id.value,
        },
        user,
      ),
    ).rejects.toThrow(NotAllowed)

    const fetchUser = await userRepository.getById(user.id)
    expect(fetchUser.userAssignments.length).toBe(1)
  })
})
