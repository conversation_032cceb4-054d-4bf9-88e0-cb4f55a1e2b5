import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { DeleteUserAssignment } from './deleteUserAssignment'
import { DeleteUserAssignmentDTO } from './deleteUserAssignment.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
    userId: string().uuid().required(),
  }).required(),
})

class DeleteUserAssignmentController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteUserAssignmentDTO,
  void
> {
  public constructor(useCase: DeleteUserAssignment) {
    super(
      'delete',
      ':collectionId/users/:userId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected override parseInput(
    request: InferType<typeof schema>,
  ): DeleteUserAssignmentDTO {
    return {
      collectionId: request.params.collectionId,
      userId: request.params.userId,
    }
  }
}

export { DeleteUserAssignmentController }
