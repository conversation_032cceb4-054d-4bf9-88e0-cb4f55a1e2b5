import { constants } from 'http2'
import { InferType, mixed, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { UserDTO } from '../../../../users/dto/userDTO'
import { CollectionRole } from '../../../../users/types/user'
import { UpdateUserAssignment } from './updateUserAssignment'
import { UpdateUserAssignmentDTO } from './updateUserAssignment.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
    userId: string().uuid().required(),
  }).required(),
  body: object({
    role: mixed<CollectionRole>()
      .oneOf(['COLLECTION_MANAGER', 'CONTRIBUTOR', 'EDITOR', 'VIEWER'])
      .required(),
  }).required(),
})

class UpdateUserAssignmentController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateUserAssignmentDTO,
  UserDTO
> {
  public constructor(useCase: UpdateUserAssignment) {
    super(
      'patch',
      ':collectionId/users/:userId',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected override parseInput(
    request: InferType<typeof schema>,
  ): UpdateUserAssignmentDTO {
    return {
      collectionId: request.params.collectionId,
      userId: request.params.userId,
      role: request.body.role,
    }
  }
}

export { UpdateUserAssignmentController }
