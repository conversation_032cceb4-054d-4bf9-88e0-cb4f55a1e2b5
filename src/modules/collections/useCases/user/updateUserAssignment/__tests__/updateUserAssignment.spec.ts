import { stubEmail } from '../../../../../../shared/domain/__stubs__/email.stub'
import {
  NotAllowed,
  NotFound,
} from '../../../../../../shared/errors/useCaseErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { Collection } from '../../../../domain/collection'
import { collectionRepository } from '../../../../repositories'
import { UpdateUserAssignment } from '../updateUserAssignment'

describe('Update User Assignment Use Case', () => {
  let user: User
  let targetUser: User
  let collection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    collection = stubCollection({
      name: 'Test Collection',
    })

    user = stubUser({
      name: 'Collection Manager',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'COLLECTION_MANAGER',
        collectionId: collection.id,
      }),
    )

    targetUser = stubUser({
      name: 'Collection Contributor',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    targetUser.userAssignments.push(
      stubUserAssignment({
        userId: targetUser.id,
        role: 'CONTRIBUTOR',
        collectionId: collection.id,
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    // Save the collection and users to the database
    await collectionRepository.save(collection)
    await userRepository.saveMany([user, targetUser])
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should update a user assignment from a collection', async () => {
    const useCase = new UpdateUserAssignment(userRepository)

    let fetchUser = await userRepository.getById(targetUser.id)

    expect(fetchUser.userAssignments.at(0)?.role).toBe('CONTRIBUTOR')

    await execute(
      useCase,
      {
        collectionId: collection.id.value,
        userId: targetUser.id.value,
        role: 'VIEWER',
      },
      user,
    )

    fetchUser = await userRepository.getById(targetUser.id)

    expect(fetchUser.userAssignments.at(0)?.role).toBe('VIEWER')
  })

  it('should throw NotFound when updating a non-existent user assignment', async () => {
    const useCase = new UpdateUserAssignment(userRepository)

    const userWithoutAssignment = stubUser({
      name: 'User Without Assignment',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(userWithoutAssignment)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: userWithoutAssignment.id.value,
          role: 'VIEWER',
        },
        user,
      ),
    ).rejects.toThrow(NotFound)
  })

  it('should throw when updating with an invalid role', async () => {
    const useCase = new UpdateUserAssignment(userRepository)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: targetUser.id.value,
          role: 'INVALID_ROLE',
        },
        user,
      ),
    ).rejects.toThrow()
  })

  it('should throw NotAllowed when user does not have permission to update assignments', async () => {
    const useCase = new UpdateUserAssignment(userRepository)

    const regularUser = stubUser({
      name: 'Regular User',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    regularUser.userAssignments.push(
      stubUserAssignment({
        userId: regularUser.id,
        role: 'VIEWER',
        collectionId: collection.id,
      }),
    )

    await userRepository.save(regularUser)

    await expect(
      execute(
        useCase,
        {
          collectionId: collection.id.value,
          userId: targetUser.id.value,
          role: 'EDITOR',
        },
        regularUser,
      ),
    ).rejects.toThrow(NotAllowed)
  })

  it('should allow PLATFORM_MANAGER to update user assignments even without collection assignment', async () => {
    const useCase = new UpdateUserAssignment(userRepository)

    const platformManager = stubUser({
      name: 'Platform Manager',
      email: stubEmail({ value: '<EMAIL>' }),
    })

    platformManager.userAssignments.push(
      stubUserAssignment({
        userId: platformManager.id,
        role: 'PLATFORM_MANAGER',
      }),
    )

    await userRepository.save(platformManager)

    let fetchUser = await userRepository.getById(targetUser.id)
    expect(fetchUser.userAssignments.at(0)?.role).toBe('CONTRIBUTOR')

    await execute(
      useCase,
      {
        collectionId: collection.id.value,
        userId: targetUser.id.value,
        role: 'EDITOR',
      },
      platformManager,
    )

    fetchUser = await userRepository.getById(targetUser.id)
    expect(fetchUser.userAssignments.at(0)?.role).toBe('EDITOR')
  })
})
