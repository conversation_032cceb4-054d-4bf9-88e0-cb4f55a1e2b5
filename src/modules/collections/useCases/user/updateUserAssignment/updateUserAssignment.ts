import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import {
  NotAllowed,
  NotFound,
} from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { UserDTO } from '../../../../users/dto/userDTO'
import { UserMapper } from '../../../../users/mappers'
import { GenericUserRepository } from '../../../../users/repositories/interfaces/genericUserRepository'
import { UpdateCollection } from '../../collection/updateCollection/updateCollection'
import { UpdateUserAssignmentDTO } from './updateUserAssignment.dto'

class UpdateUserAssignment extends UseCase<UpdateUserAssignmentDTO, UserDTO> {
  constructor(private readonly userRepository: GenericUserRepository) {
    super(UpdateCollection.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const hasPermission =
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER') ||
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId)

    if (!hasPermission) {
      throw new NotAllowed(UpdateUserAssignment.name)
    }
  }

  override async execute(
    request: Readonly<UpdateUserAssignmentDTO>,
  ): Promise<UserDTO> {
    const collectionId = new UniqueEntityID(request.collectionId)
    const userId = new UniqueEntityID(request.userId)

    this.checkPermissions(collectionId)

    const user = await this.userRepository.getById(userId)
    const currentUser = AuthContext.getUser()

    const wasUpdated = user.updateAssignment(
      collectionId,
      request.role,
      currentUser.id,
    )

    if (!wasUpdated) {
      throw new NotFound(UpdateUserAssignment.name)
    }

    await this.userRepository.save(user)

    return UserMapper.getInstance().toDTO(user)
  }
}

export { UpdateUserAssignment }
