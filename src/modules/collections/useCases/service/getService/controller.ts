import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { GetService } from './getService'
import { GetServiceDTO } from './getService.dto'

const schema = object({
  params: object({
    serviceId: string().uuid().required(),
  }).required(),
})

class GetServiceController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetServiceDTO,
  ServiceDTO
> {
  public constructor(useCase: GetService) {
    super('get', ':serviceId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetServiceDTO {
    return { id: request.params.serviceId }
  }
}

export { GetServiceController }
