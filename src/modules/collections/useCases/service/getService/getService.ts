import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { ServiceMapper } from '../../../mappers'
import { GenericServiceRepository } from '../../../repositories/interfaces/genericServiceRepository'
import { GetServiceDTO } from './getService.dto'

class GetService extends UseCase<GetServiceDTO, ServiceDTO> {
  constructor(private readonly _serviceRepository: GenericServiceRepository) {
    super(GetService.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetService.name)
  }

  async execute(request: Readonly<GetServiceDTO>): Promise<ServiceDTO> {
    const { id } = request
    const service = await this._serviceRepository.getById(
      new UniqueEntityID(id),
    )

    this.checkPermissions(service.collectionId)

    return ServiceMapper.getInstance().toDTO(service)
  }
}

export { GetService }
