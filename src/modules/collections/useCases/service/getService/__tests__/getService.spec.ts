import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubService } from '../../../../domain/__stubs__/service.stub'
import { stubServices } from '../../../../domain/__stubs__/services.stub'
import { Service } from '../../../../domain/service'
import {
  collectionRepository,
  serviceRepository,
} from '../../../../repositories'
import { GetService } from '../getService'
import { GetServiceDTO } from '../getService.dto'

describe('GetService Use Case', () => {
  const getServiceUseCase = new GetService(serviceRepository)
  let user: User
  let testServiceId: string
  let testService: Service

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const collection = stubCollection({
      name: 'Test Collection',
      services: stubServices(),
    })
    testService = stubService({
      name: 'Test Service',
      collectionId: collection.id,
    })
    collection.services?.add(testService)
    await collectionRepository.save(collection)
    testServiceId = testService.id.value
  })

  it('should return service DTO if service exists', async () => {
    const input: GetServiceDTO = { id: testServiceId }
    const result = await execute(getServiceUseCase, input, user)
    expect(result).toBeDefined()
    expect(result.id).toBe(testService.id.value)
    expect(result.name).toBe(testService.name)
    expect(result.description).toBe(testService.description)
    expect(result.category).toBe(testService.category)
    expect(result.availability).toBe(testService.availability)
    expect(result.link).toBe(testService.link.value.href)
    expect(result.collectionId).toBe(testService.collectionId.value)
  })

  it('should throw EntityNotFound if service does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const input: GetServiceDTO = { id: nonExistentId }
    await expect(execute(getServiceUseCase, input, user)).rejects.toThrow(
      new EntityNotFound('Service', nonExistentId),
    )
  })

  it('should throw if id is not a valid UUID', async () => {
    const input: GetServiceDTO = { id: 'not-a-uuid' }
    await expect(execute(getServiceUseCase, input, user)).rejects.toThrow()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })
})
