import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubService } from '../../../../domain/__stubs__/service.stub'
import { stubServices } from '../../../../domain/__stubs__/services.stub'
import { Service } from '../../../../domain/service'
import {
  collectionRepository,
  serviceRepository,
} from '../../../../repositories'
import { UpdateService } from '../updateService'
import { UpdateServiceInputDTO } from '../updateService.dto'

describe('UpdateService Use Case', () => {
  const updateServiceUseCase = new UpdateService(serviceRepository)
  let user: User
  let testServiceId: string
  let testService: Service

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const collection = stubCollection({
      name: 'Test Collection',
      services: stubServices(),
    })
    testService = stubService({
      name: 'Test Service',
      collectionId: collection.id,
    })
    collection.services?.add(testService)
    await collectionRepository.save(collection)
    testServiceId = testService.id.value

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
  })

  it('should update the service fields', async () => {
    const input: UpdateServiceInputDTO = {
      id: testServiceId,
      data: {
        name: 'Updated Service',
        description: 'Updated description',
        category: 'Updated Category',
        availability: true,
        link: 'https://example.com/updated',
      },
    }
    const result = await execute(updateServiceUseCase, input, user)
    expect(result).toBeDefined()
    expect(result.id).toBe(testService.id.value)
    expect(result.name).toBe(input.data.name)
    expect(result.description).toBe(input.data.description)
    expect(result.category).toBe(input.data.category)
    expect(result.availability).toBe(input.data.availability)
    expect(result.link).toBe(input.data.link)
  })

  it('should throw EntityNotFound if service does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const input: UpdateServiceInputDTO = {
      id: nonExistentId,
      data: { name: 'Should Not Exist' },
    }
    await expect(execute(updateServiceUseCase, input, user)).rejects.toThrow(
      new EntityNotFound('Service', nonExistentId),
    )
  })

  it('should throw if id is not a valid UUID', async () => {
    const input: UpdateServiceInputDTO = {
      id: 'not-a-uuid',
      data: { name: 'Invalid' },
    }
    await expect(execute(updateServiceUseCase, input, user)).rejects.toThrow()
  })

  it('should only update specified fields', async () => {
    const input: UpdateServiceInputDTO = {
      id: testServiceId,
      data: { name: 'Partially Updated' },
    }
    const result = await execute(updateServiceUseCase, input, user)
    expect(result.name).toBe('Partially Updated')
    expect(result.description).toBe(testService.description)
    expect(result.category).toBe(testService.category)
    expect(result.availability).toBe(testService.availability)
    expect(result.link).toBe(testService.link.value.href)
  })

  it('should not update the database when properties are unchanged', async () => {
    const input: UpdateServiceInputDTO = {
      id: testServiceId,
      data: {
        name: testService.name,
        description: testService.description,
        category: testService.category,
        availability: testService.availability,
      },
    }

    const saveSpy = jest.spyOn(serviceRepository, 'save')

    await execute(updateServiceUseCase, input, user)

    expect(saveSpy).not.toHaveBeenCalled()

    saveSpy.mockRestore()
  })

  it('should ignore undefined values in the update request', async () => {
    const initialName = testService.name
    const initialDescription = testService.description

    const input: UpdateServiceInputDTO = {
      id: testServiceId,
      data: {
        name: undefined,
        description: undefined,
        category: 'New Category',
        availability: !testService.availability,
      },
    }

    const result = await execute(updateServiceUseCase, input, user)

    expect(result.name).toBe(initialName)
    expect(result.description).toBe(initialDescription)

    expect(result.category).toBe('New Category')
    expect(result.availability).toBe(!testService.availability)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })
})
