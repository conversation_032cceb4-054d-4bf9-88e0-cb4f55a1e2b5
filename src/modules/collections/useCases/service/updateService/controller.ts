import { constants } from 'http2'
import { InferType, object, string, boolean } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { UpdateService } from './updateService'
import { UpdateServiceInputDTO } from './updateService.dto'

const schema = object({
  params: object({
    serviceId: string().uuid().required(),
  }).required(),
  body: object({
    name: string(),
    description: string(),
    category: string(),
    availability: boolean(),
    link: string().url(),
  }).required(),
})

class UpdateServiceController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateServiceInputDTO,
  ServiceDTO
> {
  public constructor(useCase: UpdateService) {
    super('patch', ':serviceId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): UpdateServiceInputDTO {
    return {
      id: request.params.serviceId,
      data: request.body,
    }
  }
}

export { UpdateServiceController }
