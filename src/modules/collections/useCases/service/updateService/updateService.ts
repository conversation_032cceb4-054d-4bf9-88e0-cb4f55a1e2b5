import { Link } from '../../../../../shared/domain/link'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { Service, ServiceProps } from '../../../domain/service'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { ServiceMapper } from '../../../mappers'
import { GenericServiceRepository } from '../../../repositories/interfaces/genericServiceRepository'
import { UpdateServiceInputDTO } from './updateService.dto'

class UpdateService extends UseCase<UpdateServiceInputDTO, ServiceDTO> {
  constructor(private readonly _serviceRepository: GenericServiceRepository) {
    super(UpdateService.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId))
      return

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(UpdateService.name)
  }

  async execute(request: Readonly<UpdateServiceInputDTO>): Promise<ServiceDTO> {
    const { id, data } = request
    const service = await this._serviceRepository.getById(
      new UniqueEntityID(id),
    )

    this.checkPermissions(service.collectionId)

    const updates = this.buildUpdateProps(data, service)

    if (Object.keys(updates).length > 0) {
      service.update(updates)
      await this._serviceRepository.save(service)
    }

    return ServiceMapper.getInstance().toDTO(service)
  }

  private buildUpdateProps(
    data: UpdateServiceInputDTO['data'],
    service: Service,
  ): Partial<ServiceProps> {
    const updates: Partial<ServiceProps> = {}

    // Only include properties that have actually changed
    if (data.name !== undefined && data.name !== service.name) {
      updates.name = data.name
    }

    if (
      data.description !== undefined &&
      data.description !== service.description
    ) {
      updates.description = data.description
    }

    if (data.category !== undefined && data.category !== service.category) {
      updates.category = data.category
    }

    if (
      data.availability !== undefined &&
      data.availability !== service.availability
    ) {
      updates.availability = data.availability
    }

    // Special handling for Link which needs conversion to Link object
    if (data.link !== undefined && service.link?.value?.href !== data.link) {
      updates.link = data.link ? new Link(data.link) : undefined
    }

    return updates
  }
}

export { UpdateService }
