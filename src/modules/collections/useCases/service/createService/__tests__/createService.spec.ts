import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import {
  collectionRepository,
  serviceRepository,
} from '../../../../repositories'
import { CreateService } from '../createService'
import { CreateServiceInputDTO } from '../createService.dto'

describe('CreateService Use Case', () => {
  const createServiceUseCase = new CreateService(serviceRepository)
  let user: User
  let testCollectionId: string

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const collection = stubCollection({
      name: 'Test Collection',
    })
    await collectionRepository.save(collection)
    testCollectionId = collection.id.value

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
  })

  it('should create a new service', async () => {
    const input: CreateServiceInputDTO = {
      collectionId: testCollectionId,
      name: 'Test Service',
      description: 'A test service',
      category: 'Test Category',
      availability: true,
      link: 'https://example.com/service',
    }
    const result = await execute(createServiceUseCase, input, user)
    expect(result).toBeDefined()
    expect(result.name).toBe(input.name)
    expect(result.description).toBe(input.description)
    expect(result.category).toBe(input.category)
    expect(result.availability).toBe(input.availability)
    expect(result.link).toBe(input.link)
    expect(result.collectionId).toBe(input.collectionId)
  })

  it('should persist the service and allow retrieval', async () => {
    const input: CreateServiceInputDTO = {
      collectionId: testCollectionId,
      name: 'Persisted Service',
      description: 'Should be persisted',
      category: 'Persistence',
      availability: true,
      link: 'https://example.com/persisted',
    }
    const result = await execute(createServiceUseCase, input, user)
    const found = await serviceRepository.getById(new UniqueEntityID(result.id))
    expect(found).toBeDefined()
    expect(found.name).toBe(input.name)
    expect(found.description).toBe(input.description)
    expect(found.category).toBe(input.category)
    expect(found.availability).toBe(input.availability)
    expect(found.link.value.href).toBe(input.link)
    expect(found.collectionId.value).toBe(input.collectionId)
  })

  it('should throw an error if required fields are missing', async () => {
    const input: Partial<CreateServiceInputDTO> = {
      collectionId: testCollectionId,
      description: 'Missing name',
      category: 'Error',
      availability: false,
      link: 'https://example.com/error',
    }
    await expect(execute(createServiceUseCase, input, user)).rejects.toThrow()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })
})
