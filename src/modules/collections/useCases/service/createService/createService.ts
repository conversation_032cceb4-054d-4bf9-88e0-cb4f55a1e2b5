import { Link } from '../../../../../shared/domain/link'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { Service } from '../../../domain/service'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { ServiceMapper } from '../../../mappers'
import { GenericServiceRepository } from '../../../repositories/interfaces/genericServiceRepository'
import { CreateServiceInputDTO } from './createService.dto'

class CreateService extends UseCase<CreateServiceInputDTO, ServiceDTO> {
  constructor(private readonly _serviceRepository: GenericServiceRepository) {
    super(CreateService.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId))
      return

    throw new NotAllowed(CreateService.name)
  }

  async execute(request: Readonly<CreateServiceInputDTO>): Promise<ServiceDTO> {
    const collectionId = new UniqueEntityID(request.collectionId)

    this.checkPermissions(collectionId)

    const service = new Service({
      collectionId: collectionId,
      name: request.name,
      description: request.description,
      category: request.category,
      availability: request.availability,
      link: new Link(request.link),
    })

    await this._serviceRepository.save(service)

    return ServiceMapper.getInstance().toDTO(service)
  }
}

export { CreateService }
