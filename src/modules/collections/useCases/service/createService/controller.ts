import { constants } from 'http2'
import { InferType, object, string, boolean } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { CreateService } from './createService'
import { CreateServiceInputDTO } from './createService.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  body: object({
    name: string().required(),
    description: string().required(),
    category: string().required(),
    availability: boolean().required(),
    link: string().url().required(),
  }).required(),
})

class CreateServiceController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  CreateServiceInputDTO,
  ServiceDTO
> {
  public constructor(useCase: CreateService) {
    super(
      'post',
      ':collectionId/services',
      constants.HTTP_STATUS_CREATED,
      useCase,
      schema,
    )
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): CreateServiceInputDTO {
    return {
      collectionId: request.params.collectionId,
      ...request.body,
    }
  }
}

export { CreateServiceController }
