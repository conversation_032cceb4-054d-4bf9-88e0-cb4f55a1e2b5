import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericServiceRepository } from '../../../repositories/interfaces/genericServiceRepository'
import { DeleteServiceDTO } from './deleteService.dto'

class DeleteService extends UseCase<DeleteServiceDTO> {
  constructor(private readonly _serviceRepository: GenericServiceRepository) {
    super(DeleteService.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId))
      return

    throw new NotAllowed(DeleteService.name)
  }

  async execute(request: Readonly<DeleteServiceDTO>): Promise<undefined> {
    const serviceId = new UniqueEntityID(request.id)

    const service = await this._serviceRepository.getById(serviceId)

    this.checkPermissions(service.collectionId)

    await this._serviceRepository.delete(serviceId)
  }
}

export { DeleteService }
