import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  truncateTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubService } from '../../../../domain/__stubs__/service.stub'
import { stubServices } from '../../../../domain/__stubs__/services.stub'
import {
  collectionRepository,
  serviceRepository,
} from '../../../../repositories'
import { DeleteService } from '../deleteService'
import { DeleteServiceDTO } from '../deleteService.dto'

describe('DeleteService Use Case', () => {
  const deleteServiceUseCase = new DeleteService(serviceRepository)
  let user: User
  let testServiceId: string

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const collection = stubCollection({
      name: 'Test Collection',
      services: stubServices(),
    })
    const service = stubService({
      name: 'Test Service',
      collectionId: collection.id,
    })
    collection.services?.add(service)
    await collectionRepository.save(collection)
    testServiceId = service.id.value

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
  })

  it('should soft delete an existing service', async () => {
    const input: DeleteServiceDTO = { id: testServiceId }
    await expect(
      execute(deleteServiceUseCase, input, user),
    ).resolves.toBeUndefined()
    await expect(
      serviceRepository.getById(new UniqueEntityID(testServiceId)),
    ).rejects.toThrow(EntityNotFound)

    await expect(
      serviceRepository.getById(new UniqueEntityID(testServiceId), {
        includeDeleted: true,
      }),
    ).resolves.not.toThrow()
  })

  it('should throw if service does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const input: DeleteServiceDTO = { id: nonExistentId }
    await expect(execute(deleteServiceUseCase, input, user)).rejects.toThrow(
      EntityNotFound,
    )
  })
  afterAll(async () => {
    await closeTestDatabase()
  })
})
