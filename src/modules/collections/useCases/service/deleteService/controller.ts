import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { DeleteService } from './deleteService'
import { DeleteServiceDTO } from './deleteService.dto'

const schema = object({
  params: object({
    serviceId: string().uuid().required(),
  }).required(),
})

class DeleteServiceController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteServiceDTO
> {
  public constructor(useCase: DeleteService) {
    super(
      'delete',
      ':serviceId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): DeleteServiceDTO {
    return { id: request.params.serviceId }
  }
}

export { DeleteServiceController }
