import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { Controller } from '../../../../../shared/infra/controller'
import { QueryParams, QuerySchema } from '../../../../../shared/types/query'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { GetServices } from './getServices'
import { GetServicesInputDTO } from './getServices.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  query: QuerySchema,
})

class GetServicesController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryParams,
  GetServicesInputDTO,
  PaginatedResponseDTO<ServiceDTO>
> {
  public constructor(useCase: GetServices) {
    super(
      'get',
      ':collectionId/services',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): GetServicesInputDTO {
    return { ...request.params, ...request.query }
  }
}

export { GetServicesController }
