import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubService } from '../../../../domain/__stubs__/service.stub'
import { stubServices } from '../../../../domain/__stubs__/services.stub'
import { Service } from '../../../../domain/service'
import {
  collectionRepository,
  serviceRepository,
} from '../../../../repositories'
import { GetServices } from '../getServices'
import { GetServicesInputDTO } from '../getServices.dto'

describe('GetServices Use Case', () => {
  const getServicesUseCase = new GetServices(serviceRepository)
  let user: User
  let testCollectionId: string
  let testServices: Service[]

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    const collection = stubCollection({
      name: 'Test Collection',
      services: stubServices(),
    })
    testServices = [
      stubService({ name: 'Service 1', collectionId: collection.id }),
      stubService({ name: 'Service 2', collectionId: collection.id }),
    ]
    testServices.forEach((service) => collection.services?.add(service))
    await collectionRepository.save(collection)
    testCollectionId = collection.id.value
  })

  it('should return all services for a collection', async () => {
    const input: GetServicesInputDTO = { collectionId: testCollectionId }
    const result = await execute(getServicesUseCase, input, user)
    expect(result.items).toHaveLength(testServices.length)
    const resultNames = result.items.map((s) => s.name).sort()
    const expectedNames = testServices.map((s) => s.name).sort()
    expect(resultNames).toEqual(expectedNames)
  })

  it('should return an empty array if no services exist for the collection', async () => {
    await truncateTestDatabase()
    const collection = stubCollection({
      name: 'Empty Collection',
      services: stubServices(),
    })
    await collectionRepository.save(collection)
    const input: GetServicesInputDTO = { collectionId: collection.id.value }
    const result = await execute(getServicesUseCase, input, user)
    expect(result.items).toEqual([])
    expect(result.totalItems).toEqual(0)
  })

  it('should throw if collectionId is not a valid UUID', async () => {
    const input: GetServicesInputDTO = { collectionId: 'not-a-uuid' }
    await expect(execute(getServicesUseCase, input, user)).rejects.toThrow()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })
})
