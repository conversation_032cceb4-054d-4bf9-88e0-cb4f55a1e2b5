import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { ServiceDTO } from '../../../dto/serviceDTO'
import { ServiceMapper } from '../../../mappers'
import { GenericServiceRepository } from '../../../repositories/interfaces/genericServiceRepository'
import { GetServicesInputDTO } from './getServices.dto'

class GetServices extends UseCase<
  GetServicesInputDTO,
  PaginatedResponseDTO<ServiceDTO>
> {
  constructor(private readonly _serviceRepository: GenericServiceRepository) {
    super(GetServices.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetServices.name)
  }

  async execute(
    request: GetServicesInputDTO,
  ): Promise<PaginatedResponseDTO<ServiceDTO>> {
    const collectionId = new UniqueEntityID(request.collectionId)

    this.checkPermissions(collectionId)

    const result = await this._serviceRepository.getAllByCollectionId(
      collectionId,
      {
        includeAll: false,
        ...request,
      },
    )

    return ServiceMapper.getInstance().toPageDTO(result)
  }
}

export { GetServices }
