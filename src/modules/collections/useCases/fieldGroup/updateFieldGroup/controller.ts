import { constants } from 'http2'
import { InferType, number, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { UpdateFieldGroup } from './updateFieldGroup'
import { UpdateFieldGroupInputDTO } from './updateFieldGroup.dto'

const schema = object({
  params: object({
    fieldGroupId: string().uuid().required(),
  }).required(),
  body: object({
    name: string(),
    position: number(),
  }).required(),
})

class UpdateFieldGroupController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateFieldGroupInputDTO,
  FieldGroupDTO
> {
  public constructor(useCase: UpdateFieldGroup) {
    super('patch', ':fieldGroupId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): UpdateFieldGroupInputDTO {
    return {
      id: request.params.fieldGroupId,
      data: request.body,
    }
  }
}

export { UpdateFieldGroupController }
