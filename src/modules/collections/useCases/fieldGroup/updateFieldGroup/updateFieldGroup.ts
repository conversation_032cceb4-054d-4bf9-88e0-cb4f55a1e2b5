import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { FieldCommonProps } from '../../../domain/field'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { FieldGroupMapper } from '../../../mappers'
import { GenericFieldGroupRepository } from '../../../repositories/interfaces/genericFieldGroupRepository'
import { UpdateFieldGroupInputDTO } from './updateFieldGroup.dto'

class UpdateFieldGroup extends UseCase<
  UpdateFieldGroupInputDTO,
  FieldGroupDTO
> {
  constructor(
    private readonly _fieldGroupRepository: GenericFieldGroupRepository,
  ) {
    super(UpdateFieldGroup.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(UpdateFieldGroup.name)
  }

  async execute(
    request: Readonly<UpdateFieldGroupInputDTO>,
  ): Promise<FieldGroupDTO> {
    const { id, data } = request
    const fieldGroup = await this._fieldGroupRepository.getById(
      new UniqueEntityID(id),
    )

    this.checkPermissions()

    const updates = this.buildUpdateProps(data, fieldGroup)

    if (Object.keys(updates).length > 0) {
      fieldGroup.update(updates)
      await this._fieldGroupRepository.save(fieldGroup)
    }

    return FieldGroupMapper.getInstance().toDTO(fieldGroup)
  }

  private buildUpdateProps(
    data: UpdateFieldGroupInputDTO['data'],
    fieldGroup: Awaited<ReturnType<GenericFieldGroupRepository['getById']>>,
  ): Partial<FieldCommonProps> {
    const updates: Partial<FieldCommonProps> = {}

    if (data.name !== undefined && data.name !== fieldGroup.name) {
      updates.name = data.name
    }

    if (data.position !== undefined && data.position !== fieldGroup.position) {
      updates.position = data.position
    }

    return updates
  }
}

export { UpdateFieldGroup }
