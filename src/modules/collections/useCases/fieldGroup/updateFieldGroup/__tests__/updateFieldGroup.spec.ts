import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import { stubFields } from '../../../../domain/__stubs__/fields.stub'
import { Collection } from '../../../../domain/collection'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../repositories'
import { UpdateFieldGroup } from '../updateFieldGroup'
import { UpdateFieldGroupInputDTO } from '../updateFieldGroup.dto'

describe('UpdateFieldGroup Use Case', () => {
  const updateFieldGroupUseCase = new UpdateFieldGroup(fieldGroupRepository)
  let user: User
  let collection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    collection = stubCollection({
      name: 'Test Collection',
      fieldGroups: stubFieldGroups(),
    })
  })

  it('should update an existing field group (name, position)', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Original Name', position: 0 })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)

    const updateDTO: UpdateFieldGroupInputDTO = {
      id: fieldGroup.id.value,
      data: {
        name: 'Updated Name',
        position: 1,
      },
    }

    await execute(updateFieldGroupUseCase, updateDTO, user)
    const updatedFieldGroup = await fieldGroupRepository.getById(fieldGroup.id)
    expect(updatedFieldGroup).toBeDefined()
    expect(updatedFieldGroup.id.value).toBe(fieldGroup.id.value)
    expect(updatedFieldGroup.name).toBe('Updated Name')
    expect(updatedFieldGroup.position).toBe(1)
  })

  it('should update a field group props and not change the fields', async () => {
    const fieldGroup = stubFieldGroup({
      name: 'Original Name',
      position: 0,
      fields: stubFields(),
    })
    collection.fieldGroups?.add(fieldGroup)

    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)

    await collectionRepository.save(collection)

    const updateDTO: UpdateFieldGroupInputDTO = {
      id: fieldGroup.id.value,
      data: {
        name: 'Updated Name',
        position: 1,
      },
    }

    await execute(updateFieldGroupUseCase, updateDTO, user)
    const updatedFieldGroup = await fieldGroupRepository.getById(fieldGroup.id)
    expect(updatedFieldGroup).toBeDefined()
    expect(updatedFieldGroup.id.value).toBe(fieldGroup.id.value)
    expect(updatedFieldGroup.name).toBe('Updated Name')
    expect(updatedFieldGroup.position).toBe(1)
    const fieldsResult = await fieldRepository.getById(field.id)
    expect(fieldsResult).toEqual(field)
  })

  it('should throw EntityNotFound when updating a non-existent field group', async () => {
    const nonExistentId = new UniqueEntityID().value
    const updateDTO: UpdateFieldGroupInputDTO = {
      id: nonExistentId,
      data: {
        name: 'Does Not Exist',
        position: 0,
      },
    }
    await expect(
      execute(updateFieldGroupUseCase, updateDTO, user),
    ).rejects.toThrow(new EntityNotFound('FieldGroup', nonExistentId))
  })

  it('should not update the database when properties are unchanged', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Original Name', position: 5 })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)
    const updateDTO: UpdateFieldGroupInputDTO = {
      id: fieldGroup.id.value,
      data: {
        name: 'Original Name',
        position: 5,
      },
    }

    const saveSpy = jest.spyOn(fieldGroupRepository, 'save')

    await execute(updateFieldGroupUseCase, updateDTO, user)
    expect(saveSpy).not.toHaveBeenCalled()

    saveSpy.mockRestore()
  })

  it('should update only the name property when only name is provided', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Original Name', position: 3 })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)

    const updateDTO: UpdateFieldGroupInputDTO = {
      id: fieldGroup.id.value,
      data: {
        name: 'Updated Name',
      },
    }

    await execute(updateFieldGroupUseCase, updateDTO, user)
    const updatedFieldGroup = await fieldGroupRepository.getById(fieldGroup.id)

    expect(updatedFieldGroup.name).toBe('Updated Name')
    expect(updatedFieldGroup.position).toBe(3)
  })

  it('should update only the position property when only position is provided', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Original Name', position: 2 })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)

    const updateDTO: UpdateFieldGroupInputDTO = {
      id: fieldGroup.id.value,
      data: {
        position: 10,
      },
    }

    await execute(updateFieldGroupUseCase, updateDTO, user)
    const updatedFieldGroup = await fieldGroupRepository.getById(fieldGroup.id)

    expect(updatedFieldGroup.name).toBe('Original Name')
    expect(updatedFieldGroup.position).toBe(10)
  })

  it('should ignore undefined values in the update request', async () => {
    const fieldGroup = stubFieldGroup({ name: 'Original Name', position: 2 })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)

    const updateDTO: UpdateFieldGroupInputDTO = {
      id: fieldGroup.id.value,
      data: {
        name: undefined,
        position: 10,
      },
    }

    await execute(updateFieldGroupUseCase, updateDTO, user)
    const updatedFieldGroup = await fieldGroupRepository.getById(fieldGroup.id)

    expect(updatedFieldGroup.name).toBe('Original Name')
    expect(updatedFieldGroup.position).toBe(10)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })
})
