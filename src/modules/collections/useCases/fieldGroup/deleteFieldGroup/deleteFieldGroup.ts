import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericFieldGroupRepository } from '../../../repositories/interfaces/genericFieldGroupRepository'
import { DeleteFieldGroupDTO } from './deleteFieldGroup.dto'

class DeleteFieldGroup extends UseCase<DeleteFieldGroupDTO> {
  constructor(
    private readonly _fieldGroupRepository: GenericFieldGroupRepository,
  ) {
    super(DeleteFieldGroup.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(DeleteFieldGroup.name)
  }

  async execute(request: Readonly<DeleteFieldGroupDTO>): Promise<undefined> {
    const fieldGroupId = new UniqueEntityID(request.id)

    this.checkPermissions()

    await this._fieldGroupRepository.delete(fieldGroupId)
  }
}

export { DeleteFieldGroup }
