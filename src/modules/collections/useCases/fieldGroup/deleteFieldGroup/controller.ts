import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { DeleteFieldGroup } from './deleteFieldGroup'
import { DeleteFieldGroupDTO } from './deleteFieldGroup.dto'

const schema = object({
  params: object({
    fieldGroupId: string().uuid().required(),
  }).required(),
})

class DeleteFieldGroupController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteFieldGroupDTO
> {
  public constructor(useCase: DeleteFieldGroup) {
    super(
      'delete',
      ':fieldGroupId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): DeleteFieldGroupDTO {
    return { id: request.params.fieldGroupId }
  }
}

export { DeleteFieldGroupController }
