import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../repositories'
import { DeleteFieldGroup } from '../deleteFieldGroup'
import { DeleteFieldGroupDTO } from '../deleteFieldGroup.dto'

describe('DeleteFieldGroup Use Case', () => {
  const deleteFieldGroupUseCase = new DeleteFieldGroup(fieldGroupRepository)
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })
  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should delete an existing field group', async () => {
    const collection = stubCollection({
      name: 'Test Collection',
      fieldGroups: stubFieldGroups(),
    })
    const fieldGroup = stubFieldGroup({ name: 'Group to Delete' })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)

    const deleteFieldGroupDTO: DeleteFieldGroupDTO = {
      id: fieldGroup.id.value,
    }
    // Ensure the field group exists before deletion
    const entity = await fieldGroupRepository.getById(fieldGroup.id)
    expect(entity).toBeDefined()
    await expect(
      execute(deleteFieldGroupUseCase, deleteFieldGroupDTO, user),
    ).resolves.toBeUndefined()
    await expect(fieldGroupRepository.getById(fieldGroup.id)).rejects.toThrow()
  })

  it('should throw EntityNotFound when deleting a non-existent field group', async () => {
    const nonExistentId = new UniqueEntityID().value
    const deleteFieldGroupDTO: DeleteFieldGroupDTO = {
      id: nonExistentId,
    }
    await expect(
      execute(deleteFieldGroupUseCase, deleteFieldGroupDTO, user),
    ).rejects.toThrow(new EntityNotFound('FieldGroup', nonExistentId))
  })
})
