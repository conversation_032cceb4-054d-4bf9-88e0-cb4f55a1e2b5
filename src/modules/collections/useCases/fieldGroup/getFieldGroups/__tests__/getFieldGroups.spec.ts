import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../repositories'
import { GetFieldGroups } from '../getFieldGroups'
import { GetFieldGroupsInputDTO } from '../getFieldGroups.dto'

describe('GetFieldGroups Use Case', () => {
  const getFieldGroupsUseCase = new GetFieldGroups(fieldGroupRepository)
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should retrieve all field groups for a collection', async () => {
    const collection = stubCollection({
      name: 'Test Collection',
      fieldGroups: stubFieldGroups(),
    })
    const fieldGroup1 = stubFieldGroup({ name: 'Group 1' })
    const fieldGroup2 = stubFieldGroup({ name: 'Group 2' })
    collection.fieldGroups?.add(fieldGroup1)
    collection.fieldGroups?.add(fieldGroup2)
    await collectionRepository.save(collection)

    const getFieldGroupsDTO: GetFieldGroupsInputDTO = {
      collectionId: collection.id.value,
    }
    const result = await execute(getFieldGroupsUseCase, getFieldGroupsDTO, user)
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)
    expect(result.length).toBe(2)
    const names = result.map((fg) => fg.name)
    expect(names).toEqual(expect.arrayContaining(['Group 1', 'Group 2']))
  })

  it('should return an empty array if no field groups exist for the collection', async () => {
    const collection = stubCollection({
      name: 'Empty Collection',
      fieldGroups: stubFieldGroups(),
    })
    await collectionRepository.save(collection)

    const getFieldGroupsDTO: GetFieldGroupsInputDTO = {
      collectionId: collection.id.value,
    }
    const result = await execute(getFieldGroupsUseCase, getFieldGroupsDTO, user)
    expect(result).toEqual([])
  })
})
