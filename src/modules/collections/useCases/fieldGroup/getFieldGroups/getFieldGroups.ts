import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { FieldGroupMapper } from '../../../mappers'
import { GenericFieldGroupRepository } from '../../../repositories/interfaces/genericFieldGroupRepository'
import { GetFieldGroupsInputDTO } from './getFieldGroups.dto'

class GetFieldGroups extends UseCase<GetFieldGroupsInputDTO, FieldGroupDTO[]> {
  constructor(
    private readonly _fieldGroupRepository: GenericFieldGroupRepository,
  ) {
    super(GetFieldGroups.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(GetFieldGroups.name)
  }

  async execute(
    request: Readonly<GetFieldGroupsInputDTO>,
  ): Promise<FieldGroupDTO[]> {
    const { collectionId } = request

    this.checkPermissions()

    const fieldGroups = await this._fieldGroupRepository.getAllByCollectionId(
      new UniqueEntityID(collectionId),
    )

    return fieldGroups.map((fieldGroup) =>
      FieldGroupMapper.getInstance().toDTO(fieldGroup),
    )
  }
}

export { GetFieldGroups }
