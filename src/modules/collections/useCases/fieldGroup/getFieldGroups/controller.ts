import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { GetFieldGroups } from './getFieldGroups'
import { GetFieldGroupsInputDTO } from './getFieldGroups.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
})

class GetFieldGroupsController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetFieldGroupsInputDTO,
  FieldGroupDTO[]
> {
  public constructor(useCase: GetFieldGroups) {
    super(
      'get',
      ':collectionId/field-groups',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): GetFieldGroupsInputDTO {
    return { collectionId: request.params.collectionId }
  }
}

export { GetFieldGroupsController }
