import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { FieldGroupMapper } from '../../../mappers'
import { GenericFieldGroupRepository } from '../../../repositories/interfaces/genericFieldGroupRepository'
import { GetFieldGroupDTO } from './getFieldGroup.dto'

class GetFieldGroup extends UseCase<GetFieldGroupDTO, FieldGroupDTO> {
  constructor(
    private readonly _fieldGroupRepository: GenericFieldGroupRepository,
  ) {
    super(GetFieldGroup.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(GetFieldGroup.name)
  }

  async execute(request: Readonly<GetFieldGroupDTO>): Promise<FieldGroupDTO> {
    const { id } = request

    const fieldGroup = await this._fieldGroupRepository.getById(
      new UniqueEntityID(id),
    )

    this.checkPermissions()

    return FieldGroupMapper.getInstance().toDTO(fieldGroup)
  }
}

export { GetFieldGroup }
