import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { stubFieldGroup } from '../../../../domain/__stubs__/fieldGroup.stub'
import { stubFieldGroups } from '../../../../domain/__stubs__/fieldGroups.stub'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../repositories'
import { GetFieldGroup } from '../getFieldGroup'
import { GetFieldGroupDTO } from '../getFieldGroup.dto'

describe('GetFieldGroup Use Case', () => {
  const getFieldGroupUseCase = new GetFieldGroup(fieldGroupRepository)
  let user: User

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should retrieve an existing field group', async () => {
    const collection = stubCollection({
      name: 'Test Collection',
      fieldGroups: stubFieldGroups(),
    })
    const fieldGroup = stubFieldGroup({
      name: 'Group to Retrieve',
    })
    collection.fieldGroups?.add(fieldGroup)
    await collectionRepository.save(collection)

    const getFieldGroupDTO: GetFieldGroupDTO = {
      id: fieldGroup.id.value,
    }
    const result = await execute(getFieldGroupUseCase, getFieldGroupDTO, user)
    expect(result).toBeDefined()
    expect(result.id).toBe(fieldGroup.id.value)
    expect(result.name).toBe('Group to Retrieve')
    expect(result.position).toBe(fieldGroup.position)
  })

  it('should throw EntityNotFound when retrieving a non-existent field group', async () => {
    const nonExistentId = new UniqueEntityID().value
    const getFieldGroupDTO: GetFieldGroupDTO = {
      id: nonExistentId,
    }
    await expect(
      execute(getFieldGroupUseCase, getFieldGroupDTO, user),
    ).rejects.toThrow(new EntityNotFound('FieldGroup', nonExistentId))
  })
})
