import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { GetFieldGroup } from './getFieldGroup'
import { GetFieldGroupDTO } from './getFieldGroup.dto'

const schema = object({
  params: object({
    fieldGroupId: string().uuid().required(),
  }).required(),
})

class GetFieldGroupController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetFieldGroupDTO,
  FieldGroupDTO
> {
  public constructor(useCase: GetFieldGroup) {
    super('get', ':fieldGroupId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetFieldGroupDTO {
    return { id: request.params.fieldGroupId }
  }
}

export { GetFieldGroupController }
