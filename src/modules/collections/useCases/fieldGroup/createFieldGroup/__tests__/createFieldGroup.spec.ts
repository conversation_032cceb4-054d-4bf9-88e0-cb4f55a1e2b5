import {
  truncateTestDatabase,
  setupTestDatabase,
  closeTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { stubCollection } from '../../../../domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../repositories'
import { CreateFieldGroup } from '../createFieldGroup'
import { CreateFieldGroupDTO } from '../createFieldGroup.dto'

describe('CreateFieldGroup Use Case', () => {
  const createFieldGroupUseCase = new CreateFieldGroup(collectionRepository)

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
  })
  afterAll(async () => {
    await closeTestDatabase()
  })

  it('should create a field group with the correct fields and return the correct DTO', async () => {
    const collection = stubCollection({ name: 'Test Collection' })
    await collectionRepository.save(collection)

    const user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )

    const createFieldGroupDTO: CreateFieldGroupDTO = {
      name: 'Test Field Group',
      position: 0,
      collectionId: collection.id.value,
      fields: [
        {
          name: 'Field 1',
          position: 0,
          mandatory: true,
          visibility: 'PUBLIC',
          type: 'TEXT',
          maxLength: 100,
          preview: true,
        },
        {
          name: 'Field 2',
          position: 1,
          mandatory: true,
          visibility: 'PUBLIC',
          type: 'SELECT',
          preview: true,
          options: [
            {
              label: 'Option 1',
              value: 'option1',
            },
          ],
        },
      ],
    }

    const resultDTO = await execute(
      createFieldGroupUseCase,
      createFieldGroupDTO,
      user,
    )

    expect(resultDTO).toBeDefined()
    expect(resultDTO.name).toBe('Test Field Group')
    expect(resultDTO.position).toBe(0)
    expect(resultDTO.fields).toBeDefined()
    expect(resultDTO.fields?.length).toBe(2)
    const fieldNames = resultDTO.fields?.map((f) => f.name)
    expect(fieldNames).toEqual(expect.arrayContaining(['Field 1', 'Field 2']))
  })

  it('should create a field group without fields', async () => {
    const collection = stubCollection({ name: 'Test Collection' })
    await collectionRepository.save(collection)

    const user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )

    const createFieldGroupDTO: CreateFieldGroupDTO = {
      name: 'Test Field Group',
      position: 0,
      collectionId: collection.id.value,
      fields: [],
    }

    const resultDTO = await execute(
      createFieldGroupUseCase,
      createFieldGroupDTO,
      user,
    )

    expect(resultDTO).toBeDefined()
    expect(resultDTO.name).toBe('Test Field Group')
    expect(resultDTO.position).toBe(0)
    expect(resultDTO.fields).toBeDefined()
    expect(resultDTO.fields?.length).toBe(0)
  })
})
