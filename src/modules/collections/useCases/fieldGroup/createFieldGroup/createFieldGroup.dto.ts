import { CreateDTO } from '../../../../../shared/dto/createDTO'
import { IdDTO } from '../../../../../shared/dto/idDTO'
import { FieldDTO } from '../../../dto/fieldDTO'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'

type FieldDTOWithoutGroupId = CreateDTO<Omit<FieldDTO, 'fieldGroupId'>>

type CreateFieldGroupDTO = CreateDTO<
  Omit<FieldGroupDTO, 'fields'> & {
    collectionId: IdDTO
    fields?: FieldDTOWithoutGroupId[]
  }
>

export type { CreateFieldGroupDTO, FieldDTOWithoutGroupId }
