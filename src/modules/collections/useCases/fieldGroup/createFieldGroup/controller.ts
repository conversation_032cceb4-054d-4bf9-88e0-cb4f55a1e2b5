import { constants } from 'http2'
import { array, InferType, number, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { CreateFieldGroup } from './createFieldGroup'
import { CreateFieldGroupDTO } from './createFieldGroup.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  body: object({
    name: string().required(),
    position: number().required(),
    fieldIds: array(string().uuid().required()).required(),
  }).required(),
})

class CreateFieldGroupController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  CreateFieldGroupDTO,
  FieldGroupDTO
> {
  public constructor(useCase: CreateFieldGroup) {
    super(
      'post',
      ':collectionId/field-groups',
      constants.HTTP_STATUS_CREATED,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): CreateFieldGroupDTO {
    return {
      collectionId: request.params.collectionId,
      ...request.body,
    }
  }
}

export { CreateFieldGroupController }
