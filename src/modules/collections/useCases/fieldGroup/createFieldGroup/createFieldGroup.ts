import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { Field } from '../../../domain/field'
import { FieldGroup } from '../../../domain/fieldGroup'
import { Fields } from '../../../domain/fields'
import { FieldGroupDTO } from '../../../dto/fieldGroupDTO'
import { FieldGroupMapper } from '../../../mappers'
import { GenericCollectionRepository } from '../../../repositories/interfaces/genericCollectionRepository'
import {
  CreateFieldGroupDTO,
  FieldDTOWithoutGroupId,
} from './createFieldGroup.dto'

class CreateFieldGroup extends UseCase<CreateFieldGroupDTO, FieldGroupDTO> {
  constructor(
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super(CreateFieldGroup.name)
  }

  private createFieldFromDTO(
    field: FieldDTOWithoutGroupId,
    fieldGroupId: UniqueEntityID,
  ): Field {
    const {
      name,
      position,
      mandatory,
      visibility,
      type,
      options,
      maxLength,
      preview,
    } = field

    if (type === 'SELECT' || type === 'SELECT-MANY') {
      return new Field({
        name,
        fieldGroupId,
        position,
        mandatory,
        visibility,
        type,
        options: options ?? [],
        maxLength,
        preview,
      })
    } else {
      return new Field({
        name,
        fieldGroupId,
        position,
        mandatory,
        visibility,
        type,
        maxLength,
        preview,
      })
    }
  }

  private createFields(
    fieldDTOs: FieldDTOWithoutGroupId[] = [],
    fieldGroupId: UniqueEntityID,
  ): Field[] | undefined {
    if (fieldDTOs.length === 0) {
      return undefined
    }

    const fieldEntities = fieldDTOs.map((fieldDTO) =>
      this.createFieldFromDTO(fieldDTO, fieldGroupId),
    )
    return fieldEntities
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId))
      return

    throw new NotAllowed(CreateFieldGroup.name)
  }

  async execute(
    request: Readonly<CreateFieldGroupDTO>,
  ): Promise<FieldGroupDTO> {
    const collection = await this._collectionRepository.getById(
      new UniqueEntityID(request.collectionId),
    )

    this.checkPermissions(collection.id)

    const fieldGroup = new FieldGroup({
      name: request.name,
      position: request.position,
      fields: new Fields([]),
    })

    const fields = this.createFields(request.fields, fieldGroup.id)
    if (fields) {
      fields.forEach((field) => fieldGroup.fields?.add(field))
    }
    collection.fieldGroups?.add(fieldGroup)

    await this._collectionRepository.save(collection)

    return FieldGroupMapper.getInstance().toDTO(fieldGroup)
  }
}

export { CreateFieldGroup }
