import { TEntityDTO } from '../../../shared/dto/entityDTO'
import { IdDTO } from '../../../shared/dto/idDTO'
import { FieldOption, FieldType, FieldVisibility } from '../domain/field'

interface FieldDTO extends TEntityDTO {
  name: string
  fieldGroupId: IdDTO
  position: number
  maxLength?: number
  mandatory: boolean
  visibility: FieldVisibility
  type: FieldType
  options?: FieldOption[]
  preview: boolean
}

export type { FieldDTO }
