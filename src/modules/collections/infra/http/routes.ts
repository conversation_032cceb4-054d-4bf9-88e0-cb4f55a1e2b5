import { Route } from '../../../../shared/infra/http/routes/route'
import { getActivitiesController } from '../../../activities/useCases/getActivities'
import { createEntryController } from '../../../entries/useCases/entry/createEntry'
import { getEntriesController } from '../../../entries/useCases/entry/getEntries'
import { getCollectionController } from '../../useCases/collection/getCollection'
import { getCollectionInvitesController } from '../../useCases/collection/getCollectionInvites'
import { getCollectionsController } from '../../useCases/collection/getCollections'
import { getStatsController } from '../../useCases/collection/getCollectionStats'
import { getCollectionUsersController } from '../../useCases/collection/getCollectionUsers'
import { updateCollectionController } from '../../useCases/collection/updateCollection'
import { createFieldController } from '../../useCases/field/createField'
import { deleteFieldController } from '../../useCases/field/deleteField'
import { getFieldController } from '../../useCases/field/getField'
import { updateFieldController } from '../../useCases/field/updateField'
import { createFieldGroupController } from '../../useCases/fieldGroup/createFieldGroup'
import { deleteFieldGroupController } from '../../useCases/fieldGroup/deleteFieldGroup'
import { getFieldGroupController } from '../../useCases/fieldGroup/getFieldGroup'
import { getFieldGroupsController } from '../../useCases/fieldGroup/getFieldGroups'
import { updateFieldGroupController } from '../../useCases/fieldGroup/updateFieldGroup'
import { createServiceController } from '../../useCases/service/createService'
import { deleteServiceController } from '../../useCases/service/deleteService'
import { getServiceController } from '../../useCases/service/getService'
import { getServicesController } from '../../useCases/service/getServices'
import { updateServiceController } from '../../useCases/service/updateService'
import { deleteUserAssignmentController } from '../../useCases/user/deleteUserAssignment'
import { updateUserAssignmentController } from '../../useCases/user/updateUserAssignment'

const collectionsRoute = new Route(
  'collections',
  getStatsController,
  getCollectionsController,
  getCollectionController,
  updateCollectionController,
  getActivitiesController,
  getEntriesController,
  createEntryController,
  getFieldGroupsController,
  createFieldGroupController,
  getServicesController,
  createServiceController,
  getCollectionUsersController,
  getCollectionInvitesController,
  updateUserAssignmentController,
  deleteUserAssignmentController,
)

const servicesRoute = new Route(
  'services',
  getServiceController,
  updateServiceController,
  deleteServiceController,
)

const fieldsRoute = new Route(
  'fields',
  getFieldController,
  updateFieldController,
  deleteFieldController,
)

const fieldGroupsRoute = new Route(
  'field-groups',
  getFieldGroupController,
  updateFieldGroupController,
  deleteFieldGroupController,
  createFieldController,
)

export { collectionsRoute, servicesRoute, fieldsRoute, fieldGroupsRoute }
