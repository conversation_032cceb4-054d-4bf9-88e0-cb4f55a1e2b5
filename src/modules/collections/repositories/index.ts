import { database } from '../../../shared/database'
import { CollectionRepository } from './collectionRepository'
import { FieldGroupRepository } from './fieldGroupRepository'
import { FieldRepository } from './fieldRepository'
import { ServiceRepository } from './serviceRepository'

const fieldRepository = new FieldRepository(database)
const fieldGroupRepository = new FieldGroupRepository(database)
const serviceRepository = new ServiceRepository(database)
const collectionRepository = new CollectionRepository(database)

export {
  fieldRepository,
  fieldGroupRepository,
  collectionRepository,
  serviceRepository,
}
