import { DatabaseClient } from '../../../shared/database'
import { fieldsTable } from '../../../shared/database/schema'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { Field } from '../domain/field'
import { FieldDTO } from '../dto/fieldDTO'
import { FieldMapper } from '../mappers/fieldMapper'
import { FieldModel } from '../models/field.model'
import { GenericFieldRepository } from './interfaces/genericFieldRepository'

class FieldRepository
  extends AbstractDrizzleRepository<
    typeof fieldsTable,
    Field,
    FieldModel,
    FieldDTO
  >
  implements GenericFieldRepository
{
  constructor(db: DatabaseClient) {
    super('Field', db, FieldMapper.getInstance(), fieldsTable, 'fieldsTable')
  }
}

export { FieldRepository }
