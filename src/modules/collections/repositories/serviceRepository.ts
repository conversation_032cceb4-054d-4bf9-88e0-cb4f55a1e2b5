import { eq, count } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { servicesTable } from '../../../shared/database/schema/services'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Service } from '../domain/service'
import { ServiceDTO } from '../dto/serviceDTO'
import { ServiceMapper } from '../mappers/serviceMapper'
import { ServiceModel } from '../models/service.model'
import { GenericServiceRepository } from './interfaces/genericServiceRepository'

class ServiceRepository
  extends AbstractDrizzleRepository<
    typeof servicesTable,
    Service,
    ServiceModel,
    ServiceDTO
  >
  implements GenericServiceRepository
{
  constructor(db: DatabaseClient) {
    super(
      'Service',
      db,
      ServiceMapper.getInstance(),
      servicesTable,
      'servicesTable',
    )
  }
  async getAllByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Service>> {
    const whereConditions = [eq(servicesTable.collectionId, collectionId.value)]

    const queryOptions = this.buildQueryOptions(
      { ...options, includeAll: false },
      whereConditions,
    )

    const [countResult] = await this.db
      .select({ totalCount: count() })
      .from(this.table)
      .where(whereConditions[0])

    const totalCount = countResult?.totalCount ?? 0

    const services = await this.db.query.servicesTable.findMany(queryOptions)

    return {
      items: services.map((service) => this.mapper.toDomain(service)),
      total: totalCount,
    }
  }
}

export { ServiceRepository }
