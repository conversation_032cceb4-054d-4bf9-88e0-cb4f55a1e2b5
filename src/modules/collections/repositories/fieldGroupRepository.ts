import { eq, inArray } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import {
  collectionFieldGroupsTable,
  fieldGroupsTable,
} from '../../../shared/database/schema'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { FieldGroup } from '../domain/fieldGroup'
import { FieldGroupDTO } from '../dto/fieldGroupDTO'
import { FieldGroupMapper } from '../mappers/fieldGroupMapper'
import { FieldGroupModel } from '../models/fieldGroup.model'
import { GenericFieldGroupRepository } from './interfaces/genericFieldGroupRepository'
import { fieldRepository } from '.'

class FieldGroupRepository
  extends AbstractDrizzleRepository<
    typeof fieldGroupsTable,
    FieldGroup,
    FieldGroupModel,
    FieldGroupDTO
  >
  implements GenericFieldGroupRepository
{
  constructor(db: DatabaseClient) {
    super(
      'FieldGroup',
      db,
      FieldGroupMapper.getInstance(),
      fieldGroupsTable,
      'fieldGroupsTable',
    )
  }

  protected override async processRelatedEntities(
    domain: FieldGroup,
    dbClient: DatabaseClient,
  ): Promise<void> {
    if (domain.fields !== undefined) {
      await fieldRepository.saveMany(domain.fields.getNewItems(), dbClient)
      await fieldRepository.saveMany(domain.fields.getItems(), dbClient)
      for (const field of domain.fields.getRemovedItems()) {
        await fieldRepository.delete(field.id, dbClient)
      }
    }
  }

  async getAllByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<FieldGroup[]> {
    try {
      // get all field groups that belong to the collection
      const fieldGroupIds = await this.db
        .select({
          fieldGroupId: collectionFieldGroupsTable.fieldGroupId,
        })
        .from(collectionFieldGroupsTable)
        .where(eq(collectionFieldGroupsTable.collectionId, collectionId.value))

      if (fieldGroupIds.length === 0) {
        return []
      }

      const ids = fieldGroupIds.map((fg) => fg.fieldGroupId)

      const whereConditions = [inArray(fieldGroupsTable.id, ids)]
      const queryOptions = this.buildQueryOptions(options, whereConditions)
      const fieldGroups = await this.db.query.fieldGroupsTable.findMany(
        queryOptions as any,
      )

      return fieldGroups.map((fg) => this.mapper.toDomain(fg))
    } catch (error) {
      this.handleError(
        error,
        `find field groups by collection id ${collectionId.value}`,
      )
    }
  }
}

export { FieldGroupRepository }
