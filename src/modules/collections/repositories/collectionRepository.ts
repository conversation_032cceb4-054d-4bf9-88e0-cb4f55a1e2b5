import { and, eq, inArray, isNot<PERSON>ull, sql } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import {
  collectionsTable,
  rolesTable,
  collectionFieldGroupsTable,
  entriesTable,
} from '../../../shared/database/schema'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { SoftDeleteFilter } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { Collection } from '../domain/collection'
import { Statistic } from '../domain/statistic'
import { CollectionDTO } from '../dto/collectionDTO'
import { CollectionMapper } from '../mappers/collectionMapper'
import { StatisticMapper } from '../mappers/statisticMapper'
import { CollectionModel } from '../models/collection.model'
import { GenericCollectionRepository } from './interfaces/genericCollectionRepository'
import { fieldGroupRepository, serviceRepository } from '.'

class CollectionRepository
  extends AbstractDrizzleRepository<
    typeof collectionsTable,
    Collection,
    CollectionModel,
    CollectionDTO
  >
  implements GenericCollectionRepository
{
  constructor(db: DatabaseClient) {
    super(
      'Collection',
      db,
      CollectionMapper.getInstance(),
      collectionsTable,
      'collectionsTable',
    )
  }

  protected override async processRelatedEntities(
    domain: Collection,
    dbClient: DatabaseClient,
  ): Promise<void> {
    // Field Groups
    if (domain.fieldGroups !== undefined) {
      for (const fieldGroup of domain.fieldGroups.getNewItems()) {
        await fieldGroupRepository.save(fieldGroup, dbClient)
        await dbClient
          .insert(collectionFieldGroupsTable)
          .values({
            collectionId: domain.id.value,
            fieldGroupId: fieldGroup.id.value,
          })
          .onConflictDoNothing()
      }
      for (const fieldGroup of domain.fieldGroups.getItems()) {
        await fieldGroupRepository.save(fieldGroup, dbClient)
        await dbClient
          .insert(collectionFieldGroupsTable)
          .values({
            collectionId: domain.id.value,
            fieldGroupId: fieldGroup.id.value,
          })
          .onConflictDoNothing()
      }
      for (const fieldGroup of domain.fieldGroups.getRemovedItems()) {
        await fieldGroupRepository.delete(fieldGroup.id, dbClient)
        await dbClient
          .delete(collectionFieldGroupsTable)
          .where(
            and(
              eq(collectionFieldGroupsTable.collectionId, domain.id.value),
              eq(collectionFieldGroupsTable.fieldGroupId, fieldGroup.id.value),
            ),
          )
      }
    }

    // Services
    if (domain.services !== undefined) {
      await serviceRepository.saveMany(domain.services.getNewItems(), dbClient)
      await serviceRepository.saveMany(domain.services.getItems(), dbClient)
      for (const service of domain.services.getRemovedItems()) {
        await serviceRepository.delete(service.id, dbClient)
      }
    }
  }

  async getAllForUser(
    userId: UniqueEntityID,
    options?: SoftDeleteFilter,
  ): Promise<Collection[]> {
    const userCollections = await this.db
      .select({ collectionId: rolesTable.collectionId })
      .from(rolesTable)
      .where(
        and(
          eq(rolesTable.userId, userId.value),
          options?.includeDeleted ? undefined : eq(rolesTable.deleted, false),
        ),
      )

    if (userCollections.length === 0) {
      return []
    }

    const collectionIds = userCollections
      .map((role) => role.collectionId)
      .filter((id): id is string => id !== null)

    // TODO: Pagination? should include relations?
    const rawResults = await this.db
      .select()
      .from(this.table)
      .where(
        and(
          inArray(this.table.id, collectionIds),
          options?.includeDeleted ? undefined : eq(this.table.deleted, false),
        ),
      )

    return rawResults.map((result) => this.mapper.toDomain(result))
  }

  async getAll(): Promise<Collection[]> {
    const collections = await this.db.query.collectionsTable.findMany()
    return collections.map((collection) => this.mapper.toDomain(collection))
  }

  async getStats(userId: UniqueEntityID): Promise<Statistic[]> {
    const userCollections = await this.db
      .select({ collectionId: rolesTable.collectionId })
      .from(rolesTable)
      .where(eq(rolesTable.userId, userId.value))

    const collectionIds = userCollections.reduce<string[]>((acc, role) => {
      if (role.collectionId) {
        acc.push(role.collectionId)
      }
      return acc
    }, [])

    if (collectionIds.length === 0) return []

    const statsResult = await this.db
      .select({
        collectionId: rolesTable.collectionId,
        members: sql<number>`count(DISTINCT ${rolesTable.id})`.as('members'),
        entries: sql<number>`count(DISTINCT ${entriesTable.id})`.as('entries'),
        publicEntries: sql<number>`
      count(DISTINCT ${entriesTable.id}) 
      filter (where ${entriesTable.visibility} = 'PUBLIC')
    `.as('public_entries'),
      })
      .from(rolesTable)
      .leftJoin(
        entriesTable,
        and(
          eq(entriesTable.collectionId, rolesTable.collectionId),
          eq(entriesTable.deleted, false),
          isNotNull(entriesTable.collectionId),
        ),
      )
      .where(
        and(
          eq(rolesTable.deleted, false),
          inArray(rolesTable.collectionId, collectionIds),
          isNotNull(rolesTable.collectionId),
        ),
      )
      .groupBy(rolesTable.collectionId)

    return statsResult
      .filter((stat) => stat.collectionId !== null)
      .map((stat) =>
        StatisticMapper.getInstance().toDomain({
          ...stat,
          collectionId: stat.collectionId as string,
        }),
      )
  }
}

export { CollectionRepository }
