import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { FieldGroup } from '../../domain/fieldGroup'

interface GenericFieldGroupRepository extends GenericRepository<FieldGroup> {
  getAllByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<FieldGroup[]>
}

export type { GenericFieldGroupRepository }
