import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  GenericRepository,
  SoftDeleteFilter,
} from '../../../../shared/repositories/genericRepository'
import { Collection } from '../../domain/collection'
import { Statistic } from '../../domain/statistic'

interface GenericCollectionRepository extends GenericRepository<Collection> {
  getAllForUser(
    userId: UniqueEntityID,
    options?: SoftDeleteFilter,
  ): Promise<Collection[]>
  getAll(): Collection[] | Promise<Collection[]>
  getStats(userId: UniqueEntityID): Statistic[] | Promise<Statistic[]>
}

export type { GenericCollectionRepository }
