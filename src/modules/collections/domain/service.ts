import { Entity } from '../../../shared/domain/entity'
import { Link } from '../../../shared/domain/link'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'

type ServiceProps = {
  collectionId: UniqueEntityID
  name: string
  description: string
  category: string
  availability: boolean
  link: Link
}

class Service extends Entity<ServiceProps> {
  constructor(
    props: ServiceProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get collectionId() {
    return this.getProp('collectionId')
  }

  get name() {
    return this.getProp('name')
  }

  get description() {
    return this.getProp('description')
  }

  get category() {
    return this.getProp('category')
  }

  get availability() {
    return this.getProp('availability')
  }

  get link() {
    return this.getProp('link')
  }
}

export { Service, type ServiceProps }
