import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { ValueObject } from '../../../shared/domain/valueObject'

type StatisticProps = {
  collectionId: UniqueEntityID
  entries: {
    total: number
    public: number
  }
  members: number
}

class Statistic extends ValueObject<StatisticProps> {
  constructor(props: StatisticProps) {
    super(props)
  }

  get collectionId(): UniqueEntityID {
    return this.props.collectionId
  }

  get entries(): { total: number; public: number } {
    return this.props.entries
  }

  get members(): number {
    return this.props.members
  }
}

export { Statistic }
