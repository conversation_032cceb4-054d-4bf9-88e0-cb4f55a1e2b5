import { stubLink } from '../../../../shared/domain/__stubs__/link.stub'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Service, ServiceProps } from '../service'

const baseServiceStubProps: ServiceProps = {
  collectionId: new UniqueEntityID(),
  name: 'Service',
  description: 'A service',
  category: 'Category',
  availability: true,
  link: stubLink('https://acme.tld/services/service'),
}

const stubService = (props?: Partial<ServiceProps>): Service => {
  return new Service({ ...baseServiceStubProps, ...props })
}

export { stubService, baseServiceStubProps }
