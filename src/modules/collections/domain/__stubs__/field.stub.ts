import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  Field,
  FieldCommonProps,
  MultipleValueFieldProps,
  SingleValueFieldProps,
  type FieldProps,
} from '../field'

const baseFieldStubProps: FieldCommonProps = {
  name: 'Field',
  position: 0,
  maxLength: 16,
  visibility: 'PUBLIC',
  mandatory: false,
  fieldGroupId: new UniqueEntityID(),
  preview: true,
}

const baseSingleValueFieldProps: SingleValueFieldProps = {
  ...baseFieldStubProps,
  type: 'TEXT',
}

const baseMultipleValueFieldProps: MultipleValueFieldProps = {
  ...baseFieldStubProps,
  type: 'SELECT',
  options: [
    {
      label: 'Option A',
      value: 'opt-a',
    },
    {
      label: 'Option B',
      value: 'opt-B',
    },
  ],
}

const stubField = (props: FieldProps): Field => {
  return new Field(props)
}

const stubSingleValueField = (
  props?: Partial<SingleValueFieldProps>,
): Field => {
  return new Field({ ...baseSingleValueFieldProps, ...props })
}

const stubMultiValueField = (
  props?: Partial<MultipleValueFieldProps>,
): Field => {
  return new Field({
    ...baseMultipleValueFieldProps,
    ...props,
  })
}

export {
  stubField,
  stubSingleValueField,
  stubMultiValueField,
  baseFieldStubProps,
  baseSingleValueFieldProps,
  baseMultipleValueFieldProps,
}
