import { Collection, type CollectionProps } from '../collection'

const baseCollectionStubProps: CollectionProps = {
  name: 'Collection Name',
  description: 'Collection description',
}

const stubCollection = (props?: Partial<CollectionProps>): Collection => {
  const collection = new Collection({
    ...baseCollectionStubProps,
    ...props,
  })

  return collection
}

export { stubCollection, baseCollectionStubProps }
