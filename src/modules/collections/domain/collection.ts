import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { FieldGroups } from './fieldGroups'
import { Services } from './services'

export type CollectionProps = {
  name: string
  description?: string
  services?: Services
  fieldGroups?: FieldGroups
}

class Collection extends Entity<CollectionProps> {
  constructor(
    props: CollectionProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get name() {
    return this.getProp('name')
  }

  get description() {
    return this.getProp('description')
  }

  get services() {
    return this.getProp('services')
  }

  get fieldGroups() {
    return this.getProp('fieldGroups')
  }
}

export { Collection }
