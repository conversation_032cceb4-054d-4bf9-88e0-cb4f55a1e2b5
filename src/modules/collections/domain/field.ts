import { StrictExtract } from 'ts-essentials'
import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'

type FieldType =
  | 'TEXT'
  | 'TEXTAREA'
  | 'SELECT'
  | 'SELECT-MANY'
  | 'IMAGE-UPLOAD'
  | 'NUMBER'

type FieldOption = {
  label: string
  value: string
}

type FieldVisibility = 'PUBLIC' | 'PRIVATE'

type FieldCommonProps = {
  name: string
  fieldGroupId: UniqueEntityID
  position: number
  maxLength?: number
  mandatory: boolean
  visibility: FieldVisibility
  preview: boolean
}

type MultipleValueFieldProps = FieldCommonProps & {
  type: StrictExtract<FieldType, 'SELECT' | 'SELECT-MANY'>
  options: FieldOption[]
}

type SingleValueFieldProps = FieldCommonProps & {
  type: StrictExtract<
    FieldType,
    'TEXT' | 'TEXTAREA' | 'IMAGE-UPLOAD' | 'NUMBER'
  >
  options?: never
}

type FieldProps = SingleValueFieldProps | MultipleValueFieldProps

class Field extends Entity<FieldProps> {
  constructor(
    props: FieldProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get name() {
    return this.getProp('name')
  }

  get position() {
    return this.getProp('position')
  }

  get maxLength() {
    return this.getProp('maxLength')
  }

  unsetMaxLength(): void {
    this.setProp('maxLength', undefined)
  }

  get mandatory() {
    return this.getProp('mandatory')
  }
  get visibility() {
    return this.getProp('visibility')
  }

  get fieldGroupId() {
    return this.getProp('fieldGroupId')
  }

  get type() {
    return this.getProp('type')
  }

  get options() {
    return this.getProp('options')
  }

  get preview() {
    return this.getProp('preview')
  }
}

export {
  Field,
  type FieldProps,
  type FieldCommonProps,
  type SingleValueFieldProps,
  type MultipleValueFieldProps,
  type FieldType,
  type FieldVisibility,
  type FieldOption,
}
