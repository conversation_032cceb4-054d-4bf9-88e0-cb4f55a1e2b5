import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Fields } from './fields'

type FieldGroupProps = {
  name: string
  position: number
  fields?: Fields
}

class FieldGroup extends Entity<FieldGroupProps> {
  constructor(
    props: FieldGroupProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get name() {
    return this.getProp('name')
  }

  get position() {
    return this.getProp('position')
  }

  get fields() {
    return this.getProp('fields')
  }
}

export { FieldGroup, type FieldGroupProps }
