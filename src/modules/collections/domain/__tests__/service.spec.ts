import { baseServiceStubProps, stubService } from '../__stubs__/service.stub'

describe('Service', () => {
  it('should create a Service entity', () => {
    const service = stubService()

    expect(service.id).toBeDefined()
    expect(service.name).toBe(baseServiceStubProps.name)
    expect(service.availability).toBe(baseServiceStubProps.availability)
    expect(service.category).toBe(baseServiceStubProps.category)
    expect(service.link.equals(baseServiceStubProps.link)).toBe(true)
  })
})
