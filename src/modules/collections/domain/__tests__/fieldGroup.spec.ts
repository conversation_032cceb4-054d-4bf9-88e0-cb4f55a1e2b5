import { stubSingleValueField } from '../__stubs__/field.stub'
import {
  baseFieldGroupStubProps,
  stubFieldGroup,
} from '../__stubs__/fieldGroup.stub'
import { stubFields } from '../__stubs__/fields.stub'

describe('FieldGroup', () => {
  it('should create a FieldGroup entity', () => {
    const field = stubSingleValueField()
    const fieldGroup = stubFieldGroup({
      fields: stubFields([field]),
    })

    expect(fieldGroup.id).toBeDefined()
    expect(fieldGroup.name).toBe(baseFieldGroupStubProps.name)
    expect(fieldGroup.position).toBe(baseFieldGroupStubProps.position)
    expect(fieldGroup.fields?.getItems()).toStrictEqual([field])
  })
})
