import {
  baseMultipleValueFieldProps,
  baseSingleValueFieldProps,
  stubMultiValueField,
  stubSingleValueField,
} from '../__stubs__/field.stub'

describe('Field', () => {
  it('should create a single value Field', () => {
    const field = stubSingleValueField()

    expect(field.id).toBeDefined()
    expect(field.name).toBe(baseSingleValueFieldProps.name)
    expect(field.position).toBe(baseSingleValueFieldProps.position)
    expect(field.maxLength).toBe(baseSingleValueFieldProps.maxLength)
    expect(field.visibility).toBe(baseSingleValueFieldProps.visibility)
    expect(field.mandatory).toBe(baseSingleValueFieldProps.mandatory)
    expect(field.type).toBe(baseSingleValueFieldProps.type)
    expect(field.options).toBeUndefined()
    expect(
      field.fieldGroupId.equals(baseSingleValueFieldProps.fieldGroupId),
    ).toBe(true)
    expect(field.preview).toBe(baseSingleValueFieldProps.preview)
  })

  it('should create a single value Field', () => {
    const field = stubMultiValueField()

    expect(field.id).toBeDefined()
    expect(field.name).toBe(baseMultipleValueFieldProps.name)
    expect(field.position).toBe(baseMultipleValueFieldProps.position)
    expect(field.maxLength).toBe(baseMultipleValueFieldProps.maxLength)
    expect(field.visibility).toBe(baseMultipleValueFieldProps.visibility)
    expect(field.mandatory).toBe(baseMultipleValueFieldProps.mandatory)
    expect(field.type).toBe(baseMultipleValueFieldProps.type)
    expect(field.options).toStrictEqual(baseMultipleValueFieldProps.options)
    expect(field.options).toBeDefined()
    expect(
      field.fieldGroupId.equals(baseMultipleValueFieldProps.fieldGroupId),
    ).toBe(true)
    expect(field.preview).toBe(baseMultipleValueFieldProps.preview)
  })
})
