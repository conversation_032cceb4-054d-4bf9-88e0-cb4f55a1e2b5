import { stubCollection } from '../__stubs__/collection.stub'
import { stubFieldGroup } from '../__stubs__/fieldGroup.stub'
import { stubFieldGroups } from '../__stubs__/fieldGroups.stub'
import { stubService } from '../__stubs__/service.stub'
import { stubServices } from '../__stubs__/services.stub'
import { CollectionProps } from '../collection'

describe('Collection', () => {
  it('should create a Collection entity', () => {
    const collectionProps: CollectionProps = {
      name: 'Collection',
      description: 'Collection description',
      fieldGroups: stubFieldGroups([stubFieldGroup()]),
      services: stubServices([stubService()]),
    }

    const collection = stubCollection(collectionProps)

    expect(collection.id).toBeDefined()
    expect(collection.name).toBe(collectionProps.name)
    expect(collection.description).toBe(collectionProps.description)
    expect(collection.fieldGroups).toStrictEqual(collectionProps.fieldGroups)
    expect(collection.services).toStrictEqual(collectionProps.services)
  })
})
