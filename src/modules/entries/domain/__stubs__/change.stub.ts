import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change, ChangeProps } from '../change'
import { stubSingleValueFieldData } from './fieldData.stub'

const baseChangeStubProps: ChangeProps = {
  entryId: new UniqueEntityID(),
  createdBy: new UniqueEntityID(),
  reviewedBy: new UniqueEntityID(),
  data: [stubSingleValueFieldData()],
  status: 'PENDING',
  reviewerMessage: 'A review message',
}

const stubChange = (props?: Partial<Change>): Change => {
  return new Change({ ...baseChangeStubProps, ...props })
}

export { stubChange, baseChangeStubProps }
