import {
  stubMultiValueField,
  stubSingleValueField,
} from '../../../collections/domain/__stubs__/field.stub'
import { FieldData, FieldDataProps } from '../fieldData'

const baseSingleValueFieldDataStubProps: FieldDataProps = {
  field: stubSingleValueField(),
  value: 'a response',
}

const baseMultipleValueFieldDataStubProps: FieldDataProps = {
  field: stubMultiValueField(),
  value: stubMultiValueField().options?.map((option) => option.value) ?? [],
}

const stubFieldData = (props: FieldDataProps): FieldData => {
  return new FieldData(props)
}

const stubSingleValueFieldData = (
  props?: Partial<FieldDataProps>,
): FieldData => {
  return new FieldData({ ...baseSingleValueFieldDataStubProps, ...props })
}

const stubMultipleValueFieldData = (
  props?: Partial<FieldDataProps>,
): FieldData => {
  return new FieldData({ ...baseMultipleValueFieldDataStubProps, ...props })
}

export {
  stubFieldData,
  stubSingleValueFieldData,
  stubMultipleValueFieldData,
  baseSingleValueFieldDataStubProps,
  baseMultipleValueFieldDataStubProps,
}
