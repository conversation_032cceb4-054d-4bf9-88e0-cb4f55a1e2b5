import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Entry, EntryProps } from '../entry'
import { stubSingleValueFieldData } from './fieldData.stub'

const baseEntryStubProps: EntryProps = {
  collectionId: new UniqueEntityID(),
  visibility: 'PUBLIC',
  data: [stubSingleValueFieldData()],
  changes: [],
  createdBy: new UniqueEntityID(),
}

const stubEntry = (props?: Partial<EntryProps>): Entry => {
  return new Entry({ ...baseEntryStubProps, ...props })
}

export { stubEntry, baseEntryStubProps }
