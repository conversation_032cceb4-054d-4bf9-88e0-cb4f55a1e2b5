import { ValueObject } from '../../../shared/domain/valueObject'
import { InvalidValue } from '../../../shared/errors/domainErrors'
import { Field } from '../../collections/domain/field'

type FieldDataProps = {
  field: Field
  value: string | string[]
}

class FieldData extends ValueObject<FieldDataProps> {
  constructor(props: FieldDataProps) {
    FieldData.validate(props)
    super(props)
  }

  private static validate(props: FieldDataProps) {
    if (props.field.type === 'SELECT-MANY' && typeof props.value === 'string') {
      throw new InvalidValue(FieldData.name, 'value', props.value)
    }
  }
  get field() {
    return this.props.field
  }

  get value() {
    return this.props.value
  }
}

export { FieldData, type FieldDataProps }
