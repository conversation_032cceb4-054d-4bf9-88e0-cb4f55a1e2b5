import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Change } from './change'
import { FieldData } from './fieldData'

type EntryVisibility = 'PUBLIC' | 'PRIVATE'

type EntryProps = {
  collectionId: UniqueEntityID
  visibility: EntryVisibility
  data: FieldData[]
  changes: Change[]
  createdBy: UniqueEntityID
}

class Entry extends Entity<EntryProps> {
  constructor(
    props: EntryProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get collectionId() {
    return this.getProp('collectionId')
  }

  get visibility() {
    return this.getProp('visibility')
  }

  get data() {
    return this.getProp('data')
  }

  get changes() {
    return this.getProp('changes')
  }

  get createdBy() {
    return this.getProp('createdBy')
  }
}

export { Entry, type EntryProps, type EntryVisibility }
