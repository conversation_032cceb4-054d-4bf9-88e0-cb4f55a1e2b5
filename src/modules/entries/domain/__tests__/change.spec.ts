import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { ChangeApprovedEvent } from '../../../activities/domain/events/changes/ChangeApprovedEvent'
import { ChangeCreatedEvent } from '../../../activities/domain/events/changes/ChangeCreatedEvent'
import { ChangeRejectedEvent } from '../../../activities/domain/events/changes/ChangeRejectedEvent'
import { baseChangeStubProps, stubChange } from '../__stubs__/change.stub'
import { Change } from '../change'

describe('Change', () => {
  it('should create a Change entity', () => {
    const change = stubChange()

    expect(change.id).toBeDefined()
    expect(change.createdBy).toStrictEqual(baseChangeStubProps.createdBy)
    expect(change.reviewedBy).toStrictEqual(baseChangeStubProps.reviewedBy)
    expect(change.data).toStrictEqual(baseChangeStubProps.data)
    expect(change.status).toStrictEqual(baseChangeStubProps.status)
    expect(change.reviewerMessage).toStrictEqual(
      baseChangeStubProps.reviewerMessage,
    )
  })

  describe('Domain Events', () => {
    it('should dispatch ChangeCreatedEvent when creating a new change', () => {
      const change = new Change({
        entryId: new UniqueEntityID(),
        createdBy: new UniqueEntityID(),
        data: [],
        status: 'PENDING',
      })

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const changeCreatedEvent = domainEvents[0] as ChangeCreatedEvent
      expect(changeCreatedEvent).toBeInstanceOf(ChangeCreatedEvent)
      expect(changeCreatedEvent.change.id.equals(change.id)).toBe(true)
      expect(changeCreatedEvent.activityType).toBe('CHANGE_CREATE')
      expect(changeCreatedEvent.activityData).toEqual({
        changeId: change.id.value,
        entryId: change.entryId.value,
        createdBy: change.createdBy.value,
        status: change.status,
      })
    })

    it('should not dispatch ChangeCreatedEvent when creating change with existing ID', () => {
      const existingId = new UniqueEntityID()
      const change = new Change(
        {
          entryId: new UniqueEntityID(),
          createdBy: new UniqueEntityID(),
          data: [],
          status: 'PENDING',
        },
        existingId,
      )

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(0)
    })

    it('should dispatch ChangeApprovedEvent when status changes to ACCEPTED', () => {
      const change = stubChange({ status: 'PENDING' })
      change.clearDomainEvents() // Clear the ChangeCreatedEvent

      const reviewedBy = new UniqueEntityID()
      const reviewerMessage = 'Looks good!'

      change.update({
        status: 'ACCEPTED',
        reviewedBy,
        reviewerMessage,
      })

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const changeApprovedEvent = domainEvents[0] as ChangeApprovedEvent
      expect(changeApprovedEvent).toBeInstanceOf(ChangeApprovedEvent)
      expect(changeApprovedEvent.change.id.equals(change.id)).toBe(true)
      expect(changeApprovedEvent.activityType).toBe('CHANGE_APPROVE')
      expect(changeApprovedEvent.activityData).toEqual({
        changeId: change.id.value,
        entryId: change.entryId.value,
        approvedBy: reviewedBy.value,
        createdBy: change.createdBy.value,
        reviewerMessage,
        data: change.data,
      })
    })

    it('should dispatch ChangeRejectedEvent when status changes to REJECTED', () => {
      const change = stubChange({ status: 'PENDING' })
      change.clearDomainEvents() // Clear the ChangeCreatedEvent

      const reviewedBy = new UniqueEntityID()
      const reviewerMessage = 'Needs more work'

      change.update({
        status: 'REJECTED',
        reviewedBy,
        reviewerMessage,
      })

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const changeRejectedEvent = domainEvents[0] as ChangeRejectedEvent
      expect(changeRejectedEvent).toBeInstanceOf(ChangeRejectedEvent)
      expect(changeRejectedEvent.change.id.equals(change.id)).toBe(true)
      expect(changeRejectedEvent.activityType).toBe('CHANGE_REJECT')
      expect(changeRejectedEvent.activityData).toEqual({
        changeId: change.id.value,
        entryId: change.entryId.value,
        rejectedBy: reviewedBy.value,
        createdBy: change.createdBy.value,
        reviewerMessage,
        data: change.data,
      })
    })

    it('should not dispatch events when status does not change', () => {
      const change = stubChange({ status: 'PENDING' })
      change.clearDomainEvents() // Clear the ChangeCreatedEvent

      change.update({
        status: 'PENDING', // Same status
        reviewerMessage: 'Updated message',
      })

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(0)
    })

    it('should not dispatch events when updating non-status fields', () => {
      const change = stubChange({ status: 'PENDING' })
      change.clearDomainEvents() // Clear the ChangeCreatedEvent

      change.update({
        reviewerMessage: 'Updated message',
        data: [],
      })

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(0)
    })

    it('should not dispatch events when changing from ACCEPTED to REJECTED or vice versa', () => {
      const change = stubChange({ status: 'ACCEPTED' })
      change.clearDomainEvents() // Clear any existing events

      change.update({
        status: 'REJECTED',
      })

      const domainEvents = change.getDomainEvents()
      expect(domainEvents).toHaveLength(1)
      expect(domainEvents[0]).toBeInstanceOf(ChangeRejectedEvent)
    })
  })
})
