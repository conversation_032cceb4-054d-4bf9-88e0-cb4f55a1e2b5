import { baseChangeStubProps, stubChange } from '../__stubs__/change.stub'

describe('Change', () => {
  it('should create a Change entity', () => {
    const change = stubChange()

    expect(change.id).toBeDefined()
    expect(change.createdBy).toStrictEqual(baseChangeStubProps.createdBy)
    expect(change.reviewedBy).toStrictEqual(baseChangeStubProps.reviewedBy)
    expect(change.data).toStrictEqual(baseChangeStubProps.data)
    expect(change.status).toStrictEqual(baseChangeStubProps.status)
    expect(change.reviewerMessage).toStrictEqual(
      baseChangeStubProps.reviewerMessage,
    )
  })
})
