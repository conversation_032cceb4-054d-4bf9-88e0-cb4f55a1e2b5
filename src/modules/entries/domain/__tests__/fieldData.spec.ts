import {
  baseMultipleValueFieldDataStubProps,
  baseSingleValueFieldDataStubProps,
  stubMultipleValueFieldData,
  stubSingleValueFieldData,
} from '../__stubs__/fieldData.stub'

describe('FieldData', () => {
  it('should create a FieldData entity of a single value Field', () => {
    const fieldData = stubSingleValueFieldData()

    expect(
      fieldData.field.equals(baseSingleValueFieldDataStubProps.field),
    ).toBe(true)

    expect(fieldData.value).toStrictEqual(
      baseSingleValueFieldDataStubProps.value,
    )
  })

  it('should create a FieldData entity of a multiple value Field', () => {
    const fieldData = stubMultipleValueFieldData()

    expect(
      fieldData.field.equals(baseMultipleValueFieldDataStubProps.field),
    ).toBe(true)

    expect(fieldData.value).toStrictEqual(
      baseMultipleValueFieldDataStubProps.value,
    )
  })
})
