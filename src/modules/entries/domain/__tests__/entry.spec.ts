import { stubChange } from '../__stubs__/change.stub'
import { baseEntryStubProps, stubEntry } from '../__stubs__/entry.stub'

describe('Entry', () => {
  it('should create a Entry entity', () => {
    const entry = stubEntry()

    const change = stubChange()

    entry.changes.push(change)

    expect(entry.id).toBeDefined()
    expect(entry.collectionId).toBe(baseEntryStubProps.collectionId)
    expect(entry.visibility).toBe(baseEntryStubProps.visibility)
    expect(entry.data).toStrictEqual(baseEntryStubProps.data)
    expect(entry.changes).toStrictEqual([change])
    expect(entry.createdBy).toStrictEqual(baseEntryStubProps.createdBy)
  })
})
