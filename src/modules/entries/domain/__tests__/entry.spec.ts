import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { EntryCreatedEvent } from '../../../activities/domain/events/EntryCreatedEvent'
import { EntryUpdatedEvent } from '../../../activities/domain/events/EntryUpdatedEvent'
import { stubChange } from '../__stubs__/change.stub'
import { baseEntryStubProps, stubEntry } from '../__stubs__/entry.stub'
import { Entry } from '../entry'

describe('Entry', () => {
  it('should create a Entry entity', () => {
    const entry = stubEntry()

    const change = stubChange()

    entry.changes.push(change)

    expect(entry.id).toBeDefined()
    expect(entry.collectionId).toBe(baseEntryStubProps.collectionId)
    expect(entry.visibility).toBe(baseEntryStubProps.visibility)
    expect(entry.data).toStrictEqual(baseEntryStubProps.data)
    expect(entry.changes).toStrictEqual([change])
    expect(entry.createdBy).toStrictEqual(baseEntryStubProps.createdBy)
  })

  describe('Domain Events', () => {
    it('should dispatch EntryCreatedEvent when creating a new entry', () => {
      const collectionId = new UniqueEntityID()
      const createdBy = new UniqueEntityID()

      const entry = new Entry({
        collectionId,
        visibility: 'PUBLIC',
        data: [],
        changes: [],
        createdBy,
      })

      const domainEvents = entry.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const entryCreatedEvent = domainEvents[0] as EntryCreatedEvent
      expect(entryCreatedEvent).toBeInstanceOf(EntryCreatedEvent)
      expect(entryCreatedEvent.entry.id.equals(entry.id)).toBe(true)
      expect(entryCreatedEvent.activityType).toBe('ENTRY_CREATE')
      expect(entryCreatedEvent.activityData).toEqual({
        entryId: entry.id.value,
        collectionId: collectionId.value,
        createdBy: createdBy.value,
        visibility: 'PUBLIC',
      })
    })

    it('should not dispatch EntryCreatedEvent when creating entry with existing ID', () => {
      const existingId = new UniqueEntityID()
      const entry = new Entry(
        {
          collectionId: new UniqueEntityID(),
          visibility: 'PRIVATE',
          data: [],
          changes: [],
          createdBy: new UniqueEntityID(),
        },
        existingId,
      )

      const domainEvents = entry.getDomainEvents()
      expect(domainEvents).toHaveLength(0)
    })

    it('should dispatch EntryUpdatedEvent when updateWithUser is called', () => {
      const entry = stubEntry({ visibility: 'PRIVATE' })
      entry.clearDomainEvents() // Clear the EntryCreatedEvent

      const updatedBy = new UniqueEntityID()

      entry.updateWithUser(
        {
          visibility: 'PUBLIC',
        },
        updatedBy,
      )

      const domainEvents = entry.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const entryUpdatedEvent = domainEvents[0] as EntryUpdatedEvent
      expect(entryUpdatedEvent).toBeInstanceOf(EntryUpdatedEvent)
      expect(entryUpdatedEvent.entry.id.equals(entry.id)).toBe(true)
      expect(entryUpdatedEvent.updatedBy.equals(updatedBy)).toBe(true)
      expect(entryUpdatedEvent.activityType).toBe('ENTRY_UPDATE')
      expect(entryUpdatedEvent.activityData).toEqual({
        entryId: entry.id.value,
        collectionId: entry.collectionId.value,
        updatedBy: updatedBy.value,
        visibility: 'PUBLIC', // Updated visibility
      })
    })

    it('should dispatch EntryUpdatedEvent with correct data when updating multiple fields', () => {
      const entry = stubEntry({ visibility: 'PRIVATE' })
      entry.clearDomainEvents() // Clear the EntryCreatedEvent

      const updatedBy = new UniqueEntityID()
      const newData = []

      entry.updateWithUser(
        {
          visibility: 'PUBLIC',
          data: newData,
        },
        updatedBy,
      )

      const domainEvents = entry.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const entryUpdatedEvent = domainEvents[0] as EntryUpdatedEvent
      expect(entryUpdatedEvent).toBeInstanceOf(EntryUpdatedEvent)
      expect(entryUpdatedEvent.entry.visibility).toBe('PUBLIC')
      expect(entryUpdatedEvent.entry.data).toBe(newData)
      expect(entryUpdatedEvent.updatedBy.equals(updatedBy)).toBe(true)
    })

    it('should not dispatch EntryUpdatedEvent when using regular update method', () => {
      const entry = stubEntry({ visibility: 'PRIVATE' })
      entry.clearDomainEvents() // Clear the EntryCreatedEvent

      entry.update({
        visibility: 'PUBLIC',
      })

      const domainEvents = entry.getDomainEvents()
      expect(domainEvents).toHaveLength(0)
    })

    it('should dispatch EntryUpdatedEvent even when no fields are actually changed', () => {
      const entry = stubEntry({ visibility: 'PUBLIC' })
      entry.clearDomainEvents() // Clear the EntryCreatedEvent

      const updatedBy = new UniqueEntityID()

      entry.updateWithUser(
        {
          visibility: 'PUBLIC', // Same visibility
        },
        updatedBy,
      )

      const domainEvents = entry.getDomainEvents()
      expect(domainEvents).toHaveLength(1)

      const entryUpdatedEvent = domainEvents[0] as EntryUpdatedEvent
      expect(entryUpdatedEvent).toBeInstanceOf(EntryUpdatedEvent)
      expect(entryUpdatedEvent.updatedBy.equals(updatedBy)).toBe(true)
    })
  })
})
