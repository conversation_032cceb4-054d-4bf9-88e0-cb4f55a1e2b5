import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import {
  ChangeCreatedEvent,
  ChangeApprovedEvent,
  ChangeRejectedEvent,
} from '../../activities/domain/events'
import { FieldData } from './fieldData'

type ChangeStatus = 'PENDING' | 'ACCEPTED' | 'REJECTED'

type ChangeProps = {
  entryId: UniqueEntityID
  createdBy: UniqueEntityID
  reviewedBy?: UniqueEntityID
  data: FieldData[]
  status: ChangeStatus
  reviewerMessage?: string
}

class Change extends Entity<ChangeProps> {
  constructor(
    props: ChangeProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)

    if (!id) {
      this.addDomainEvent(new ChangeCreatedEvent(this))
    }
  }

  get entryId() {
    return this.getProp('entryId')
  }

  get createdBy() {
    return this.getProp('createdBy')
  }
  get reviewedBy() {
    return this.getProp('reviewedBy')
  }
  get data() {
    return this.getProp('data')
  }

  get status() {
    return this.getProp('status')
  }

  get reviewerMessage() {
    return this.getProp('reviewerMessage')
  }

  override update(props: Partial<ChangeProps>) {
    const previousStatus = this.status

    // Call parent update method
    super.update(props)

    // Dispatch domain events based on status change
    if (props.status && props.status !== previousStatus) {
      if (props.status === 'ACCEPTED') {
        this.addDomainEvent(new ChangeApprovedEvent(this))
      } else if (props.status === 'REJECTED') {
        this.addDomainEvent(new ChangeRejectedEvent(this))
      }
    }
  }
}

export { Change, type ChangeProps, type ChangeStatus }
