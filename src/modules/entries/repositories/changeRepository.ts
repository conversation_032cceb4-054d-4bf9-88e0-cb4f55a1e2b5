import { eq, count } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { editsTable } from '../../../shared/database/schema'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Change } from '../domain/change'
import { ChangeDTO } from '../dto/changeDTO'
import { ChangeMapper } from '../mappers'
import { ChangeModel } from '../models/change.model'
import { GenericChangeRepository } from './interfaces/genericChangeRepository'

class ChangeRepository
  extends AbstractDrizzleRepository<
    typeof editsTable,
    Change,
    ChangeModel,
    ChangeDTO
  >
  implements GenericChangeRepository
{
  constructor(db: DatabaseClient) {
    super('Change', db, ChangeMapper.getInstance(), editsTable, 'editsTable')
  }

  async getAllByEntryId(
    entryId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Change>> {
    const whereConditions = [eq(editsTable.entryId, entryId.value)]

    const queryOptions = this.buildQueryOptions(
      { ...options, includeAll: false },
      whereConditions,
    )

    const changes = await this.db.query.editsTable.findMany(queryOptions)

    const [countResult] = await this.db
      .select({ totalCount: count() })
      .from(this.table)
      .where(whereConditions[0])

    const totalCount = countResult?.totalCount ?? 0

    return {
      items: (<ChangeModel[]>changes).map(this.mapper.toDomain),
      total: totalCount,
    }
  }
}

export { ChangeRepository }
