import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { PaginatedDomain } from '../../../../shared/types/pagination'
import { Change } from '../../domain/change'

interface GenericChangeRepository extends GenericRepository<Change> {
  getAllByEntryId(
    entryId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Change>>
}

export type { GenericChangeRepository }
