import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { PaginatedDomain } from '../../../../shared/types/pagination'
import { Entry } from '../../domain/entry'

interface GenericEntryRepository extends GenericRepository<Entry> {
  getByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Entry>>
}

export type { GenericEntryRepository }
