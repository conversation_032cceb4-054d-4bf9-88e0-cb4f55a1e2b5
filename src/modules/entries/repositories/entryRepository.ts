import { eq, count } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { entriesTable } from '../../../shared/database/schema'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Entry } from '../domain/entry'
import { EntryDTO } from '../dto/entryDTO'
import { EntryMapper } from '../mappers'
import { EntryModel } from '../models/entry.model'
import { GenericEntryRepository } from './interfaces/genericEntryRepository'

class EntryRepository
  extends AbstractDrizzleRepository<
    typeof entriesTable,
    Entry,
    EntryModel,
    EntryDTO
  >
  implements GenericEntryRepository
{
  constructor(db: DatabaseClient) {
    super('Entry', db, EntryMapper.getInstance(), entriesTable, 'entriesTable')
  }

  async getByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Entry>> {
    const whereConditions = [eq(entriesTable.collectionId, collectionId.value)]

    const queryOptions = this.buildQueryOptions(
      { ...options, includeAll: false },
      whereConditions,
    )

    const entries = await this.db.query.entriesTable.findMany(queryOptions)

    const [countResult] = await this.db
      .select({ totalCount: count() })
      .from(this.table)
      .where(whereConditions[0])

    const totalCount = countResult?.totalCount ?? 0

    return {
      items: (<EntryModel[]>entries).map(this.mapper.toDomain),
      total: totalCount,
    }
  }
}

export { EntryRepository }
