import { Route } from '../../../shared/infra/http/routes/route'
import { approveChangeController } from '../useCases/change/approveChange'
import { createChangeController } from '../useCases/change/createChange'
import { deleteChangeController } from '../useCases/change/deleteChange'
import { getChangeController } from '../useCases/change/getChange'
import { getChangesController } from '../useCases/change/getChanges'
import { rejectChangeController } from '../useCases/change/rejectChange'
import { deleteEntryController } from '../useCases/entry/deleteEntry'
import { getEntryController } from '../useCases/entry/getEntry'
import { updateEntryController } from '../useCases/entry/updateEntry'

const entriesRoute = new Route(
  'entries',
  getEntryController,
  updateEntryController,
  deleteEntryController,
  getChangesController,
  createChangeController,
)

const changesRoute = new Route(
  'changes',
  getChangeController,
  approveChangeController,
  rejectChangeController,
  deleteChangeController,
)

export { entriesRoute, changesRoute }
