import { TEntityDTO } from '../../../shared/dto/entityDTO'
import { IdDTO } from '../../../shared/dto/idDTO'
import { ChangeStatus } from '../domain/change'
import { FieldDataDTO } from './fieldDataDTO'

interface ChangeDTO extends TEntityDTO {
  entryId: IdDTO
  createdBy: IdDTO
  reviewedBy?: IdDTO
  data: FieldDataDTO[]
  status: ChangeStatus
  reviewerMessage?: string
}

export type { ChangeDTO }
