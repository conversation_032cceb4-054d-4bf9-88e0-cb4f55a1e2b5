import { TEntityDTO } from '../../../shared/dto/entityDTO'
import { IdDTO } from '../../../shared/dto/idDTO'
import { EntryVisibility } from '../domain/entry'
import { ChangeDTO } from './changeDTO'
import { FieldDataDTO } from './fieldDataDTO'

interface EntryDTO extends TEntityDTO {
  collectionId: IdDTO
  visibility: EntryVisibility
  data: FieldDataDTO[]
  changes: ChangeDTO[]
  createdBy: IdDTO
}

export type { EntryDTO }
