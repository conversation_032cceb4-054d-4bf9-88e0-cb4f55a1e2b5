import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Change } from '../domain/change'
import { ChangeDTO } from '../dto/changeDTO'
import { ChangeModel } from '../models/change.model'
import { FieldDataMapper } from './fieldDataMapper'

class ChangeMapper implements Mapper<Change, ChangeModel, ChangeDTO> {
  private static _instance: ChangeMapper

  toDomain(model: ChangeModel): Change {
    return new Change(
      {
        entryId: new UniqueEntityID(model.entryId),
        status: model.status,
        createdBy: new UniqueEntityID(model.editorId),
        reviewedBy: model.reviewerId
          ? new UniqueEntityID(model.reviewerId)
          : undefined,
        reviewerMessage: model.reviewerMessage ?? undefined,
        data: model.data.map(FieldDataMapper.getInstance().toDomain),
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt ?? undefined,
      model.deleted,
    )
  }

  toModel(domain: Change): ChangeModel {
    return {
      id: domain.id.value,
      entryId: domain.entryId.value,
      editorId: domain.createdBy.value,
      status: domain.status,
      reviewerId: domain.reviewedBy?.value ?? null,
      reviewerMessage: domain.reviewerMessage ?? null,
      data: domain.data.map(FieldDataMapper.getInstance().toModel),
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
      deleted: domain.deleted,
    }
  }

  toDTO(domain: Change): ChangeDTO {
    return {
      id: domain.id.value,
      entryId: domain.entryId.value,
      createdBy: domain.createdBy.value,
      reviewedBy: domain.reviewedBy?.value,
      status: domain.status,
      reviewerMessage: domain.reviewerMessage,
      data: domain.data.map(FieldDataMapper.getInstance().toDTO),
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
      deleted: domain.deleted,
    }
  }

  toPageDTO(domain: PaginatedDomain<Change>): PaginatedResponseDTO<ChangeDTO> {
    return {
      items: domain.items.map(this.toDTO),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!ChangeMapper._instance) {
      ChangeMapper._instance = new ChangeMapper()
    }
    return ChangeMapper._instance
  }
}

export { ChangeMapper }
