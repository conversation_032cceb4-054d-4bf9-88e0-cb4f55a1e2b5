import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Entry } from '../domain/entry'
import { EntryDTO } from '../dto/entryDTO'
import { EntryModel } from '../models/entry.model'
import { ChangeMapper } from './changeMapper'
import { FieldDataMapper } from './fieldDataMapper'

class EntryMapper implements Mapper<Entry, EntryModel, EntryDTO> {
  private static _instance: EntryMapper

  toDomain(model: EntryModel): Entry {
    return new Entry(
      {
        collectionId: new UniqueEntityID(model.collectionId),
        createdBy: new UniqueEntityID(model.creatorId),
        visibility: model.visibility,
        changes: [], // TODO
        data: model.data.map(FieldDataMapper.getInstance().toDomain),
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt ?? undefined,
      model.deleted,
    )
  }

  toModel(domain: Entry): EntryModel {
    return {
      id: domain.id.value,
      collectionId: domain.collectionId.value,
      visibility: domain.visibility,
      creatorId: domain.createdBy.value,
      data: domain.data.map(FieldDataMapper.getInstance().toModel),
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
      deleted: domain.deleted,
    }
  }

  toDTO(domain: Entry, preview = false): EntryDTO {
    // TODO: remove this filter once get fields logic is implemented (issue#107)
    const dataFields = preview
      ? domain.data.filter((d) => d.field.preview)
      : domain.data
    return {
      id: domain.id.value,
      collectionId: domain.collectionId.value,
      createdBy: domain.createdBy.value,
      visibility: domain.visibility,
      data: dataFields.map(FieldDataMapper.getInstance().toDTO),
      changes: domain.changes.map((changeEntry) =>
        ChangeMapper.getInstance().toDTO(changeEntry),
      ),
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
      deleted: domain.deleted,
    }
  }

  toPageDTO(
    domain: PaginatedDomain<Entry>,
    preview = false,
  ): PaginatedResponseDTO<EntryDTO> {
    return {
      items: domain.items.map((entry) => this.toDTO(entry, preview)),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!EntryMapper._instance) {
      EntryMapper._instance = new EntryMapper()
    }
    return EntryMapper._instance
  }
}

export { EntryMapper }
