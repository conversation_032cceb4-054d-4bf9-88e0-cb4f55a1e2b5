import { FieldMapper } from '../../collections/mappers'
import { FieldData } from '../domain/fieldData'
import { FieldDataDTO } from '../dto/fieldDataDTO'
import { FieldDataModel } from '../models/fieldData.model'

class FieldDataMapper {
  private static _instance: FieldDataMapper

  toDomain(model: FieldDataModel): FieldData {
    return new FieldData({
      field: FieldMapper.getInstance().toDomain(model.field),
      value: model.value,
    })
  }

  toModel(domain: FieldData): FieldDataModel {
    return {
      field: FieldMapper.getInstance().toModel(domain.field),
      value: domain.value,
    }
  }

  toDTO(domain: FieldData): FieldDataDTO {
    return {
      fieldId: domain.field.id.value,
      value: domain.value,
    }
  }

  static getInstance() {
    if (!FieldDataMapper._instance) {
      FieldDataMapper._instance = new FieldDataMapper()
    }
    return FieldDataMapper._instance
  }
}

export { FieldDataMapper }
