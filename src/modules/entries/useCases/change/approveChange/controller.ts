import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ChangeDTO } from '../../../dto/changeDTO'
import { ApproveChange } from './approveChange'
import { ApproveChangeDTO } from './approveChange.dto'

const schema = object({
  params: object({
    changeId: string().uuid().required(),
  }).required(),
  body: object({
    reviewerMessage: string(),
  }).optional(),
})

class ApproveChangeController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  ApproveChangeDTO,
  ChangeDTO
> {
  public constructor(useCase: ApproveChange) {
    super(
      'patch',
      ':changeId/approve',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): ApproveChangeDTO {
    return {
      id: request.params.changeId,
      data: {
        status: 'ACCEPTED',
        reviewerMessage: request.body?.reviewerMessage,
      },
    }
  }
}

export { ApproveChangeController }
