import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { InvalidState } from '../../../../../shared/errors/domainErrors'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericFieldRepository } from '../../../../collections/repositories/interfaces/genericFieldRepository'
import { FieldData } from '../../../domain/fieldData'
import { ChangeDTO } from '../../../dto/changeDTO'
import { ChangeMapper } from '../../../mappers'
import { GenericChangeRepository } from '../../../repositories/interfaces/genericChangeRepository'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { ApproveChangeDTO } from './approveChange.dto'

class ApproveChange extends UseCase<ApproveChangeDTO, ChangeDTO> {
  constructor(
    private readonly _changeRepository: GenericChangeRepository,
    private readonly _fieldRepository: GenericFieldRepository,
    private readonly _entryRepository: GenericEntryRepository,
  ) {
    super(ApproveChange.name)
  }

  private async generateData(data: ApproveChangeDTO['data']['data']) {
    const parsedData = data?.map(
      async (dataEntry) =>
        new FieldData({
          field: await this._fieldRepository.getById(
            new UniqueEntityID(dataEntry.fieldId),
          ),
          value: dataEntry.value,
        }),
    )

    return parsedData ? await Promise.all(parsedData) : undefined
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId))
      return

    throw new NotAllowed(ApproveChange.name)
  }

  async execute(request: Readonly<ApproveChangeDTO>): Promise<ChangeDTO> {
    const {
      id,
      data: { reviewerMessage, data, status },
    } = request
    const changeId = new UniqueEntityID(id)
    const change = await this._changeRepository.getById(changeId)
    const entry = await this._entryRepository.getById(change.entryId)

    this.checkPermissions(entry.collectionId)

    if (change.status !== 'PENDING') {
      throw new InvalidState('Change already reviewed', 'Change')
    }

    const currentUser = AuthContext.getUser()

    change.update({
      reviewedBy: currentUser.id,
      reviewerMessage: reviewerMessage,
      status: status,
      data: await this.generateData(data),
    })

    await this._changeRepository.save(change)

    // TODO: Add logic to update the entry with the new data

    return ChangeMapper.getInstance().toDTO(change)
  }
}

export { ApproveChange }
