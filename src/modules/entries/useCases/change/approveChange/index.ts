import { fieldRepository } from '../../../../collections/repositories'
import { changeRepository, entryRepository } from '../../../repositories'
import { ApproveChange } from './approveChange'
import { ApproveChangeController } from './controller'

const useCase = new ApproveChange(
  changeRepository,
  fieldRepository,
  entryRepository,
)
const approveChangeController = new ApproveChangeController(useCase)

export { approveChangeController }
