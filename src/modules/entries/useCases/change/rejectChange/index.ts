import { fieldRepository } from '../../../../collections/repositories'
import { changeRepository, entryRepository } from '../../../repositories'
import { RejectChangeController } from './controller'
import { RejectChange } from './rejectChange'

const useCase = new RejectChange(
  changeRepository,
  fieldRepository,
  entryRepository,
)
const rejectChangeController = new RejectChangeController(useCase)

export { rejectChangeController }
