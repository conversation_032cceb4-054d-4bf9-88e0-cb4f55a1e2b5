import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ChangeDTO } from '../../../dto/changeDTO'
import { RejectChange } from './rejectChange'
import { RejectChangeDTO } from './rejectChange.dto'

const schema = object({
  params: object({
    changeId: string().uuid().required(),
  }).required(),
  body: object({
    reviewerMessage: string(),
  }).optional(),
})

class RejectChangeController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  RejectChangeDTO,
  ChangeDTO
> {
  public constructor(useCase: RejectChange) {
    super(
      'patch',
      ':changeId/reject',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): RejectChangeDTO {
    return {
      id: request.params.changeId,
      data: {
        status: 'REJECTED',
        reviewerMessage: request.body?.reviewerMessage,
      },
    }
  }
}

export { RejectChangeController }
