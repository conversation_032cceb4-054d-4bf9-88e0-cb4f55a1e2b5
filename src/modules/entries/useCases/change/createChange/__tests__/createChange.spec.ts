import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { changeRepository, entryRepository } from '../../../../repositories'
import { CreateChange } from '../createChange'
import { CreateChangeDTO } from '../createChange.dto'

let useCase: CreateChange
let user: User
let entryId: string
let fieldId: string

describe('CreateChange', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const entry = stubEntry({ collectionId: collection.id, createdBy: user.id })
    entryId = entry.id.value
    await entryRepository.save(entry)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    fieldId = field.id.value
    await fieldGroupRepository.save(fieldGroup)

    useCase = new CreateChange(
      changeRepository,
      fieldRepository,
      entryRepository,
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to create a new change', async () => {
    const request: CreateChangeDTO = {
      entryId,
      data: [{ fieldId, value: 'value' }],
    }
    const result = await execute(useCase, request, user)

    expect(result.entryId).toEqual(request.entryId)
    expect(result.createdBy).toEqual(user.id.value)
    expect(result.status).toEqual('PENDING')
    expect(result.data[0]?.fieldId).toEqual(request.data[0]?.fieldId)
    expect(result.data[0]?.value).toEqual(request.data[0]?.value)
  })
})
