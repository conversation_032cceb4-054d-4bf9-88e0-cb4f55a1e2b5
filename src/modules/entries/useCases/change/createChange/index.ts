import { fieldRepository } from '../../../../collections/repositories'
import { changeRepository, entryRepository } from '../../../repositories'
import { CreateChangeController } from './controller'
import { CreateChange } from './createChange'

const useCase = new CreateChange(
  changeRepository,
  fieldRepository,
  entryRepository,
)
const createChangeController = new CreateChangeController(useCase)

export { createChangeController }
