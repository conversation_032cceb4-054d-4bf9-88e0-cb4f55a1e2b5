import { constants } from 'http2'
import { array, InferType, mixed, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ChangeDTO } from '../../../dto/changeDTO'
import { CreateChange } from './createChange'
import { CreateChangeDTO } from './createChange.dto'

const schema = object({
  params: object({
    entryId: string().uuid().required(),
  }).required(),
  body: object({
    data: array(
      object({
        fieldId: string().uuid().required(),
        value: mixed<string | string[]>().required(),
      }).required(),
    ).required(),
  }).required(),
})

class CreateChangeController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  CreateChangeDTO,
  ChangeDTO
> {
  public constructor(useCase: CreateChange) {
    super(
      'post',
      ':entryId/changes',
      constants.HTTP_STATUS_CREATED,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): CreateChangeDTO {
    return {
      entryId: request.params.entryId,
      data: request.body.data,
    }
  }
}

export { CreateChangeController }
