import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericFieldRepository } from '../../../../collections/repositories/interfaces/genericFieldRepository'
import { Change } from '../../../domain/change'
import { FieldData } from '../../../domain/fieldData'
import { ChangeDTO } from '../../../dto/changeDTO'
import { FieldDataDTO } from '../../../dto/fieldDataDTO'
import { ChangeMapper } from '../../../mappers'
import { GenericChangeRepository } from '../../../repositories/interfaces/genericChangeRepository'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { CreateChangeDTO } from './createChange.dto'

class CreateChange extends UseCase<CreateChangeDTO, ChangeDTO> {
  constructor(
    private readonly _changeRepository: GenericChangeRepository,
    private readonly _fieldRepository: GenericFieldRepository,
    private readonly _entryRepository: GenericEntryRepository,
  ) {
    super(CreateChange.name)
  }

  private async processFieldData(data: FieldDataDTO[]): Promise<FieldData[]> {
    return Promise.all(
      data.map(async (dataEntry) => {
        const relatedField = await this._fieldRepository.getById(
          new UniqueEntityID(dataEntry.fieldId),
        )

        return new FieldData({
          field: relatedField,
          value: dataEntry.value,
        })
      }),
    )
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const isCollectionManager = UserUtils.hasRole(
      currentUser,
      'COLLECTION_MANAGER',
      collectionId,
    )

    if (isCollectionManager) return

    const hasRole = [
      UserUtils.hasRole(currentUser, 'EDITOR', collectionId),
      UserUtils.hasRole(currentUser, 'CONTRIBUTOR', collectionId),
    ]

    const hasPermission = hasRole.some((role) => role)

    if (hasPermission) return

    throw new NotAllowed(CreateChange.name)
  }

  async execute(request: Readonly<CreateChangeDTO>): Promise<ChangeDTO> {
    const fieldData = await this.processFieldData(request.data)
    const entry = await this._entryRepository.getById(
      new UniqueEntityID(request.entryId),
    )

    this.checkPermissions(entry.collectionId)

    const change = new Change({
      createdBy: AuthContext.getUser().id,
      status: 'PENDING',
      data: fieldData,
      entryId: entry.id,
    })

    await this._changeRepository.save(change)

    return ChangeMapper.getInstance().toDTO(change)
  }
}

export { CreateChange }
