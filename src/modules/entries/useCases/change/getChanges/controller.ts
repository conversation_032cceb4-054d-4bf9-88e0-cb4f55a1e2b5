import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { QueryDTO } from '../../../../../shared/dto/queryDTO'
import { Controller } from '../../../../../shared/infra/controller'
import { QuerySchema } from '../../../../../shared/types/query'
import { ChangeDTO } from '../../../dto/changeDTO'
import { GetChanges } from './getChanges'
import { GetChangesDTO } from './getChanges.dto'

const schema = object({
  params: object({
    entryId: string().uuid().required(),
  }).required(),
  query: QuerySchema,
})

class GetChangesController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryDTO,
  GetChangesDTO,
  PaginatedResponseDTO<ChangeDTO>
> {
  public constructor(useCase: GetChanges) {
    super('get', ':entryId/changes', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetChangesDTO {
    return { entryId: request.params.entryId, ...request.query }
  }
}

export { GetChangesController }
