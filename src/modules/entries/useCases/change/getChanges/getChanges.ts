import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { ChangeDTO } from '../../../dto/changeDTO'
import { ChangeMapper } from '../../../mappers'
import { GenericChangeRepository } from '../../../repositories/interfaces/genericChangeRepository'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { GetChangesDTO } from './getChanges.dto'

class GetChanges extends UseCase<
  GetChangesDTO,
  PaginatedResponseDTO<ChangeDTO>
> {
  constructor(
    private readonly _changeRepository: GenericChangeRepository,
    private readonly _entryRepository: GenericEntryRepository,
  ) {
    super(GetChanges.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetChanges.name)
  }

  async execute(
    request: Readonly<GetChangesDTO>,
  ): Promise<PaginatedResponseDTO<ChangeDTO>> {
    const entryId = new UniqueEntityID(request.entryId)

    const entry = await this._entryRepository.getById(entryId)

    this.checkPermissions(entry.collectionId)

    const result = await this._changeRepository.getAllByEntryId(
      entryId,
      request,
    )

    return ChangeMapper.getInstance().toPageDTO(result)
  }
}

export { GetChanges }
