import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { ChangeDTO } from '../../../dto/changeDTO'
import { ChangeMapper } from '../../../mappers'
import { GenericChangeRepository } from '../../../repositories/interfaces/genericChangeRepository'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { GetChangeDTO } from './getChange.dto'

class GetChange extends UseCase<GetChangeDTO, ChangeDTO> {
  constructor(
    private readonly _changeRepository: GenericChangeRepository,
    private readonly _entryRepository: GenericEntryRepository,
  ) {
    super(GetChange.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetChange.name)
  }

  async execute(request: Readonly<GetChangeDTO>): Promise<ChangeDTO> {
    const { id } = request

    const change = await this._changeRepository.getById(new UniqueEntityID(id))
    const entry = await this._entryRepository.getById(change.entryId)

    this.checkPermissions(entry.collectionId)

    return ChangeMapper.getInstance().toDTO(change)
  }
}

export { GetChange }
