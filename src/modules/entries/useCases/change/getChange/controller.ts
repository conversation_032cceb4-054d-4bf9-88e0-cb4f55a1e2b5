import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { ChangeDTO } from '../../../dto/changeDTO'
import { GetChange } from './getChange'
import { GetChangeDTO } from './getChange.dto'

const schema = object({
  params: object({
    changeId: string().uuid().required(),
  }).required(),
})

class GetChangeController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetChangeDTO,
  ChangeDTO
> {
  public constructor(useCase: GetChange) {
    super('get', ':changeId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetChangeDTO {
    return { id: request.params.changeId }
  }
}

export { GetChangeController }
