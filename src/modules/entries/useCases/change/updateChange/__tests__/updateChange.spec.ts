import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubChange } from '../../../../domain/__stubs__/change.stub'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { stubFieldData } from '../../../../domain/__stubs__/fieldData.stub'
import { Change } from '../../../../domain/change'
import { changeRepository, entryRepository } from '../../../../repositories'
import { UpdateChange } from '../updateChange'
import { UpdateChangeDTO } from '../updateChange.dto'

let useCase: UpdateChange
let user: User
let change: Change
let fieldId: string

describe('UpdateChange', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const entry = stubEntry({ collectionId: collection.id, createdBy: user.id })
    await entryRepository.save(entry)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    fieldId = field.id.value
    await fieldGroupRepository.save(fieldGroup)

    change = stubChange({
      createdBy: user.id,
      entryId: entry.id,
      data: [stubFieldData({ field, value: 'value' })],
      reviewedBy: user.id,
    })
    await changeRepository.save(change)

    useCase = new UpdateChange(
      changeRepository,
      fieldRepository,
      entryRepository,
    )
  })

  it('Should be able to update a change', async () => {
    const request: UpdateChangeDTO = {
      id: change.id.value,
      data: {
        data: [{ fieldId, value: 'another value' }],
      },
    }
    const result = await execute(useCase, request, user)

    expect(result.data[0]?.fieldId).toEqual(request.data.data![0]?.fieldId)
    expect(result.data[0]?.value).toEqual(request.data.data![0]?.value)
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
