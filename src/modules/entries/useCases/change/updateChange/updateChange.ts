import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericFieldRepository } from '../../../../collections/repositories/interfaces/genericFieldRepository'
import { Change } from '../../../domain/change'
import { FieldData } from '../../../domain/fieldData'
import { ChangeDTO } from '../../../dto/changeDTO'
import { ChangeMapper } from '../../../mappers'
import { GenericChangeRepository } from '../../../repositories/interfaces/genericChangeRepository'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { UpdateChangeDTO } from './updateChange.dto'

class UpdateChange extends UseCase<UpdateChangeDTO, ChangeDTO> {
  constructor(
    private readonly _changeRepository: GenericChangeRepository,
    private readonly _fieldRepository: GenericFieldRepository,
    private readonly _entryRepository: GenericEntryRepository,
  ) {
    super(UpdateChange.name)
  }

  private async generateData(data: UpdateChangeDTO['data']['data']) {
    const parsedData = data?.map(
      async (dataEntry) =>
        new FieldData({
          field: await this._fieldRepository.getById(
            new UniqueEntityID(dataEntry.fieldId),
          ),
          value: dataEntry.value,
        }),
    )

    return parsedData ? await Promise.all(parsedData) : undefined
  }

  private async checkPermissions(change: Change) {
    const currentUser = AuthContext.getUser()

    const { collectionId } = await this._entryRepository.getById(change.entryId)
    const isCollectionManager = UserUtils.hasRole(
      currentUser,
      'COLLECTION_MANAGER',
      collectionId,
    )

    if (isCollectionManager) return

    const isPending = change.status === 'PENDING'
    const isUserRelated = change.createdBy.equals(currentUser.id)

    const hasRoleList = [
      UserUtils.hasRole(currentUser, 'EDITOR', collectionId),
      UserUtils.hasRole(currentUser, 'CONTRIBUTOR', collectionId),
    ]

    const hasPermission = hasRoleList.some((hasRole) => hasRole === true)

    if (hasPermission && isUserRelated && isPending) return

    throw new NotAllowed(UpdateChange.name)
  }

  async execute(request: Readonly<UpdateChangeDTO>): Promise<ChangeDTO> {
    const { id, data: updateData } = request

    const change = await this._changeRepository.getById(new UniqueEntityID(id))

    await this.checkPermissions(change)

    change.update({
      data: await this.generateData(updateData.data),
    })

    await this._changeRepository.save(change)

    return ChangeMapper.getInstance().toDTO(change)
  }
}

export { UpdateChange }
