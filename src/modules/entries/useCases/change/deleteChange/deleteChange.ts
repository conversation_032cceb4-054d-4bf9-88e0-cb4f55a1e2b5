import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { Change } from '../../../domain/change'
import { GenericChangeRepository } from '../../../repositories/interfaces/genericChangeRepository'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { DeleteChangeDTO } from './deleteChange.dto'

class DeleteChange extends UseCase<DeleteChangeDTO, void> {
  constructor(
    private readonly _changeRepository: GenericChangeRepository,
    private readonly _entryRepository: GenericEntryRepository,
  ) {
    super(DeleteChange.name)
  }

  private async checkPermissions(change: Change) {
    const currentUser = AuthContext.getUser()

    const { collectionId } = await this._entryRepository.getById(change.entryId)

    const isPending = change.status === 'PENDING'
    const isUserRelated = change.createdBy.equals(currentUser.id)

    const isCollectionManager = UserUtils.hasRole(
      currentUser,
      'COLLECTION_MANAGER',
      collectionId,
    )

    if (isCollectionManager) return

    const hasRoles = [
      UserUtils.hasRole(currentUser, 'EDITOR', collectionId),
      UserUtils.hasRole(currentUser, 'CONTRIBUTOR', collectionId),
    ]

    const hasPermission = hasRoles.some((role) => role)

    if (hasPermission && isUserRelated && isPending) return

    throw new NotAllowed(DeleteChange.name)
  }

  async execute(request: Readonly<DeleteChangeDTO>): Promise<void> {
    const { id } = request
    const change = await this._changeRepository.getById(new UniqueEntityID(id))

    await this.checkPermissions(change)

    await this._changeRepository.delete(change.id)
  }
}

export { DeleteChange }
