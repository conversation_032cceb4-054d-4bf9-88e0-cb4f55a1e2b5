import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { DeleteChange } from './deleteChange'
import { DeleteChangeDTO } from './deleteChange.dto'

const schema = object({
  params: object({
    changeId: string().uuid().required(),
  }).required(),
})

class DeleteChangeController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteChangeDTO
> {
  public constructor(useCase: DeleteChange) {
    super(
      'delete',
      ':changeId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): DeleteChangeDTO {
    return { id: request.params.changeId }
  }
}

export { DeleteChangeController }
