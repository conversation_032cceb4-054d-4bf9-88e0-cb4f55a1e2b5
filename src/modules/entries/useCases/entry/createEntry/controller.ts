import { constants } from 'http2'
import { array, InferType, mixed, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { EntryVisibility } from '../../../domain/entry'
import { EntryDTO } from '../../../dto/entryDTO'
import { CreateEntry } from './createEntry'
import { CreateEntryDTO } from './createEntry.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  body: object({
    data: array(
      object({
        fieldId: string().uuid().required(),
        value: mixed<string | string[]>().required(),
      }).required(),
    ).required(),
    visibility: mixed<EntryVisibility>()
      .oneOf(['PUBLIC', 'PRIVATE'])
      .required(),
  }).required(),
})

class CreateEntryController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  CreateEntryDTO,
  EntryDTO
> {
  public constructor(useCase: CreateEntry) {
    super(
      'post',
      ':collectionId/entries',
      constants.HTTP_STATUS_CREATED,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): CreateEntryDTO {
    return {
      collectionId: request.params.collectionId,
      ...request.body,
    }
  }
}

export { CreateEntryController }
