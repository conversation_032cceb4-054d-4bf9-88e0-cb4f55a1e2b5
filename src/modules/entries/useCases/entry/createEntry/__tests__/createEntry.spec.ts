import { domainEvents } from '../../../../../../shared/domain/events'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { EntryCreatedEvent } from '../../../../../activities/domain/events/entries/EntryCreatedEvent'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import { Field } from '../../../../../collections/domain/field'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { entryRepository } from '../../../../repositories'
import { CreateEntry } from '../createEntry'
import { CreateEntryDTO } from '../createEntry.dto'

let useCase: CreateEntry
let user: User
let collectionId: string
let field: Field
let domainEventsSpy: jest.SpyInstance
let markEntitySpy: jest.SpyInstance

describe('CreateEntry', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    useCase = new CreateEntry(entryRepository, fieldRepository)
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    // Re-save the collection, field group, and user for each test
    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    // Update user assignment with new collection ID
    user.userAssignments = [
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    ]
    await userRepository.save(user)

    // Clear any existing domain events and set up spies
    jest.restoreAllMocks()
    domainEvents.clearMarkedEntities()
    domainEventsSpy = jest.spyOn(domainEvents, 'dispatchEventsForEntity')
    markEntitySpy = jest.spyOn(domainEvents, 'markEntityForDispatch')
  })

  afterEach(() => {
    // Clean up spies
    if (domainEventsSpy) {
      domainEventsSpy.mockRestore()
    }
    if (markEntitySpy) {
      markEntitySpy.mockRestore()
    }
  })

  it('Should be able to create an entry', async () => {
    const request: CreateEntryDTO = {
      collectionId,
      data: [{ fieldId: field.id.value, value: 'value' }],
      visibility: 'PUBLIC',
    }
    const result = await execute(useCase, request, user)

    expect(result.collectionId).toEqual(request.collectionId)
    expect(result.visibility).toEqual(request.visibility)
    expect(result.data[0]?.fieldId).toEqual(request.data[0]?.fieldId)
    expect(result.data[0]?.value).toEqual(request.data[0]?.value)
  })

  it('should dispatch EntryCreatedEvent when creating an entry', async () => {
    const request: CreateEntryDTO = {
      collectionId,
      data: [{ fieldId: field.id.value, value: 'test value' }],
      visibility: 'PRIVATE',
    }

    const result = await execute(useCase, request, user)

    // Verify that domain events were dispatched
    expect(domainEventsSpy).toHaveBeenCalled()
    expect(markEntitySpy).toHaveBeenCalledTimes(1)

    // Verify EntryCreatedEvent was marked for dispatch
    const markedEvent = markEntitySpy.mock.calls[0][0] as EntryCreatedEvent
    expect(markedEvent).toBeInstanceOf(EntryCreatedEvent)
    expect(markedEvent.entry.id.value).toBe(result.id)
    expect(markedEvent.entry.collectionId.value).toBe(collectionId)
    expect(markedEvent.entry.createdBy.equals(user.id)).toBe(true)
    expect(markedEvent.entry.visibility).toBe('PRIVATE')
    expect(markedEvent.activityType).toBe('ENTRY_CREATE')
    expect(markedEvent.activityData).toEqual({
      entryId: result.id,
      collectionId: collectionId,
      createdBy: user.id.value,
      visibility: 'PRIVATE',
    })
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
