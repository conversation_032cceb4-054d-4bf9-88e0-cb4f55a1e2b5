import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import { Field } from '../../../../../collections/domain/field'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { entryRepository } from '../../../../repositories'
import { CreateEntry } from '../createEntry'
import { CreateEntryDTO } from '../createEntry.dto'

let useCase: CreateEntry
let user: User
let collectionId: string
let field: Field

describe('CreateEntry', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    useCase = new CreateEntry(entryRepository, fieldRepository)
  })

  it('Should be able to create an entry', async () => {
    const request: CreateEntryDTO = {
      collectionId,
      data: [{ fieldId: field.id.value, value: 'value' }],
      visibility: 'PUBLIC',
    }
    const result = await execute(useCase, request, user)

    expect(result.collectionId).toEqual(request.collectionId)
    expect(result.visibility).toEqual(request.visibility)
    expect(result.data[0]?.fieldId).toEqual(request.data[0]?.fieldId)
    expect(result.data[0]?.value).toEqual(request.data[0]?.value)
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
