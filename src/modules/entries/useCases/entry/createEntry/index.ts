import { fieldRepository } from '../../../../collections/repositories'
import { userRepository } from '../../../../users/repositories'
import { entryRepository } from '../../../repositories'
import { CreateEntryController } from './controller'
import { CreateEntry } from './createEntry'

const useCase = new CreateEntry(
  entryRepository,
  fieldRepository,
  userRepository,
)
const createEntryController = new CreateEntryController(useCase)

export { createEntryController }
