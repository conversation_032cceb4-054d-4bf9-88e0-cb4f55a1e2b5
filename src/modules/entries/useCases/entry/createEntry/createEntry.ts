import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'

import { GenericFieldRepository } from '../../../../collections/repositories/interfaces/genericFieldRepository'
import { User } from '../../../../users/domain/user'

import { Entry } from '../../../domain/entry'
import { FieldData } from '../../../domain/fieldData'
import { EntryDTO } from '../../../dto/entryDTO'
import { FieldDataDTO } from '../../../dto/fieldDataDTO'
import { EntryMapper } from '../../../mappers'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { CreateEntryDTO } from './createEntry.dto'

class CreateEntry extends UseCase<CreateEntryDTO, EntryDTO> {
  constructor(
    private readonly _entryRepository: GenericEntryRepository,
    private readonly _fieldRepository: GenericFieldRepository,
  ) {
    super(CreateEntry.name)
  }

  private processData(fieldData: FieldDataDTO[]) {
    return Promise.all(
      fieldData.map(
        async (dataEntry) =>
          new FieldData({
            field: await this._fieldRepository.getById(
              new UniqueEntityID(dataEntry.fieldId),
            ),
            value: dataEntry.value,
          }),
      ),
    )
  }

  private checkPermissions(collectionId: UniqueEntityID, currentUser: User) {
    const hasRoleList = [
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId),
      UserUtils.hasRole(currentUser, 'EDITOR', collectionId),
      UserUtils.hasRole(currentUser, 'CONTRIBUTOR', collectionId),
    ]

    const hasPermission = hasRoleList.some((hasRole) => hasRole === true)

    if (!hasPermission) {
      throw new NotAllowed(CreateEntry.name)
    }
  }

  async execute(request: Readonly<CreateEntryDTO>): Promise<EntryDTO> {
    const collectionId = new UniqueEntityID(request.collectionId)
    const data = await this.processData(request.data)
    const currentUser = AuthContext.getUser()

    this.checkPermissions(collectionId, currentUser)

    // TODO: Should we add as a change or directly to the entry?

    const entry = new Entry({
      collectionId,
      changes: [],
      createdBy: currentUser.id,
      data: data,
      visibility: request.visibility,
    })

    await this._entryRepository.save(entry)

    return EntryMapper.getInstance().toDTO(entry)
  }
}

export { CreateEntry }
