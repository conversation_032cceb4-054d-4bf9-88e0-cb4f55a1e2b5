import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { stubFieldData } from '../../../../domain/__stubs__/fieldData.stub'
import { Entry } from '../../../../domain/entry'
import { entryRepository } from '../../../../repositories'
import { DeleteEntry } from '../deleteEntry'

let useCase: DeleteEntry
let user: User
let entry: Entry

describe('DeleteEntry', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    entry = stubEntry({
      collectionId: collection.id,
      createdBy: user.id,
      data: [stubFieldData({ field, value: 'value' })],
    })
    await entryRepository.save(entry)

    useCase = new DeleteEntry(entryRepository)
  })

  it('Should be able to delete an existing entry', async () => {
    await expect(
      execute(useCase, { id: entry.id.value }, user),
    ).resolves.not.toThrow()
    return expect(entryRepository.getById(entry.id)).rejects.toThrow(
      EntityNotFound,
    )
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
