import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { DeleteEntryDTO } from './deleteEntry.dto'

class DeleteEntry extends UseCase<DeleteEntryDTO, void> {
  constructor(private readonly _entryRepository: GenericEntryRepository) {
    super(DeleteEntry.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId))
      return

    throw new NotAllowed(DeleteEntry.name)
  }

  async execute(request: Readonly<DeleteEntryDTO>): Promise<void> {
    const { id } = request
    const entryId = new UniqueEntityID(id)

    const entry = await this._entryRepository.getById(entryId)

    this.checkPermissions(entry.collectionId)

    await this._entryRepository.delete(entryId)
  }
}

export { DeleteEntry }
