import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { DeleteEntry } from './deleteEntry'
import { DeleteEntryDTO } from './deleteEntry.dto'

const schema = object({
  params: object({
    entryId: string().uuid().required(),
  }).required(),
})

class DeleteEntryController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteEntryDTO
> {
  public constructor(useCase: DeleteEntry) {
    super(
      'delete',
      ':entryId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): DeleteEntryDTO {
    return { id: request.params.entryId }
  }
}

export { DeleteEntryController }
