import { fieldRepository } from '../../../../collections/repositories'
import { userRepository } from '../../../../users/repositories'
import { entryRepository } from '../../../repositories'
import { UpdateEntryController } from './controller'
import { UpdateEntry } from './updateEntry'

const useCase = new UpdateEntry(
  entryRepository,
  fieldRepository,
  userRepository,
)
const updateEntryController = new UpdateEntryController(useCase)

export { updateEntryController }
