import { domainEvents } from '../../../../../../shared/domain/events'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { EntryUpdatedEvent } from '../../../../../activities/domain/events/EntryUpdatedEvent'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { stubFieldData } from '../../../../domain/__stubs__/fieldData.stub'
import { entryRepository } from '../../../../repositories'
import { UpdateEntry } from '../updateEntry'
import { UpdateEntryDTO } from '../updateEntry.dto'

let useCase: UpdateEntry
let user: User
let entryId: string
let collectionId: string
let domainEventsSpy: jest.SpyInstance
let markEntitySpy: jest.SpyInstance

describe('UpdateEntry', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    useCase = new UpdateEntry(entryRepository, fieldRepository)
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    // Create fresh collection, field, and entry for each test
    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    // Update user assignment with new collection ID
    user.userAssignments = [
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    ]
    await userRepository.save(user)

    const entry = stubEntry({
      collectionId: collection.id,
      createdBy: user.id,
      data: [stubFieldData({ field, value: 'initial value' })],
      visibility: 'PUBLIC',
    })
    entryId = entry.id.value
    await entryRepository.save(entry)

    // Clear any existing domain events and set up spies
    jest.restoreAllMocks()
    domainEvents.clearMarkedEntities()
    domainEventsSpy = jest.spyOn(domainEvents, 'dispatchEventsForEntity')
    markEntitySpy = jest.spyOn(domainEvents, 'markEntityForDispatch')
  })

  afterEach(() => {
    // Clean up spies
    if (domainEventsSpy) {
      domainEventsSpy.mockRestore()
    }
    if (markEntitySpy) {
      markEntitySpy.mockRestore()
    }
  })

  it('should be able to update an entry', async () => {
    const request: UpdateEntryDTO = {
      id: entryId,
      data: {
        visibility: 'PRIVATE',
      },
    }
    const result = await execute(useCase, request, user)

    expect(result.visibility).toEqual(request.data.visibility)
  })

  it('should dispatch EntryUpdatedEvent when updating an entry', async () => {
    const request: UpdateEntryDTO = {
      id: entryId,
      data: {
        visibility: 'PRIVATE',
      },
    }

    const result = await execute(useCase, request, user)

    // Verify that domain events were dispatched
    expect(domainEventsSpy).toHaveBeenCalled()
    expect(markEntitySpy).toHaveBeenCalledTimes(1)

    // Verify EntryUpdatedEvent was marked for dispatch
    const markedEvent = markEntitySpy.mock.calls[0][0] as EntryUpdatedEvent
    expect(markedEvent).toBeInstanceOf(EntryUpdatedEvent)
    expect(markedEvent.entry.id.value).toBe(result.id)
    expect(markedEvent.entry.collectionId.value).toBe(collectionId)
    expect(markedEvent.entry.visibility).toBe('PRIVATE')
    expect(markedEvent.updatedBy.equals(user.id)).toBe(true)
    expect(markedEvent.activityType).toBe('ENTRY_UPDATE')
    expect(markedEvent.activityData).toEqual({
      entryId: result.id,
      collectionId: collectionId,
      updatedBy: user.id.value,
      visibility: 'PRIVATE',
    })
  })

  it('should not dispatch EntryUpdatedEvent when no actual changes are made', async () => {
    const request: UpdateEntryDTO = {
      id: entryId,
      data: {
        visibility: 'PUBLIC', // Same as initial value
      },
    }

    await execute(useCase, request, user)

    // Verify that no domain events were dispatched since no actual changes were made
    expect(markEntitySpy).not.toHaveBeenCalled()
  })

  it('should not dispatch EntryUpdatedEvent when no updates are provided', async () => {
    const request: UpdateEntryDTO = {
      id: entryId,
      data: {}, // No updates
    }

    await execute(useCase, request, user)

    // Verify that no domain events were dispatched since no updates were made
    expect(markEntitySpy).not.toHaveBeenCalled()
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
