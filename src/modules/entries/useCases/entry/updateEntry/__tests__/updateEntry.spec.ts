import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
  fieldRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { stubFieldData } from '../../../../domain/__stubs__/fieldData.stub'
import { entryRepository } from '../../../../repositories'
import { UpdateEntry } from '../updateEntry'
import { UpdateEntryDTO } from '../updateEntry.dto'

let useCase: UpdateEntry
let user: User
let entryId: string

describe('UpdateEntry', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    const entry = stubEntry({
      collectionId: collection.id,
      createdBy: user.id,
      data: [stubFieldData({ field, value: 'value' })],
    })
    entryId = entry.id.value
    await entryRepository.save(entry)

    useCase = new UpdateEntry(entryRepository, fieldRepository)
  })

  it('should be able to update an entry', async () => {
    const request: UpdateEntryDTO = {
      id: entryId,
      data: {
        visibility: 'PRIVATE',
      },
    }
    const result = await execute(useCase, request, user)

    expect(result.visibility).toEqual(request.data.visibility)
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
