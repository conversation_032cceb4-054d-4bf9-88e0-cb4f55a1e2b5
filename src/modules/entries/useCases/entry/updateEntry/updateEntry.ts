import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { GenericFieldRepository } from '../../../../collections/repositories/interfaces/genericFieldRepository'
import { Entry, EntryProps } from '../../../domain/entry'
import { EntryDTO } from '../../../dto/entryDTO'
import { EntryMapper } from '../../../mappers'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { UpdateEntryDTO } from './updateEntry.dto'

class UpdateEntry extends UseCase<UpdateEntryDTO, EntryDTO> {
  constructor(
    private readonly _entryRepository: GenericEntryRepository,
    private readonly _fieldRepository: GenericFieldRepository,
  ) {
    super(UpdateEntry.name)
  }

  private checkPermissions(entry: Entry) {
    const currentUser = AuthContext.getUser()

    const isCollectionManager = UserUtils.hasRole(
      currentUser,
      'COLLECTION_MANAGER',
      entry.collectionId,
    )

    const hasOtherRoleList = [
      UserUtils.hasRole(currentUser, 'EDITOR', entry.collectionId),
      UserUtils.hasRole(currentUser, 'CONTRIBUTOR', entry.collectionId),
    ]

    const hasPermission = hasOtherRoleList.some((hasRole) => hasRole === true)

    if (isCollectionManager) return

    if (hasPermission && entry.createdBy.equals(currentUser.id)) return

    throw new NotAllowed(UpdateEntry.name)
  }

  async execute(request: Readonly<UpdateEntryDTO>): Promise<EntryDTO> {
    const { id, data } = request
    const entryId = new UniqueEntityID(id)
    const entry = await this._entryRepository.getById(entryId)

    this.checkPermissions(entry)

    const updates = this.buildUpdateProps(data, entry)

    if (Object.keys(updates).length > 0) {
      entry.update(updates)
      await this._entryRepository.save(entry)
    }

    return EntryMapper.getInstance().toDTO(entry)
  }

  private buildUpdateProps(
    data: UpdateEntryDTO['data'],
    entry: Awaited<ReturnType<GenericEntryRepository['getById']>>,
  ): Partial<EntryProps> {
    const updates: Partial<EntryProps> = {}

    if (data.visibility !== undefined && data.visibility !== entry.visibility) {
      updates.visibility = data.visibility
    }

    return updates
  }
}

export { UpdateEntry }
