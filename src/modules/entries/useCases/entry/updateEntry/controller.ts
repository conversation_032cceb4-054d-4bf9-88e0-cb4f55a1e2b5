import { constants } from 'http2'
import { array, InferType, mixed, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { EntryVisibility } from '../../../domain/entry'
import { EntryDTO } from '../../../dto/entryDTO'
import { UpdateEntry } from './updateEntry'
import { UpdateEntryDTO } from './updateEntry.dto'

const schema = object({
  params: object({
    entryId: string().uuid().required(),
  }).required(),
  body: object({
    visibility: mixed<EntryVisibility>().oneOf(['PUBLIC', 'PRIVATE']),
    data: array(
      object({
        fieldId: string().uuid().required(),
        value: mixed<string | string[]>().required(),
      }),
    ),
  }).required(),
})

class UpdateEntryController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateEntryDTO,
  EntryDTO
> {
  public constructor(useCase: UpdateEntry) {
    super('patch', ':entryId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): UpdateEntryDTO {
    return {
      id: request.params.entryId,
      data: request.body,
    }
  }
}

export { UpdateEntryController }
