import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../../shared/infra/controller'
import { EntryDTO } from '../../../dto/entryDTO'
import { GetEntry } from './getEntry'
import { GetEntryDTO } from './getEntry.dto'

const schema = object({
  params: object({
    entryId: string().uuid().required(),
  }).required(),
})

class GetEntryController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetEntryDTO,
  EntryDTO
> {
  public constructor(useCase: GetEntry) {
    super('get', ':entryId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetEntryDTO {
    return { id: request.params.entryId }
  }
}

export { GetEntryController }
