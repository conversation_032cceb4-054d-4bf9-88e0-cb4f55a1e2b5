import { UniqueEntityID } from '../../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { stubFieldData } from '../../../../domain/__stubs__/fieldData.stub'
import { Entry } from '../../../../domain/entry'
import { entryRepository } from '../../../../repositories'
import { GetEntry } from '../getEntry'

let useCase: GetEntry
let user: User
let entry: Entry

describe('GetEntry', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    entry = stubEntry({
      collectionId: collection.id,
      createdBy: user.id,
      data: [stubFieldData({ field, value: 'value' })],
    })
    await entryRepository.save(entry)

    useCase = new GetEntry(entryRepository)
  })

  it('Should get the specified entry', async () => {
    const result = await execute(useCase, { id: entry.id.value }, user)

    expect(result.collectionId).toEqual(entry.collectionId.value)
    expect(result.visibility).toEqual(entry.visibility)
    expect(result.data[0]?.fieldId).toEqual(entry.data[0]?.field.id.value)
    expect(result.data[0]?.value).toEqual(entry.data[0]?.value)
  })

  it('Should fail to get an entry that does not exist', async () => {
    return expect(
      execute(useCase, { id: new UniqueEntityID().value }, user),
    ).rejects.toThrow(EntityNotFound)
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
