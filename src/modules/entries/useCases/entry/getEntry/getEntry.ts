import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { EntryDTO } from '../../../dto/entryDTO'
import { EntryMapper } from '../../../mappers'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { GetEntryDTO } from './getEntry.dto'

class GetEntry extends UseCase<GetEntryDTO, EntryDTO> {
  constructor(private readonly _entryRepository: GenericEntryRepository) {
    super(GetEntry.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetEntry.name)
  }

  async execute(request: Readonly<GetEntryDTO>): Promise<EntryDTO> {
    const { id } = request

    const entry = await this._entryRepository.getById(new UniqueEntityID(id))

    this.checkPermissions(entry.collectionId)

    return EntryMapper.getInstance().toDTO(entry)
  }
}

export { GetEntry }
