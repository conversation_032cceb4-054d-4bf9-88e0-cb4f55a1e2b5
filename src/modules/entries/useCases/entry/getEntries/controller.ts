import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { QueryDTO } from '../../../../../shared/dto/queryDTO'
import { Controller } from '../../../../../shared/infra/controller'
import { QuerySchema } from '../../../../../shared/types/query'
import { EntryDTO } from '../../../dto/entryDTO'
import { GetEntries } from './getEntries'
import { GetEntriesDTO } from './getEntries.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  query: QuerySchema,
})

class GetEntriesController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryDTO,
  GetEntriesDTO,
  PaginatedResponseDTO<EntryDTO>
> {
  public constructor(useCase: GetEntries) {
    super(
      'get',
      ':collectionId/entries',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): GetEntriesDTO {
    return { collectionId: request.params.collectionId, ...request.query }
  }
}

export { GetEntriesController }
