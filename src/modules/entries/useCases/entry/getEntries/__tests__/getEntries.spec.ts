import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../../utils/tests/tests'
import { stubCollection } from '../../../../../collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../../../../../collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../../../../../collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../../../../../collections/domain/__stubs__/fields.stub'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../../../../../collections/repositories'
import { stubUser } from '../../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../../users/domain/user'
import { userRepository } from '../../../../../users/repositories'
import { stubEntry } from '../../../../domain/__stubs__/entry.stub'
import { stubFieldData } from '../../../../domain/__stubs__/fieldData.stub'
import { Entry } from '../../../../domain/entry'
import { entryRepository } from '../../../../repositories'
import { GetEntries } from '../getEntries'

let useCase: GetEntries
let user: User
let collectionId: string
let entry: Entry

describe('GetEntries', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
    await userRepository.save(user)

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    const field = stubSingleValueField({
      fieldGroupId: fieldGroup.id,
      preview: true,
    })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    entry = stubEntry({
      collectionId: collection.id,
      createdBy: user.id,
      data: [stubFieldData({ field, value: 'value' })],
    })
    await entryRepository.save(entry)

    useCase = new GetEntries(entryRepository)
  })

  it('Should get collection entries', async () => {
    const result = await execute(useCase, { collectionId }, user)

    expect(result.totalItems).toEqual(1)
    expect(result.items[0]?.collectionId).toEqual(entry.collectionId.value)
    expect(result.items[0]?.visibility).toEqual(entry.visibility)
    expect(result.items[0]?.data[0]?.fieldId).toEqual(
      entry.data[0]?.field.id.value,
    )
    expect(result.items[0]?.data[0]?.value).toEqual(entry.data[0]?.value)
  })
})
afterAll(async () => {
  await closeTestDatabase()
})
