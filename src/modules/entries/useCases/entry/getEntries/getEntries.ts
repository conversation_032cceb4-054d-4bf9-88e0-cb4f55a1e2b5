import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { IdDTO } from '../../../../../shared/dto/idDTO'
import { PaginatedResponseDTO } from '../../../../../shared/dto/paginatedResponseDTO'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../../shared/infra/useCase'
import { UserUtils } from '../../../../../utils/userUtils'
import { EntryDTO } from '../../../dto/entryDTO'
import { EntryMapper } from '../../../mappers'
import { GenericEntryRepository } from '../../../repositories/interfaces/genericEntryRepository'
import { GetEntriesDTO } from './getEntries.dto'

class GetEntries extends UseCase<
  GetEntriesDTO,
  PaginatedResponseDTO<EntryDTO>
> {
  constructor(private readonly _entryRepository: GenericEntryRepository) {
    super(GetEntries.name)
  }

  private getEntries(collectionId: IdDTO) {
    return this._entryRepository.getByCollectionId(
      new UniqueEntityID(collectionId),
    )
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return
    if (UserUtils.hasAssignment(currentUser, collectionId)) return

    throw new NotAllowed(GetEntries.name)
  }

  async execute(
    request: Readonly<GetEntriesDTO>,
  ): Promise<PaginatedResponseDTO<EntryDTO>> {
    const collectionId = new UniqueEntityID(request.collectionId)

    this.checkPermissions(collectionId)

    const result = await this._entryRepository.getByCollectionId(
      collectionId,
      request,
    )

    return EntryMapper.getInstance().toPageDTO(result, true)
  }
}

export { GetEntries }
