import { Email } from '../../../shared/domain/email'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { User as Domain, UserProps } from '../domain/user'
import { UserAssignment } from '../domain/userAssignment'
import { UserSettings } from '../domain/userSettings'
import { UserDTO as DTO } from '../dto/userDTO'
import { UserModel as Model } from '../models/user.model'

class UserMapper implements Mapper<Domain, Model, DTO> {
  private static _instance: UserMapper

  toDomain(model: Model): Domain {
    const userAssignments = model.assignments
      ? model.assignments.map(
          (assignment) =>
            new UserAssignment({
              userId: new UniqueEntityID(assignment.userId),
              role: assignment.role,
              collectionId: assignment.collectionId
                ? new UniqueEntityID(assignment.collectionId)
                : undefined,
            }),
        )
      : []

    const userSettings = new UserSettings({
      emailNotifications: model.emailNotifications,
    })

    const userProps: UserProps = {
      name: model.name,
      email: new Email({ value: model.email }),
      settings: userSettings,
      userAssignments,
    }

    return new Domain(
      userProps,
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt ?? undefined,
    )
  }

  toModel(domain: Domain): Model {
    return {
      id: domain.id.value,
      name: domain.name,
      email: domain.email.value,
      emailNotifications: domain.emailNotifications,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
      deleted: domain.deleted,
    }
  }

  toDTO(domain: Domain): DTO {
    return {
      id: domain.id.value,
      name: domain.name,
      email: domain.email.value,
      settings: {
        emailNotifications: domain.emailNotifications,
      },
      assignments: domain.userAssignments.map((assignment) => ({
        collectionId: assignment.collectionId?.value,
        role: assignment.role,
      })),
      deleted: domain.deleted,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
    }
  }

  toPageDTO(domain: PaginatedDomain<Domain>): PaginatedResponseDTO<DTO> {
    return {
      items: domain.items.map(this.toDTO),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!UserMapper._instance) {
      UserMapper._instance = new UserMapper()
    }
    return UserMapper._instance
  }
}

export { UserMapper }
