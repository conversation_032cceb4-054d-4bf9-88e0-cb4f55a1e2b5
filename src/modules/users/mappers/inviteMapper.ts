import { Email } from '../../../shared/domain/email'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Invite as Domain } from '../domain/invite'
import { InviteDTO as DTO } from '../dto/inviteDTO'
import { InviteModel as Model } from '../models/invite.model'

class InviteMapper implements Mapper<Domain, Model, DTO> {
  private static _instance: InviteMapper

  toDomain(model: Model): Domain {
    return new Domain(
      {
        name: model.name,
        email: new Email({ value: model.email }),
        status: model.status,
        role: model.role,
        collectionId: model.collectionId
          ? new UniqueEntityID(model.collectionId)
          : undefined,
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt ?? undefined,
      model.deleted,
    )
  }

  toModel(domain: Domain): Model {
    return {
      id: domain.id.value,
      name: domain.name,
      email: domain.email.value,
      status: domain.status,
      role: domain.role,
      collectionId: domain.collectionId ? domain.collectionId.value : null,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
      deleted: domain.deleted,
    }
  }

  toDTO(domain: Domain): DTO {
    if (domain.role === 'PLATFORM_MANAGER') {
      return {
        id: domain.id.value,
        name: domain.name,
        email: domain.email.value,
        role: domain.role,
        status: domain.status,
        deleted: domain.deleted,
        createdAt: domain.createdAt.toISOString(),
        updatedAt: domain.updatedAt?.toISOString(),
      }
    }

    return {
      id: domain.id.value,
      name: domain.name,
      email: domain.email.value,
      role: domain.role,
      status: domain.status,
      collectionId: domain.collectionId?.value,
      deleted: domain.deleted,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
    }
  }

  toPageDTO(domain: PaginatedDomain<Domain>): PaginatedResponseDTO<DTO> {
    return {
      items: domain.items.map(this.toDTO),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!InviteMapper._instance) {
      InviteMapper._instance = new InviteMapper()
    }
    return InviteMapper._instance
  }
}

export { InviteMapper }
