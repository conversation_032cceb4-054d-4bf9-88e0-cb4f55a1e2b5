import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { UserDTO } from '../../dto/userDTO'
import { AcceptInvite } from './acceptInvite'
import { AcceptInviteDTO } from './acceptInvite.dto'

const schema = object({
  params: object({
    inviteId: string().uuid().required(),
  }).required(),
})

class AcceptInviteController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  AcceptInviteDTO,
  UserDTO
> {
  public constructor(useCase: AcceptInvite) {
    super('post', ':inviteId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): AcceptInviteDTO {
    return { inviteId: request.params.inviteId }
  }
}

export { AcceptInviteController }
