import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { IdDTO } from '../../../../shared/dto/idDTO'
import { UseCase } from '../../../../shared/infra/useCase'
import { User } from '../../domain/user'
import { UserAssignment } from '../../domain/userAssignment'
import { UserSettings } from '../../domain/userSettings'
import { UserDTO } from '../../dto/userDTO'
import { UserMapper } from '../../mappers'
import { GenericInviteRepository } from '../../repositories/interfaces/genericInviteRepository'
import { GenericUserRepository } from '../../repositories/interfaces/genericUserRepository'
import { Role } from '../../types/user'
import { AcceptInviteDTO } from './acceptInvite.dto'
import { InvalidInviteState } from './acceptInvite.errors'

class AcceptInvite extends UseCase<AcceptInviteDTO, UserDTO> {
  constructor(
    private readonly _inviteRepository: GenericInviteRepository,
    private readonly _userRepository: GenericUserRepository,
  ) {
    super(AcceptInvite.name)
  }

  private getInvite(inviteId: IdDTO) {
    return this._inviteRepository.getById(new UniqueEntityID(inviteId))
  }

  private generateAssignment(
    user: User,
    role: Role,
    collectionId?: UniqueEntityID,
  ) {
    return new UserAssignment({
      userId: user.id,
      role,
      collectionId: collectionId,
    })
  }

  async execute(request: Readonly<AcceptInviteDTO>): Promise<UserDTO> {
    const invite = await this.getInvite(request.inviteId)
    const { email, name } = invite

    if (invite.status !== 'PENDING') throw new InvalidInviteState()

    let user: User | undefined
    user = await this._userRepository.findByEmail(email.value)
    if (!user) {
      user = new User({
        name,
        email,
        settings: new UserSettings({
          emailNotifications: true,
        }),
        userAssignments: [],
      })
    }

    const assignment = this.generateAssignment(
      user,
      invite.role,
      invite.collectionId,
    )
    user.userAssignments.push(assignment)

    await this._userRepository.save(user)

    return UserMapper.getInstance().toDTO(user)
  }
}

export { AcceptInvite }
