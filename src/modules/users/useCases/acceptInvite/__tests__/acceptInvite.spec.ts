import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { Collection } from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import { collectionRepository } from '../../../../collections/repositories'
import { stubInvite } from '../../../domain/__stubs__/invite.stub'
import { stubUser } from '../../../domain/__stubs__/user.stub'
import { Invite } from '../../../domain/invite'
import { UserAssignment } from '../../../domain/userAssignment'
import { inviteRepository, userRepository } from '../../../repositories'
import { AcceptInvite } from '../acceptInvite'
import { AcceptInviteDTO } from '../acceptInvite.dto'

describe('AcceptInvite Use Case', () => {
  const acceptInviteUseCase = new AcceptInvite(inviteRepository, userRepository)

  let testCollection: Collection
  let testEditorInvite: Invite

  describe('Accept Invite Use Case', () => {
    beforeAll(async () => {
      await setupTestDatabase()
    })

    afterAll(async () => {
      await closeTestDatabase()
    })

    beforeEach(async () => {
      await truncateTestDatabase()

      testCollection = stubCollection({
        name: 'Default Test Collection',
        services: new Services([]),
        fieldGroups: new FieldGroups([]),
      })
      await collectionRepository.save(testCollection)

      testEditorInvite = stubInvite({
        collectionId: testCollection.id,
        role: 'EDITOR',
        email: stubEmail({ value: '<EMAIL>' }),
        name: 'Invited Editor',
        status: 'PENDING',
      })
      await inviteRepository.save(testEditorInvite)
    })

    it('should create a new user, assign role from default invite, and return user DTO', async () => {
      const acceptInviteDTO: AcceptInviteDTO = {
        inviteId: testEditorInvite.id.value,
      }

      const resultUserDTO = await acceptInviteUseCase.execute(acceptInviteDTO)

      expect(resultUserDTO).toBeDefined()
      expect(resultUserDTO.email).toBe(testEditorInvite.email.value)
      expect(resultUserDTO.name).toBe(testEditorInvite.name)
      expect(resultUserDTO.assignments).toBeDefined()
      expect(resultUserDTO.assignments.length).toBe(1)

      const firstAssignmentDTO = resultUserDTO.assignments[0]
      expect(firstAssignmentDTO).toBeDefined()
      if (firstAssignmentDTO) {
        expect(firstAssignmentDTO.role).toBe('EDITOR')
        expect(firstAssignmentDTO.collectionId).toBe(testCollection.id.value)
      }

      const createdUserDomain = await userRepository.getById(
        new UniqueEntityID(resultUserDTO.id),
      )
      expect(createdUserDomain).toBeDefined()
      if (createdUserDomain) {
        expect(createdUserDomain.name).toBe(testEditorInvite.name)
        expect(createdUserDomain.email.value).toBe(testEditorInvite.email.value)
        expect(createdUserDomain.userAssignments.length).toBe(1)

        const firstUserAssignmentDomain = createdUserDomain.userAssignments[0]
        expect(firstUserAssignmentDomain).toBeDefined()
        if (firstUserAssignmentDomain) {
          expect(firstUserAssignmentDomain.role).toBe('EDITOR')
          expect(
            firstUserAssignmentDomain.collectionId?.equals(testCollection.id),
          ).toBe(true)
        }
      }
    })

    it('should throw an error if the invite does not exist', async () => {
      const nonExistentInviteId = new UniqueEntityID().value
      const acceptInviteDTO: AcceptInviteDTO = {
        inviteId: nonExistentInviteId,
      }

      await expect(
        acceptInviteUseCase.execute(acceptInviteDTO),
      ).rejects.toThrow(new EntityNotFound('Invite', nonExistentInviteId))
    })

    it('should create a user with PLATFORM_MANAGER role when invite is for platform manager', async () => {
      const platformManagerInvite = stubInvite({
        collectionId: undefined,
        role: 'PLATFORM_MANAGER',
        email: stubEmail({ value: '<EMAIL>' }),
        name: 'Platform Admin User',
        status: 'PENDING',
      })
      await inviteRepository.save(platformManagerInvite)

      const acceptInviteDTO: AcceptInviteDTO = {
        inviteId: platformManagerInvite.id.value,
      }

      const resultUserDTO = await acceptInviteUseCase.execute(acceptInviteDTO)

      expect(resultUserDTO).toBeDefined()
      expect(resultUserDTO.email).toBe(platformManagerInvite.email.value)
      expect(resultUserDTO.name).toBe(platformManagerInvite.name)
      expect(resultUserDTO.assignments.length).toBe(1)

      const firstAssignmentDTO = resultUserDTO.assignments[0]
      expect(firstAssignmentDTO).toBeDefined()
      if (firstAssignmentDTO) {
        expect(firstAssignmentDTO.role).toBe('PLATFORM_MANAGER')
        expect(firstAssignmentDTO.collectionId).toBeUndefined()
      }

      const createdUserDomain = await userRepository.getById(
        new UniqueEntityID(resultUserDTO.id),
      )
      expect(createdUserDomain).toBeDefined()
      if (createdUserDomain) {
        const firstUserAssignmentDomain = createdUserDomain.userAssignments[0]
        expect(firstUserAssignmentDomain).toBeDefined()
        if (firstUserAssignmentDomain) {
          expect(firstUserAssignmentDomain.role).toBe('PLATFORM_MANAGER')
          expect(firstUserAssignmentDomain.collectionId).toBeUndefined()
        }
      }
    })

    it('should add a new assignment to an existing user with the invite email', async () => {
      const existingUserEmail = stubEmail({ value: '<EMAIL>' })
      const existingUser = stubUser({
        name: 'Existing User',
        email: existingUserEmail,
        userAssignments: [],
      })
      await userRepository.save(existingUser)

      const newInvite = stubInvite({
        collectionId: testCollection.id,
        role: 'VIEWER',
        email: existingUserEmail,
        name: 'Invited User',
        status: 'PENDING',
      })
      await inviteRepository.save(newInvite)

      const acceptInviteDTO: AcceptInviteDTO = {
        inviteId: newInvite.id.value,
      }

      const resultUserDTO = await acceptInviteUseCase.execute(acceptInviteDTO)

      expect(resultUserDTO).toBeDefined()
      expect(resultUserDTO.id).toBe(existingUser.id.value)
      expect(resultUserDTO.email).toBe(existingUserEmail.value)
      expect(resultUserDTO.name).toBe(existingUser.name)
      expect(resultUserDTO.assignments).toBeDefined()
      expect(resultUserDTO.assignments.length).toBe(1)

      const assignmentDTO = resultUserDTO.assignments[0]
      expect(assignmentDTO).toBeDefined()
      if (assignmentDTO) {
        expect(assignmentDTO.role).toBe('VIEWER')
        expect(assignmentDTO.collectionId).toBe(testCollection.id.value)
      }

      const updatedUser = await userRepository.getById(existingUser.id)
      expect(updatedUser).toBeDefined()
      if (updatedUser) {
        expect(updatedUser.userAssignments.length).toBe(1)

        const assignment = updatedUser.userAssignments[0]
        expect(assignment).toBeDefined()
        if (assignment) {
          expect(assignment.role).toBe('VIEWER')
          expect(assignment.collectionId?.equals(testCollection.id)).toBe(true)
        }
      }
    })

    it('should preserve existing role assignments when adding a new assignment', async () => {
      const secondCollection = stubCollection({
        name: 'Second Test Collection',
        services: new Services([]),
        fieldGroups: new FieldGroups([]),
      })
      await collectionRepository.save(secondCollection)

      const existingUserEmail = stubEmail({
        value: '<EMAIL>',
      })

      const existingUser = stubUser({
        name: 'Multi-Role User',
        email: existingUserEmail,
        userAssignments: [],
      })

      const assignment = new UserAssignment({
        userId: existingUser.id,
        role: 'EDITOR',
        collectionId: secondCollection.id,
      })
      existingUser.userAssignments.push(assignment)
      await userRepository.save(existingUser)

      const newInvite = stubInvite({
        collectionId: testCollection.id,
        role: 'VIEWER',
        email: existingUserEmail,
        name: 'Invited Again',
        status: 'PENDING',
      })
      await inviteRepository.save(newInvite)

      const acceptInviteDTO: AcceptInviteDTO = {
        inviteId: newInvite.id.value,
      }
      const resultUserDTO = await acceptInviteUseCase.execute(acceptInviteDTO)

      expect(resultUserDTO).toBeDefined()
      expect(resultUserDTO.id).toBe(existingUser.id.value)
      expect(resultUserDTO.assignments).toBeDefined()
      expect(resultUserDTO.assignments.length).toBe(2)

      const editorAssignment = resultUserDTO.assignments.find(
        (a) => a.role === 'EDITOR',
      )
      const viewerAssignment = resultUserDTO.assignments.find(
        (a) => a.role === 'VIEWER',
      )
      expect(editorAssignment).toBeDefined()
      expect(viewerAssignment).toBeDefined()
      expect(editorAssignment!.collectionId).toBe(secondCollection.id.value)
      expect(viewerAssignment!.collectionId).toBe(testCollection.id.value)

      const updatedUser = await userRepository.getById(existingUser.id)
      expect(updatedUser).toBeDefined()
      expect(updatedUser.userAssignments.length).toBe(2)
      const editorDomainAssignment = updatedUser.userAssignments.find(
        (a) => a.role === 'EDITOR',
      )
      const viewerDomainAssignment = updatedUser.userAssignments.find(
        (a) => a.role === 'VIEWER',
      )
      expect(editorDomainAssignment).toBeDefined()
      expect(viewerDomainAssignment).toBeDefined()
    })
  })
})
