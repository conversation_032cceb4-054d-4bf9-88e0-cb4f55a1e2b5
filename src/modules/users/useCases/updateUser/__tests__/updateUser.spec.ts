import { database } from '../../../../../shared/database'
import { collectionsTable } from '../../../../../shared/database/schema/collections'
import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { Collection } from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import {
  stubUser,
  stubUserWithAssignments,
} from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { User } from '../../../domain/user'
import { UserSettings } from '../../../domain/userSettings'
import { userRepository } from '../../../repositories'
import { UpdateUser } from '../updateUser'
import { UpdateUserDTO } from '../updateUser.dto'

describe('UpdateUser Use Case', () => {
  const dbClient = database
  const updateUserUseCase = new UpdateUser(userRepository)
  let user: User
  let testCollection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    testCollection = stubCollection({
      name: 'UpdateUser Test Collection',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    })
    const collectionToSave = {
      id: testCollection.id.value,
      name: testCollection.name,
      createdAt: testCollection.createdAt,
      updatedAt: testCollection.updatedAt ?? new Date(),
      deleted: testCollection.deleted,
    }
    await dbClient.insert(collectionsTable).values(collectionToSave)
  })

  it('should update users name', async () => {
    const existingUser = stubUser({
      name: 'Old Name',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(existingUser)

    const newName = 'New Name'
    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: { name: newName },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(newName)
    expect(resultDTO.email).toBe(existingUser.email.value)
  })

  it('should update users email', async () => {
    const existingUser = stubUser({
      name: 'Email Update User',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(existingUser)

    const newEmail = '<EMAIL>'
    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: { email: newEmail },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(existingUser.name)
    expect(resultDTO.email).toBe(newEmail)
  })

  it('should update users email notification settings', async () => {
    const existingUser = stubUser({
      name: 'Settings User',
      email: stubEmail({ value: '<EMAIL>' }),
      settings: new UserSettings({ emailNotifications: true }),
    })
    await userRepository.save(existingUser)

    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: { settings: { emailNotifications: false } },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.settings?.emailNotifications).toBe(false)
  })

  it('should update multiple fields (name and email)', async () => {
    const existingUser = stubUser({
      name: 'Multi Old Name',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(existingUser)

    const newName = 'Multi New Name'
    const newEmail = '<EMAIL>'
    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: { name: newName, email: newEmail },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(newName)
    expect(resultDTO.email).toBe(newEmail)
  })

  it('should throw EntityNotFound if user does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const updateUserDTO: UpdateUserDTO = {
      id: nonExistentId,
      data: { name: 'Any Name' },
    }

    await expect(
      execute(updateUserUseCase, updateUserDTO, user),
    ).rejects.toThrow(new EntityNotFound('User', nonExistentId))
  })

  it('should succeed and return user if no data is changed', async () => {
    const existingUser = stubUser({
      name: 'No Change User',
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(existingUser)

    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: {},
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(existingUser.name)
    expect(resultDTO.email).toBe(existingUser.email.value)
  })

  it('should not call save when no changes are detected', async () => {
    const existingUser = stubUser({
      name: 'Unchanged User',
      email: stubEmail({ value: '<EMAIL>' }),
      settings: new UserSettings({ emailNotifications: true }),
    })
    await userRepository.save(existingUser)

    const saveSpyMethod = jest.spyOn(userRepository, 'save')

    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: {
        name: existingUser.name, // Same value
        email: existingUser.email.value, // Same value
        settings: { emailNotifications: true }, // Same value
      },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(existingUser.name)
    expect(resultDTO.email).toBe(existingUser.email.value)
    expect(saveSpyMethod).not.toHaveBeenCalled()

    saveSpyMethod.mockRestore()
  })

  it('should properly handle falsy values for emailNotifications', async () => {
    const existingUser = stubUser({
      name: 'Falsy Settings User',
      email: stubEmail({ value: '<EMAIL>' }),
      settings: new UserSettings({ emailNotifications: true }),
    })
    await userRepository.save(existingUser)

    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: { settings: { emailNotifications: false } },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.settings?.emailNotifications).toBe(false)

    // Verify it was actually saved to the database
    const updatedUserFromDb = await userRepository.getById(existingUser.id)
    expect(updatedUserFromDb?.settings.emailNotifications).toBe(false)
  })

  it('should not affect user assignments', async () => {
    const existingUser = stubUserWithAssignments(
      {
        name: 'User With Roles',
        email: stubEmail({ value: '<EMAIL>' }),
        settings: new UserSettings({ emailNotifications: true }),
      },
      testCollection.id,
    )
    await userRepository.save(existingUser)

    const originalAssignmentsCount = existingUser.userAssignments.length

    const newName = 'Updated Name For Assignments Test'
    const updateUserDTO: UpdateUserDTO = {
      id: existingUser.id.value,
      data: { name: newName, settings: { emailNotifications: false } },
    }

    const resultDTO = await execute(updateUserUseCase, updateUserDTO, user)

    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(newName)
    expect(resultDTO.settings?.emailNotifications).toBe(false)
    expect(resultDTO.assignments.length).toBe(originalAssignmentsCount)

    const updatedUserFromDb = await userRepository.getById(existingUser.id)
    expect(updatedUserFromDb?.userAssignments.length).toBe(
      originalAssignmentsCount,
    )
    expect(updatedUserFromDb?.name).toBe(newName)
  })
})
