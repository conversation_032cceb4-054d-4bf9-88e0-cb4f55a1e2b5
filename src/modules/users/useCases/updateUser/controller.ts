import { constants } from 'http2'
import { object, string, boolean, array, InferType } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { UserDTO } from '../../dto/userDTO'
import { UpdateUser } from './updateUser'
import { UpdateUserDTO } from './updateUser.dto'

const schema = object({
  params: object({
    userId: string().uuid().required(),
  }).required(),
  body: object({
    name: string(),
    email: string().email(),
    settings: object({
      emailNotifications: boolean(),
    }),
    assignments: array().of(
      object({
        role: string().required(),
        collectionId: string().uuid(),
      }),
    ),
  })
    .required()
    .test(
      'at-least-one-key',
      'Request body must not be empty and must contain at least one valid key to update.',
      (value) => Object.keys(value).length > 0,
    ),
})

class UpdateUserController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  UpdateUserDTO,
  UserDTO
> {
  public constructor(useCase: UpdateUser) {
    super('patch', ':userId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): UpdateUserDTO {
    return {
      id: request.params.userId,
      data: request.body,
    }
  }
}

export { UpdateUserController }
