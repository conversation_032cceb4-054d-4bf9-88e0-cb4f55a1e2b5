import { Email } from '../../../../shared/domain/email'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { User, UserProps } from '../../domain/user'
import { UserSettings } from '../../domain/userSettings'
import { UserDTO } from '../../dto/userDTO'
import { UserMapper } from '../../mappers'
import { GenericUserRepository } from '../../repositories/interfaces/genericUserRepository'
import { UpdateUserDTO } from './updateUser.dto'

class UpdateUser extends UseCase<UpdateUserDTO, UserDTO> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(UpdateUser.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(UpdateUser.name)
  }

  async execute(request: Readonly<UpdateUserDTO>): Promise<UserDTO> {
    this.checkPermissions()

    const userId = new UniqueEntityID(request.id)
    const user = await this._userRepository.getById(userId)

    const updates = this.buildUpdateProps(request.data, user)

    if (Object.keys(updates).length === 0) {
      return UserMapper.getInstance().toDTO(user)
    }

    user.update(updates)

    await this._userRepository.save(user)

    return UserMapper.getInstance().toDTO(user)
  }

  private buildUpdateProps(
    data: UpdateUserDTO['data'],
    user: User,
  ): Partial<UserProps> {
    const updates: Partial<UserProps> = {}

    if (data.name !== undefined && data.name !== user.name) {
      updates.name = data.name
    }

    if (data.email !== undefined && data.email !== user.email.value) {
      updates.email = new Email({ value: data.email })
    }

    if (
      data.settings?.emailNotifications !== undefined &&
      data.settings.emailNotifications !== user.settings.emailNotifications
    ) {
      updates.settings = new UserSettings({
        emailNotifications: data.settings.emailNotifications,
      })
    }

    return updates
  }
}

export { UpdateUser }
