import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { InviteDTO } from '../../dto/inviteDTO'
import { GetInvite } from './getInvite'
import { GetInviteDTO } from './getInvite.dto'

const schema = object({
  params: object({
    inviteId: string().uuid().required(),
  }).required(),
})

class GetInviteController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetInviteDTO,
  InviteDTO
> {
  public constructor(useCase: GetInvite) {
    super('get', ':inviteId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetInviteDTO {
    return { id: request.params.inviteId }
  }
}

export { GetInviteController }
