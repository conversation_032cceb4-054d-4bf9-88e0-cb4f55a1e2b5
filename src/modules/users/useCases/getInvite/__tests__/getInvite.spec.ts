import { database } from '../../../../../shared/database'
import { collectionsTable } from '../../../../../shared/database/schema/collections'
import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { Collection } from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import { stubInvite } from '../../../domain/__stubs__/invite.stub'
import { stubUser } from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { Invite } from '../../../domain/invite'
import { User } from '../../../domain/user'
import { inviteRepository } from '../../../repositories'
import { InviteStatus } from '../../../types/invite'
import { CollectionRole } from '../../../types/user'
import { GetInvite } from '../getInvite'
import { GetInviteDTO } from '../getInvite.dto'

describe('GetInvite Use Case', () => {
  const dbClient = database
  const getInviteUseCase = new GetInvite(inviteRepository)
  let user: User

  let testCollection: Collection
  let existingInvite: Invite

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    testCollection = stubCollection({
      name: 'Test Collection for GetInvite',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    })
    const collectionToSave = {
      id: testCollection.id.value,
      name: testCollection.name,
      createdAt: testCollection.createdAt,
      updatedAt: testCollection.updatedAt ?? new Date(),
      deleted: testCollection.deleted,
    }
    await dbClient.insert(collectionsTable).values(collectionToSave)

    existingInvite = stubInvite({
      name: 'Existing User',
      email: stubEmail({ value: '<EMAIL>' }),
      role: 'EDITOR' as CollectionRole,
      collectionId: testCollection.id,
      status: 'PENDING' as InviteStatus,
    })
    await inviteRepository.save(existingInvite)
  })

  it('should return invite DTO if invite exists', async () => {
    const getInviteDTO: GetInviteDTO = {
      id: existingInvite.id.value,
    }

    const resultDTO = await execute(getInviteUseCase, getInviteDTO, user)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(existingInvite.id.value)
    expect(resultDTO.name).toBe(existingInvite.name)
    expect(resultDTO.email).toBe(existingInvite.email.value)
    expect(resultDTO.role).toBe(existingInvite.role)
    expect(resultDTO.status).toBe(existingInvite.status)

    if ('collectionId' in resultDTO) {
      expect(resultDTO.collectionId).toBe(testCollection.id.value)
    } else {
      fail(
        'Expected collectionId to be present in the DTO for a collection invite',
      )
    }
  })

  it('should throw EntityNotFound if invite does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const getInviteDTO: GetInviteDTO = {
      id: nonExistentId,
    }

    await expect(execute(getInviteUseCase, getInviteDTO, user)).rejects.toThrow(
      new EntityNotFound('Invite', nonExistentId),
    )
  })
})
