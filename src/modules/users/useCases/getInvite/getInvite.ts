import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { InviteDTO } from '../../dto/inviteDTO'
import { InviteMapper } from '../../mappers/inviteMapper'
import { GenericInviteRepository } from '../../repositories/interfaces/genericInviteRepository'
import { GetInviteDTO } from './getInvite.dto'

class GetInvite extends UseCase<GetInviteDTO, InviteDTO> {
  constructor(private readonly _inviteRepository: GenericInviteRepository) {
    super(GetInvite.name)
  }

  private checkPermissions(collectionId?: UniqueEntityID) {
    const user = AuthContext.getUser()

    if (
      UserUtils.hasRole(user, 'PLATFORM_MANAGER') ||
      (collectionId &&
        UserUtils.hasRole(user, 'COLLECTION_MANAGER', collectionId))
    )
      return

    throw new NotAllowed(GetInvite.name)
  }

  override async execute(request: Readonly<GetInviteDTO>): Promise<InviteDTO> {
    const id = new UniqueEntityID(request.id)

    const invite = await this._inviteRepository.getById(id)

    this.checkPermissions(invite.collectionId)

    return InviteMapper.getInstance().toDTO(invite)
  }
}

export { GetInvite }
