import { DatabaseClient, database } from '../../../../../shared/database'
import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { InvalidState } from '../../../../../shared/errors/domainErrors'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { Collection } from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import { collectionRepository } from '../../../../collections/repositories'
import { stubInvite } from '../../../domain/__stubs__/invite.stub'
import { stubUser } from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { Invite } from '../../../domain/invite'
import { User } from '../../../domain/user'
import { InviteRepository } from '../../../repositories/inviteRepository'
import { InviteStatus } from '../../../types/invite'
import { CancelInvite } from '../cancelInvite'
import { CancelInviteDTO } from '../cancelInvite.dto'

describe('CancelInvite Use Case', () => {
  let dbClient: DatabaseClient
  let inviteRepository: InviteRepository
  let cancelInviteUseCase: CancelInvite
  let user: User

  let testCollection: Collection
  let pendingInvite: Invite

  async function createAndSaveInviteWithStatus(
    status: InviteStatus,
    emailSuffix: string,
  ): Promise<Invite> {
    const invite = stubInvite({
      collectionId: testCollection.id,
      role: 'VIEWER',
      email: stubEmail({ value: `invite-${emailSuffix}@example.com` }),
      name: `Invite ${status} User`,
      status: status,
    })
    await inviteRepository.save(invite)
    return invite
  }

  beforeAll(async () => {
    await setupTestDatabase()
    dbClient = database
    inviteRepository = new InviteRepository(dbClient)
    cancelInviteUseCase = new CancelInvite(inviteRepository)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('execute', () => {
    beforeEach(async () => {
      await truncateTestDatabase()

      testCollection = stubCollection({
        name: 'Default Test Collection',
        services: new Services([]),
        fieldGroups: new FieldGroups([]),
      })
      await collectionRepository.save(testCollection)

      pendingInvite = await createAndSaveInviteWithStatus(
        'PENDING' as InviteStatus,
        'pending',
      )
    })

    it('should cancel a PENDING invite and return the updated invite DTO', async () => {
      const cancelInviteDTO: CancelInviteDTO = {
        id: pendingInvite.id.value,
      }

      const resultDTO = await execute(
        cancelInviteUseCase,
        cancelInviteDTO,
        user,
      )

      expect(resultDTO).toBeDefined()
      expect(resultDTO.id).toBe(pendingInvite.id.value)
      expect(resultDTO.status).toBe('CANCELLED')

      const updatedInviteDomain = await inviteRepository.getById(
        pendingInvite.id,
      )
      expect(updatedInviteDomain).toBeDefined()
      if (updatedInviteDomain) {
        expect(updatedInviteDomain.status).toBe('CANCELLED')
      }
    })

    it('should throw EntityNotFound if the invite does not exist', async () => {
      const nonExistentInviteId = new UniqueEntityID().value
      const cancelInviteDTO: CancelInviteDTO = {
        id: nonExistentInviteId,
      }

      await expect(
        execute(cancelInviteUseCase, cancelInviteDTO, user),
      ).rejects.toThrow(new EntityNotFound('Invite', nonExistentInviteId))
    })

    it('should throw InvalidState when trying to cancel a REGISTERED invite', async () => {
      const registeredInvite = await createAndSaveInviteWithStatus(
        'REGISTERED' as InviteStatus,
        'registered',
      )
      const cancelInviteDTO: CancelInviteDTO = {
        id: registeredInvite.id.value,
      }

      await expect(
        execute(cancelInviteUseCase, cancelInviteDTO, user),
      ).rejects.toThrow(new InvalidState('Cannot cancel invite', 'Invite'))
    })

    it('should throw InvalidState when trying to cancel an already CANCELLED invite', async () => {
      await execute(cancelInviteUseCase, { id: pendingInvite.id.value }, user)

      const cancelInviteDTO: CancelInviteDTO = {
        id: pendingInvite.id.value,
      }

      await expect(
        execute(cancelInviteUseCase, cancelInviteDTO, user),
      ).rejects.toThrow(new InvalidState('Cannot cancel invite', 'Invite'))
    })

    it('should throw InvalidState when trying to cancel an EXPIRED invite', async () => {
      const expiredInvite = await createAndSaveInviteWithStatus(
        'EXPIRED' as InviteStatus,
        'expired',
      )
      const cancelInviteDTO: CancelInviteDTO = {
        id: expiredInvite.id.value,
      }

      await expect(
        execute(cancelInviteUseCase, cancelInviteDTO, user),
      ).rejects.toThrow(new InvalidState('Cannot cancel invite', 'Invite'))
    })
  })
})
