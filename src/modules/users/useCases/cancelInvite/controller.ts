import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { InviteDTO } from '../../dto/inviteDTO'
import { CancelInvite } from './cancelInvite'
import { CancelInviteDTO } from './cancelInvite.dto'

const schema = object({
  params: object({
    inviteId: string().uuid().required(),
  }).required(),
})

class CancelInviteController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  CancelInviteDTO,
  InviteDTO
> {
  public constructor(useCase: CancelInvite) {
    super(
      'patch',
      ':inviteId/cancel',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): CancelInviteDTO {
    return { id: request.params.inviteId }
  }
}

export { CancelInviteController }
