import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { InviteDTO } from '../../dto/inviteDTO'
import { InviteMapper } from '../../mappers'
import { GenericInviteRepository } from '../../repositories/interfaces/genericInviteRepository'
import { CancelInviteDTO } from './cancelInvite.dto'

class CancelInvite extends UseCase<CancelInviteDTO, InviteDTO> {
  constructor(private readonly _inviteRepository: GenericInviteRepository) {
    super(CancelInvite.name)
  }

  private checkPermissions(collectionId?: UniqueEntityID) {
    const user = AuthContext.getUser()

    if (
      UserUtils.hasRole(user, 'PLATFORM_MANAGER') ||
      (collectionId &&
        UserUtils.hasRole(user, 'COLLECTION_MANAGER', collectionId))
    )
      return

    throw new NotAllowed(CancelInvite.name)
  }

  override async execute(
    request: Readonly<CancelInviteDTO>,
  ): Promise<InviteDTO> {
    const inviteId = new UniqueEntityID(request.id)
    const invite = await this._inviteRepository.getById(inviteId)

    this.checkPermissions(invite.collectionId)

    invite.cancel()

    await this._inviteRepository.save(invite)

    return InviteMapper.getInstance().toDTO(invite)
  }
}

export { CancelInvite }
