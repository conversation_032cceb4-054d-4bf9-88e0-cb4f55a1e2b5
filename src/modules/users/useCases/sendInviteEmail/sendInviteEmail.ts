import { logger } from '../../../../shared/infra/logger'
import { UseCase } from '../../../../shared/infra/useCase'
import { SendInviteEmailDTO } from './sendInviteEmail.dto'

export class SendInviteEmail extends UseCase<SendInviteEmailDTO, void> {
  constructor() {
    super('SendInviteEmail')
  }

  public async execute(request: SendInviteEmailDTO): Promise<void> {
    logger.info('This would handle sending of the emails.', {
      inviteId: request.id,
    })
  }
}
