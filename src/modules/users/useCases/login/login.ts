import { NotFound } from '../../../../shared/errors/useCaseErrors'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericUserRepository } from '../../repositories/interfaces/genericUserRepository'
import { usersServices } from '../../services'
import { LoginDTO } from './login.dto'

class Login extends UseCase<LoginDTO, string> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(Login.name)
  }

  async execute(request: Readonly<LoginDTO>): Promise<string> {
    const authService = usersServices.get('authorization')
    const user = await this._userRepository.findByEmail(request.email)

    if (!user) {
      throw new NotFound(Login.name)
    }

    const token = await authService.generateToken(user!)

    return token
  }
}

export { Login }
