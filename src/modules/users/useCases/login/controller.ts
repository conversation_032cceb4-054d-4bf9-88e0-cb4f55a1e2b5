import { constants } from 'http2'
import { object, string, InferType } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { Login } from './login'
import { LoginDTO } from './login.dto'

const schema = object({
  params: object(),
  body: object({
    email: string().email().required(),
  }).required(),
})

class LoginController extends Controller<
  InferType<typeof schema>['params'],
  InferType<typeof schema>['body'],
  undefined,
  LoginDTO,
  string
> {
  public constructor(useCase: Login) {
    super('post', 'login', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): LoginDTO {
    return { email: request.body.email }
  }
}

export { LoginController }
