import { database } from '../../../../../shared/database'
import { collectionsTable } from '../../../../../shared/database/schema/collections'
import { domainEvents } from '../../../../../shared/domain/events'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import {
  Collection,
  CollectionProps,
} from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { InviteCreatedEvent } from '../../../domain/events/InviteCreatedEvent'
import { User } from '../../../domain/user'
import { CollectionInviteDTO as FullCollectionInviteDTO } from '../../../dto/inviteDTO'
import { inviteRepository } from '../../../repositories'
import { Role, CollectionRole } from '../../../types/user'
import { CreateInvite } from '../createInvite'
import {
  CreateCollectionInviteDTO,
  CreateInviteInputDTO,
  CreatePlatformInviteDTO,
} from '../createInvite.dto'
import { MissingCollectionId } from '../createInvite.errors'

describe('CreateInvite Use Case', () => {
  const dbClient = database
  let createInviteUseCase: CreateInvite
  let user: User
  let testCollection: Collection

  beforeAll(async () => {
    await setupTestDatabase()
    createInviteUseCase = new CreateInvite(
      inviteRepository,
      collectionRepository,
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('Create Invite Use Case', () => {
    let dispatchSpy: jest.SpyInstance
    let markedEntitiesSpy: jest.SpyInstance

    beforeEach(async () => {
      await truncateTestDatabase()

      jest.restoreAllMocks()
      domainEvents.clearMarkedEntities()
      dispatchSpy = jest.spyOn(domainEvents, 'dispatchEventsForEntity')
      markedEntitiesSpy = jest.spyOn(domainEvents, 'markEntityForDispatch')

      const collectionProps: CollectionProps = {
        name: 'Test Collection for Invites',
        services: new Services([]),
        fieldGroups: new FieldGroups([]),
      }
      testCollection = stubCollection(collectionProps)

      user = stubUser()
      user.userAssignments.push(
        stubUserAssignment({
          userId: user.id,
          role: 'PLATFORM_MANAGER',
        }),
      )

      const collectionToSave = {
        id: testCollection.id.value,
        name: testCollection.name,
        createdAt: testCollection.createdAt,
        updatedAt: testCollection.updatedAt ?? new Date(),
        deleted: testCollection.deleted,
      }
      await dbClient.insert(collectionsTable).values(collectionToSave)
    })

    it('should create a PENDING invite for a collection role and return invite DTO', async () => {
      const input: CreateCollectionInviteDTO = {
        name: 'Test User Collab',
        email: '<EMAIL>',
        role: 'EDITOR' as CollectionRole,
        collectionId: testCollection.id.value,
      }

      const resultDTO = await execute(createInviteUseCase, input, user)

      expect(dispatchSpy).toHaveBeenCalledTimes(1)
      expect(dispatchSpy).toHaveBeenCalledWith(new UniqueEntityID(resultDTO.id))
      expect(markedEntitiesSpy).toHaveBeenCalledTimes(1)
      expect(markedEntitiesSpy).toHaveBeenCalledWith(
        expect.any(InviteCreatedEvent),
      )

      expect(resultDTO).toBeDefined()
      expect(resultDTO.name).toBe(input.name)
      expect(resultDTO.email).toBe(input.email)
      expect(resultDTO.role).toBe('EDITOR')
      expect(resultDTO.status).toBe('PENDING')
      expect((resultDTO as FullCollectionInviteDTO).collectionId).toBe(
        testCollection.id.value,
      )

      const savedInvite = await inviteRepository.getById(
        new UniqueEntityID(resultDTO.id),
      )
      expect(savedInvite).toBeDefined()
      if (savedInvite) {
        expect(savedInvite.name).toBe(input.name)
        expect(savedInvite.email.value).toBe(input.email)
        expect(savedInvite.role).toBe('EDITOR')
        expect(savedInvite.status).toBe('PENDING')
        expect(savedInvite.collectionId?.equals(testCollection.id)).toBe(true)
      }
    })

    it('should create a PENDING invite for a platform role (PLATFORM_MANAGER) and return invite DTO', async () => {
      const input: CreatePlatformInviteDTO = {
        name: 'Test Platform Manager',
        email: '<EMAIL>',
        role: 'PLATFORM_MANAGER',
      }

      const resultDTO = await execute(createInviteUseCase, input, user)

      expect(dispatchSpy).toHaveBeenCalledTimes(1)
      expect(dispatchSpy).toHaveBeenCalledWith(new UniqueEntityID(resultDTO.id))
      expect(markedEntitiesSpy).toHaveBeenCalledTimes(1)
      expect(markedEntitiesSpy).toHaveBeenCalledWith(
        expect.any(InviteCreatedEvent),
      )

      expect(resultDTO).toBeDefined()
      expect(resultDTO.name).toBe(input.name)
      expect(resultDTO.email).toBe(input.email)
      expect(resultDTO.role).toBe('PLATFORM_MANAGER')
      expect(resultDTO.status).toBe('PENDING')

      const savedInvite = await inviteRepository.getById(
        new UniqueEntityID(resultDTO.id),
      )
      expect(savedInvite).toBeDefined()
      if (savedInvite) {
        expect(savedInvite.name).toBe(input.name)
        expect(savedInvite.email.value).toBe(input.email)
        expect(savedInvite.role).toBe('PLATFORM_MANAGER')
        expect(savedInvite.status).toBe('PENDING')
        expect(savedInvite.collectionId).toBeUndefined()
      }
    })

    it('should throw EntityNotFound if collectionId is provided for a collection role but collection does not exist', async () => {
      const nonExistentCollectionId = new UniqueEntityID().value
      const input: CreateCollectionInviteDTO = {
        name: 'Test User Invalid Collection',
        email: '<EMAIL>',
        role: 'VIEWER' as CollectionRole,
        collectionId: nonExistentCollectionId,
      }

      await expect(execute(createInviteUseCase, input, user)).rejects.toThrow(
        new EntityNotFound('Collection', nonExistentCollectionId),
      )
      expect(dispatchSpy).not.toHaveBeenCalled()
    })

    it('should throw an error if a non-PLATFORM_MANAGER role is provided without a collectionId', async () => {
      const input: CreateInviteInputDTO = {
        name: 'Test User No CollectionId',
        email: '<EMAIL>',
        role: 'EDITOR' as Role,
      } as CreateInviteInputDTO

      await expect(execute(createInviteUseCase, input, user)).rejects.toThrow(
        MissingCollectionId,
      )
      expect(dispatchSpy).not.toHaveBeenCalled()
    })
  })
})
