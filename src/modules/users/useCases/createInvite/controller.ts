import { constants } from 'http2'
import { InferType, lazy, mixed, object, string } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { InviteDTO } from '../../dto/inviteDTO'
import { CollectionRole, Role } from '../../types/user'
import { CreateInvite } from './createInvite'
import { CreateInviteInputDTO } from './createInvite.dto'

const platformInviteBodySchema = object({
  name: string().required(),
  email: string().email().required(),
  role: mixed<'PLATFORM_MANAGER'>().oneOf(['PLATFORM_MANAGER']).defined(),
}).noUnknown(true)

const collectionInviteBodySchema = object({
  name: string().required(),
  email: string().email().required(),
  role: mixed<CollectionRole>()
    .oneOf(['COLLECTION_MANAGER', 'EDITOR', 'CONTRIBUTOR', 'VIEWER'])
    .defined(),
  collectionId: string().uuid().required(),
}).noUnknown(true)

type CreateInviteParams = Record<string, never>

const createInviteSchema = object({
  params: object()
    .default({} as CreateInviteParams)
    .defined(),
  body: lazy((value: unknown) => {
    const role = (value as CreateInviteInputDTO | undefined)?.role as
      | Role
      | undefined
    if (role === 'PLATFORM_MANAGER') {
      return platformInviteBodySchema.defined('Request body is required.')
    }
    return collectionInviteBodySchema.defined('Request body is required.')
  }),
}).defined()

class CreateInviteController extends Controller<
  CreateInviteParams,
  InferType<typeof createInviteSchema>['body'],
  undefined,
  CreateInviteInputDTO,
  InviteDTO
> {
  public constructor(useCase: CreateInvite) {
    super(
      'post',
      '',
      constants.HTTP_STATUS_CREATED,
      useCase,
      createInviteSchema,
    )
  }

  protected parseInput(
    request: InferType<typeof createInviteSchema>,
  ): CreateInviteInputDTO {
    return request.body!
  }
}

export { CreateInviteController }
