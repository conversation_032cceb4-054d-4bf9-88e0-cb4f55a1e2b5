import { Email } from '../../../../shared/domain/email'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { GenericCollectionRepository } from '../../../collections/repositories/interfaces/genericCollectionRepository'
import { Invite } from '../../domain/invite'
import { User } from '../../domain/user'
import { InviteDTO } from '../../dto/inviteDTO'
import { InviteMapper } from '../../mappers'
import { GenericInviteRepository } from '../../repositories/interfaces/genericInviteRepository'
import { CreateInviteInputDTO } from './createInvite.dto'
import { MissingCollectionId } from './createInvite.errors'

class CreateInvite extends UseCase<CreateInviteInputDTO, InviteDTO> {
  constructor(
    private readonly _inviteRepository: GenericInviteRepository,
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super(CreateInvite.name)
  }
  override async execute(request: CreateInviteInputDTO): Promise<InviteDTO> {
    const invite = await this.generateInvite(request)

    await this._inviteRepository.save(invite)

    return InviteMapper.getInstance().toDTO(invite)
  }

  private async generateInvite(request: CreateInviteInputDTO) {
    const currentUser = AuthContext.getUser()

    if (request.role === 'PLATFORM_MANAGER') {
      this.checkPermissions(currentUser)

      return new Invite({
        name: request.name,
        email: new Email({ value: request.email }),
        role: 'PLATFORM_MANAGER',
        status: 'PENDING',
      })
    }

    if (!request.collectionId) {
      throw new MissingCollectionId()
    }

    const collection = await this._collectionRepository.getById(
      new UniqueEntityID(request.collectionId),
    )
    this.checkPermissions(currentUser, collection.id)

    return new Invite({
      name: request.name,
      email: new Email({ value: request.email }),
      role: request.role,
      status: 'PENDING',
      collectionId: collection.id,
    })
  }

  async checkPermissions(
    user: User,
    collectionId?: UniqueEntityID,
  ): Promise<void> {
    const hasPermission =
      UserUtils.hasRole(user, 'PLATFORM_MANAGER') ||
      (collectionId &&
        UserUtils.hasRole(user, 'COLLECTION_MANAGER', collectionId))

    if (!hasPermission) {
      throw new NotAllowed(CreateInvite.name)
    }
  }
}

export { CreateInvite }
