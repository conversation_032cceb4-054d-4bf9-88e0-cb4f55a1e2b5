import { StrictOmit } from 'ts-essentials'
import { CreateDTO } from '../../../../shared/dto/createDTO'
import { CollectionInviteDTO, PlatformInviteDTO } from '../../dto/inviteDTO'

type CreatePlatformInviteDTO = CreateDTO<
  StrictOmit<PlatformInviteDTO, 'status'>
>
type CreateCollectionInviteDTO = CreateDTO<
  StrictOmit<CollectionInviteDTO, 'status'>
>

type CreateInviteInputDTO = CreatePlatformInviteDTO | CreateCollectionInviteDTO

export type {
  CreateInviteInputDTO,
  CreatePlatformInviteDTO,
  CreateCollectionInviteDTO,
}
