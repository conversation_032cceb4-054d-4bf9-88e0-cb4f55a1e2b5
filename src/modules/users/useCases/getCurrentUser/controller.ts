import { constants } from 'http2'
import { object, InferType } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { UserDTO } from '../../dto/userDTO'
import { GetCurrentUser } from './getCurrentUser'

const schema = object({ params: object() })

class GetCurrentUserController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  undefined,
  UserDTO
> {
  public constructor(useCase: GetCurrentUser) {
    super('get', 'me', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(): undefined {
    return
  }
}

export { GetCurrentUserController }
