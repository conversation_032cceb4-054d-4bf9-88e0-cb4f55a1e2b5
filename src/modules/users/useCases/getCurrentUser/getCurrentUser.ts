import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserDTO } from '../../dto/userDTO'
import { UserMapper } from '../../mappers'
import { GenericUserRepository } from '../../repositories/interfaces/genericUserRepository'

class GetCurrentUser extends UseCase<void, UserDTO> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(GetCurrentUser.name)
  }

  async execute(): Promise<UserDTO> {
    const user = AuthContext.getUser()

    return UserMapper.getInstance().toDTO(user)
  }
}

export { GetCurrentUser }
