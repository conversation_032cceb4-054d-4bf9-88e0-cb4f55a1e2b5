import { constants } from 'http2'
import { object, string, InferType } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { UserDTO } from '../../dto/userDTO'
import { GetUser } from './getUser'
import { GetUserDTO } from './getUser.dto'

const schema = object({
  params: object({
    userId: string().uuid().required(),
  }).required(),
})

class GetUserController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  GetUserDTO,
  UserDTO
> {
  public constructor(useCase: GetUser) {
    super('get', ':userId', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetUserDTO {
    return { id: request.params.userId }
  }
}

export { GetUserController }
