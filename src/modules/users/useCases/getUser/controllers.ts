import { constants } from 'http2'
import { object, string, InferType } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { UserDTO } from '../../dto/userDTO'
import { GetUser } from './getUser'
import { GetUserDTO } from './getUser.dto'

const getUserSchema = object({
  params: object({
    userId: string().uuid().required(),
  }).required(),
})

class GetUserController extends Controller<
  InferType<typeof getUserSchema>['params'],
  undefined,
  undefined,
  GetUserDTO,
  UserDTO
> {
  public constructor(useCase: GetUser) {
    super('get', ':userId', constants.HTTP_STATUS_OK, useCase, getUserSchema)
  }

  protected parseInput(request: InferType<typeof getUserSchema>): GetUserDTO {
    return { id: request.params.userId }
  }
}

const getCurrentUserSchema = object({ params: object() })

class GetCurrentUser<PERSON>ontroller extends Controller<
  InferType<typeof getCurrentUserSchema>['params'],
  undefined,
  undefined,
  GetUserDTO,
  UserDTO
> {
  public constructor(useCase: GetUser) {
    super('get', 'me', constants.HTTP_STATUS_OK, useCase, getCurrentUserSchema)
  }

  protected parseInput(): GetUserDTO {
    return {
      id: '00000000-0000-0000-0000-000000000000', // TODO: need a way to pass the current user
    }
  }
}

export { GetUserController, GetCurrentUserController }
