import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { UserDTO } from '../../dto/userDTO'
import { UserMapper } from '../../mappers'
import { GenericUserRepository } from '../../repositories/interfaces/genericUserRepository'
import { GetUserDTO } from './getUser.dto'

class GetUser extends UseCase<GetUserDTO, UserDTO> {
  constructor(private readonly _userRepository: GenericUserRepository) {
    super(GetUser.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(GetUser.name)
  }

  async execute(request: Readonly<GetUserDTO>): Promise<UserDTO> {
    this.checkPermissions()

    const id = new UniqueEntityID(request.id)

    const user = await this._userRepository.getById(id)

    return UserMapper.getInstance().toDTO(user)
  }
}

export { GetUser }
