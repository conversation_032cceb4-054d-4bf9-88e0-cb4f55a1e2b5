import { database } from '../../../../../shared/database'
import { collectionsTable } from '../../../../../shared/database/schema/collections'
import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { Collection } from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import {
  stubUser,
  stubUserWithAssignments,
} from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { User } from '../../../domain/user'
import { UserDTO } from '../../../dto/userDTO'
import { userRepository } from '../../../repositories'
import { GetUser } from '../getUser'
import { GetUserDTO } from '../getUser.dto'

describe('GetUser Use Case', () => {
  const dbClient = database
  const getUserUseCase = new GetUser(userRepository)
  let user: User
  let testCollection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    testCollection = stubCollection({
      name: 'User Test Collection',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    })
    const collectionToSave = {
      id: testCollection.id.value,
      name: testCollection.name,
      createdAt: testCollection.createdAt,
      updatedAt: testCollection.updatedAt ?? new Date(),
      deleted: testCollection.deleted,
    }
    await dbClient.insert(collectionsTable).values(collectionToSave)
  })

  it('should return user DTO if user exists (with assignments)', async () => {
    const existingUser = stubUserWithAssignments(
      {
        name: 'Test User With Roles',
        email: stubEmail({ value: '<EMAIL>' }),
      },
      testCollection.id,
    )

    await userRepository.save(existingUser)

    const getUserDTO: GetUserDTO = {
      id: existingUser.id.value,
    }

    const resultDTO: UserDTO = await execute(getUserUseCase, getUserDTO, user)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(existingUser.id.value)
    expect(resultDTO.name).toBe(existingUser.name)
    expect(resultDTO.email).toBe(existingUser.email.value)
    expect(resultDTO.settings?.emailNotifications).toBe(
      existingUser.settings.emailNotifications,
    )
    expect(resultDTO.assignments).toBeDefined()
    expect(resultDTO.assignments.length).toBe(
      existingUser.userAssignments.length,
    )
    const platformAssignmentDTO = resultDTO.assignments.find(
      (a) => a.role === 'PLATFORM_MANAGER',
    )
    expect(platformAssignmentDTO).toBeDefined()
    expect(platformAssignmentDTO?.collectionId).toBeUndefined()

    const editorAssignmentDTO = resultDTO.assignments.find(
      (a) => a.role === 'EDITOR',
    )
    expect(editorAssignmentDTO).toBeDefined()
    expect(editorAssignmentDTO?.collectionId).toBe(testCollection.id.value)
  })

  it('should return user DTO if user exists (no assignments)', async () => {
    const simpleUser = stubUser({
      name: 'Simple User',
      email: stubEmail({ value: '<EMAIL>' }),
      userAssignments: [], // Ensure no assignments for this test
    })
    await userRepository.save(simpleUser)

    const getUserDTO: GetUserDTO = {
      id: simpleUser.id.value,
    }

    const resultDTO: UserDTO = await execute(getUserUseCase, getUserDTO, user)

    expect(resultDTO).toBeDefined()
    expect(resultDTO.id).toBe(simpleUser.id.value)
    expect(resultDTO.name).toBe(simpleUser.name)
    expect(resultDTO.email).toBe(simpleUser.email.value)
    expect(resultDTO.assignments.length).toBe(0)
  })

  it('should throw EntityNotFound if user does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    const getUserDTO: GetUserDTO = {
      id: nonExistentId,
    }

    await expect(execute(getUserUseCase, getUserDTO, user)).rejects.toThrow(
      new EntityNotFound('User', nonExistentId),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })
})
