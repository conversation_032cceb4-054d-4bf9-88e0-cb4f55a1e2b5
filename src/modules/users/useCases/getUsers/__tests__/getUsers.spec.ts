import { database } from '../../../../../shared/database'
import { collectionsTable } from '../../../../../shared/database/schema/collections'
import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { Collection } from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import {
  stubUser,
  stubUserWithAssignments,
} from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { User } from '../../../domain/user'
import { userRepository } from '../../../repositories'
import { GetUsers } from '../getUsers'

describe('GetUsers Use Case', () => {
  const dbClient = database
  const getUsersUseCase = new GetUsers(userRepository)
  let user: User
  let testCollection: Collection
  let anotherTestCollection: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()
    testCollection = stubCollection({
      name: 'GetUsers Test Collection 1',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    })
    anotherTestCollection = stubCollection({
      name: 'GetUsers Test Collection 2',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    })

    await dbClient.insert(collectionsTable).values([
      {
        id: testCollection.id.value,
        name: testCollection.name,
        createdAt: testCollection.createdAt,
        updatedAt: testCollection.updatedAt ?? new Date(),
        deleted: testCollection.deleted,
      },
      {
        id: anotherTestCollection.id.value,
        name: anotherTestCollection.name,
        createdAt: anotherTestCollection.createdAt,
        updatedAt: anotherTestCollection.updatedAt ?? new Date(),
        deleted: anotherTestCollection.deleted,
      },
    ])
  })

  it('should return an empty array if no users exist', async () => {
    const { items, totalItems } = await execute(
      getUsersUseCase,
      undefined,
      user,
    )

    expect(items).toBeDefined()
    expect(items.length).toBe(0)
    expect(totalItems).toBe(0)
  })

  it('should return all users with their assignments correctly mapped', async () => {
    const user1 = stubUser({
      name: 'Simple User',
      email: stubEmail({ value: '<EMAIL>' }),
      userAssignments: [],
    })

    const user2 = stubUserWithAssignments(
      {
        name: 'User With Roles 1',
        email: stubEmail({ value: '<EMAIL>' }),
      },
      testCollection.id, // PLATFORM_MANAGER and EDITOR for testCollection
    )

    const user3 = stubUser({
      name: 'User With Roles 2',
      email: stubEmail({ value: '<EMAIL>' }),
      userAssignments: [],
    })

    const user3Assignment = stubUserAssignment({
      userId: user3.id,
      role: 'VIEWER',
      collectionId: anotherTestCollection.id,
    })

    user3.userAssignments.push(user3Assignment)

    await userRepository.saveMany([user1, user2, user3])

    const { items } = await execute(getUsersUseCase, undefined, user)

    expect(items).toBeDefined()
    expect(items.length).toBe(3)

    const dto1 = items.find((u) => u.id === user1.id.value)
    expect(dto1).toBeDefined()
    expect(dto1?.name).toBe(user1.name)
    expect(dto1?.email).toBe(user1.email.value)
    expect(dto1?.assignments.length).toBe(0)

    const dto2 = items.find((u) => u.id === user2.id.value)
    expect(dto2).toBeDefined()
    expect(dto2?.name).toBe(user2.name)
    expect(dto2?.email).toBe(user2.email.value)

    expect(dto2?.assignments.length).toBe(user2.userAssignments.length) // Should be 2

    const platformAssignmentUser2 = dto2?.assignments.find(
      (a) => a.role === 'PLATFORM_MANAGER',
    )
    expect(platformAssignmentUser2).toBeDefined()
    expect(platformAssignmentUser2?.collectionId).toBeUndefined()

    const editorAssignmentUser2 = dto2?.assignments.find(
      (a) => a.role === 'EDITOR',
    )
    expect(editorAssignmentUser2).toBeDefined()
    expect(editorAssignmentUser2?.collectionId).toBe(testCollection.id.value)

    const dto3 = items.find((u) => u.id === user3.id.value)
    expect(dto3).toBeDefined()
    expect(dto3?.name).toBe(user3.name)
    expect(dto3?.email).toBe(user3.email.value)
    expect(dto3?.assignments.length).toBe(1)

    const viewerAssignmentUser3 = dto3?.assignments.find(
      (a) => a.role === 'VIEWER',
    )
    expect(viewerAssignmentUser3).toBeDefined()
    expect(viewerAssignmentUser3?.collectionId).toBe(
      anotherTestCollection.id.value,
    )
  })
})
