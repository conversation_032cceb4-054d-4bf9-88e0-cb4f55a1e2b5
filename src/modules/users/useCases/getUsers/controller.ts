import { constants } from 'http2'
import { object, InferType } from 'yup'
import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { QueryDTO } from '../../../../shared/dto/queryDTO'
import { Controller } from '../../../../shared/infra/controller'
import { QuerySchema } from '../../../../shared/types/query'
import { UserDTO } from '../../dto/userDTO'
import { GetUsers } from './getUsers'
import { GetUsersDTO } from './getUsers.dto'

const schema = object({
  params: object(),
  query: QuerySchema,
})

class GetUsersController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryDTO,
  GetUsersDTO,
  PaginatedResponseDTO<UserDTO>
> {
  protected override parseInput(request: InferType<typeof schema>): QueryDTO {
    return QuerySchema.validateSync(request.query)
  }
  public constructor(useCase: GetUsers) {
    super('get', '', constants.HTTP_STATUS_OK, useCase, schema)
  }
}

export { GetUsersController }
