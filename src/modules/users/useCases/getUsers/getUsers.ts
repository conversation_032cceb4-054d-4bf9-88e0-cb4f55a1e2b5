import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { UserDTO } from '../../dto/userDTO'
import { UserMapper } from '../../mappers'
import { GenericUserRepository } from '../../repositories/interfaces/genericUserRepository'
import { GetUsersDTO } from './getUsers.dto'

class GetUsers extends UseCase<GetUsersDTO, PaginatedResponseDTO<UserDTO>> {
  private checkPermissions() {
    const currentUser = AuthContext.getUser()

    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(GetUsers.name)
  }

  override async execute(
    request: Readonly<GetUsersDTO>,
  ): Promise<PaginatedResponseDTO<UserDTO>> {
    this.checkPermissions()

    const { items, total } = await this._userRepository.getAll(request)

    return {
      items: items.map(UserMapper.getInstance().toDTO),
      totalItems: total,
    }
  }

  constructor(private readonly _userRepository: GenericUserRepository) {
    super(GetUsers.name)
  }
}

export { GetUsers }
