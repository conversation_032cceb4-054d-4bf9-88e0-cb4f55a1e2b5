import { constants } from 'http2'
import { InferType, object } from 'yup'
import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { Controller } from '../../../../shared/infra/controller'
import { QueryParams, QuerySchema } from '../../../../shared/types/query'
import { InviteDTO } from '../../dto/inviteDTO'
import { GetInvites } from './getInvites'
import { GetInvitesDTO } from './getInvites.dto'

const schema = object({ params: object(), query: QuerySchema })

class GetInvitesController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryParams,
  GetInvitesDTO,
  PaginatedResponseDTO<InviteDTO>
> {
  public constructor(useCase: GetInvites) {
    super('get', '', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected override parseInput(
    request: InferType<typeof schema>,
  ): QueryParams {
    return QuerySchema.validateSync(request.query)
  }
}

export { GetInvitesController }
