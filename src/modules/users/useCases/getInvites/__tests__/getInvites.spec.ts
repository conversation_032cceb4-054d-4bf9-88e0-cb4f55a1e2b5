import { database } from '../../../../../shared/database'
import { collectionsTable } from '../../../../../shared/database/schema/collections'
import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import {
  Collection,
  CollectionProps,
} from '../../../../collections/domain/collection'
import { FieldGroups } from '../../../../collections/domain/fieldGroups'
import { Services } from '../../../../collections/domain/services'
import { stubInvite } from '../../../domain/__stubs__/invite.stub'
import { stubUser } from '../../../domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../domain/__stubs__/userAssignment.stub'
import { User } from '../../../domain/user'
import { PlatformInviteDTO, CollectionInviteDTO } from '../../../dto/inviteDTO' // Import DTO types
import { inviteRepository } from '../../../repositories'
import { InviteStatus } from '../../../types/invite'
import { CollectionRole, PlatformRole } from '../../../types/user'
import { GetInvites } from '../getInvites'

describe('GetInvites Use Case', () => {
  const dbClient = database
  const getInvitesUseCase = new GetInvites(inviteRepository)
  let user: User

  let testCollection1: Collection
  let testCollection2: Collection

  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    const collection1Props: CollectionProps = {
      name: 'Collection One',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    }
    testCollection1 = stubCollection(collection1Props)
    const collection1ToSave = {
      id: testCollection1.id.value,
      name: testCollection1.name,
      createdAt: testCollection1.createdAt,
      updatedAt: testCollection1.updatedAt ?? new Date(),
      deleted: testCollection1.deleted,
    }
    await dbClient.insert(collectionsTable).values(collection1ToSave)

    const collection2Props: CollectionProps = {
      name: 'Collection Two',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    }
    testCollection2 = stubCollection(collection2Props)
    const collection2ToSave = {
      id: testCollection2.id.value,
      name: testCollection2.name,
      createdAt: testCollection2.createdAt,
      updatedAt: testCollection2.updatedAt ?? new Date(),
      deleted: testCollection2.deleted,
    }
    await dbClient.insert(collectionsTable).values(collection2ToSave)
  })

  it('should return an empty array if no invites exist', async () => {
    const resultDTOs = await execute(getInvitesUseCase, {}, user)
    expect(resultDTOs.items).toBeInstanceOf(Array)
    expect(resultDTOs.totalItems).toBe(0)
  })

  it('should return all existing invites as DTOs', async () => {
    // Create some invites
    const invite1 = stubInvite({
      name: 'Platform Manager Invite',
      email: stubEmail({ value: '<EMAIL>' }),
      role: 'PLATFORM_MANAGER' as PlatformRole,
      status: 'PENDING' as InviteStatus,
      collectionId: undefined,
    })
    const invite2 = stubInvite({
      name: 'Editor Invite C1',
      email: stubEmail({ value: '<EMAIL>' }),
      role: 'EDITOR' as CollectionRole,
      collectionId: testCollection1.id,
      status: 'CANCELLED' as InviteStatus,
    })
    const invite3 = stubInvite({
      name: 'Viewer Invite C2',
      email: stubEmail({ value: '<EMAIL>' }),
      role: 'VIEWER' as CollectionRole,
      collectionId: testCollection2.id,
      status: 'REGISTERED' as InviteStatus,
    })

    await inviteRepository.saveMany([invite1, invite2, invite3])

    const resultDTOs = await execute(getInvitesUseCase, {}, user)

    expect(resultDTOs.items).toBeInstanceOf(Array)
    expect(resultDTOs.totalItems).toBe(3)

    // Check details of each invite DTO
    const dto1 = resultDTOs.items.find((dto) => dto.id === invite1.id.value) as
      | PlatformInviteDTO
      | undefined
    expect(dto1).toBeDefined()
    if (dto1) {
      expect(dto1.name).toBe(invite1.name)
      expect(dto1.email).toBe(invite1.email.value)
      expect(dto1.role).toBe('PLATFORM_MANAGER')
      expect(dto1.status).toBe('PENDING')
      expect('collectionId' in dto1).toBe(false)
    }

    const dto2 = resultDTOs.items.find((dto) => dto.id === invite2.id.value) as
      | CollectionInviteDTO
      | undefined
    expect(dto2).toBeDefined()
    if (dto2 && 'collectionId' in dto2) {
      expect(dto2.name).toBe(invite2.name)
      expect(dto2.email).toBe(invite2.email.value)
      expect(dto2.role).toBe('EDITOR')
      expect(dto2.status).toBe('CANCELLED')
      expect(dto2.collectionId).toBe(testCollection1.id.value)
    } else if (dto2) {
      fail('dto2 should be a CollectionInvite and have a collectionId')
    }

    const dto3 = resultDTOs.items.find((dto) => dto.id === invite3.id.value) as
      | CollectionInviteDTO
      | undefined
    expect(dto3).toBeDefined()
    if (dto3 && 'collectionId' in dto3) {
      expect(dto3.name).toBe(invite3.name)
      expect(dto3.email).toBe(invite3.email.value)
      expect(dto3.role).toBe('VIEWER')
      expect(dto3.status).toBe('REGISTERED')
      expect(dto3.collectionId).toBe(testCollection2.id.value)
    } else if (dto3) {
      fail('dto3 should be a CollectionInvite and have a collectionId')
    }
  })
})
