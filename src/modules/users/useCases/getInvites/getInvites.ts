import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { InviteDTO } from '../../dto/inviteDTO'
import { InviteMapper } from '../../mappers/inviteMapper'
import { GenericInviteRepository } from '../../repositories/interfaces/genericInviteRepository'

import { GetInvitesDTO } from './getInvites.dto'

class GetInvites extends UseCase<
  GetInvitesDTO,
  PaginatedResponseDTO<InviteDTO>
> {
  constructor(private readonly _inviteRepository: GenericInviteRepository) {
    super(GetInvites.name)
  }

  private checkPermissions() {
    const currentUser = AuthContext.getUser()
    if (UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER')) return

    throw new NotAllowed(GetInvites.name)
  }

  override async execute(
    request: GetInvitesDTO,
  ): Promise<PaginatedResponseDTO<InviteDTO>> {
    this.checkPermissions()

    const result = await this._inviteRepository.getAll(request)

    return InviteMapper.getInstance().toPageDTO(result)
  }
}

export { GetInvites }
