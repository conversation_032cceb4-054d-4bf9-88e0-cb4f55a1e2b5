import { eq } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { invitesTable } from '../../../shared/database/schema/users'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Invite as Domain } from '../domain/invite'
import { InviteDTO as DTO } from '../dto/inviteDTO'
import { InviteMapper } from '../mappers/inviteMapper'
import { InviteModel as Model } from '../models/invite.model'
import { GenericInviteRepository } from './interfaces/genericInviteRepository'

class InviteRepository
  extends AbstractDrizzleRepository<typeof invitesTable, Domain, Model, DTO>
  implements GenericInviteRepository
{
  constructor(db: DatabaseClient) {
    super(
      'Invite',
      db,
      InviteMapper.getInstance(),
      invitesTable,
      'invitesTable',
    )
  }

  async getAll(options?: BaseFindQuery): Promise<PaginatedDomain<Domain>> {
    const queryOptions = this.buildQueryOptions({
      includeAll: false,
      ...options,
    })

    const invites = await this.db.query.invitesTable.findMany(queryOptions)

    return {
      items: invites.map((invite) => this.mapper.toDomain(invite)),
      total: invites.length,
    }
  }

  async getAllByCollectionId(collectionId: UniqueEntityID): Promise<Domain[]> {
    const invites = await this.db.query.invitesTable.findMany({
      where: eq(invitesTable.collectionId, collectionId.value),
    })
    return invites.map((invite) => this.mapper.toDomain(invite))
  }
}

export { InviteRepository }
