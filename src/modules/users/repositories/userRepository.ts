import { eq, inArray } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { rolesTable, usersTable } from '../../../shared/database/schema/users'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { User as Domain } from '../domain/user'
import { UserAssignment } from '../domain/userAssignment'
import { UserDTO as DTO } from '../dto/userDTO'
import { UserMapper } from '../mappers/userMapper'
import { RoleModel } from '../models/role.model'
import { UserModel as Model } from '../models/user.model'
import { Role } from '../types/user'
import { GenericUserRepository } from './interfaces/genericUserRepository'

class UserRepository
  extends AbstractDrizzleRepository<typeof usersTable, Domain, Model, DTO>
  implements GenericUserRepository
{
  private static readonly PLATFORM_ROLE_KEY = '__platform_role__'

  constructor(db: DatabaseClient) {
    super('User', db, UserMapper.getInstance(), usersTable, 'usersTable')
  }

  // Overridden methods

  override async getById(id: UniqueEntityID): Promise<Domain> {
    const effectiveOptions = {
      with: {
        assignments: true,
      },
    }
    return super.getById(id, effectiveOptions)
  }

  protected override async processRelatedEntities(
    domain: Domain,
    dbClient: DatabaseClient,
  ): Promise<void> {
    const userId = domain.id.value
    const desiredAssignments = domain.userAssignments || []

    const existingAssignmentsMap = await this._getExistingAssignmentsMap(
      userId,
      dbClient,
    )

    const { assignmentsToInsert, assignmentsToUpdate, idsToDelete } =
      this._classifyRoleChanges(
        desiredAssignments,
        existingAssignmentsMap,
        userId,
      )

    await this._executeRoleDbOperations(
      assignmentsToInsert,
      assignmentsToUpdate,
      idsToDelete,
      dbClient,
    )
  }

  // Private methods

  private async _getExistingAssignmentsMap(
    userId: string,
    dbClient: DatabaseClient,
  ): Promise<Map<string, RoleModel>> {
    const existingDbAssignmentsList: RoleModel[] =
      await dbClient.query.rolesTable.findMany({
        where: eq(rolesTable.userId, userId),
      })

    const existingAssignmentsMap = new Map<string, RoleModel>()
    existingDbAssignmentsList.forEach((dbAssignment) => {
      const key = dbAssignment.collectionId ?? UserRepository.PLATFORM_ROLE_KEY
      existingAssignmentsMap.set(key, dbAssignment)
    })
    return existingAssignmentsMap
  }

  private _classifyRoleChanges(
    desiredAssignments: ReadonlyArray<UserAssignment>,
    existingAssignmentsMap: Map<string, RoleModel>,
    userId: string,
  ): {
    assignmentsToInsert: RoleModel[]
    assignmentsToUpdate: Array<{ id: string; role: Role }>
    idsToDelete: string[]
  } {
    const assignmentsToInsert: RoleModel[] = []
    const assignmentsToUpdate: Array<{ id: string; role: Role }> = []

    for (const domainAssignment of desiredAssignments) {
      const key =
        domainAssignment.collectionId?.value ?? UserRepository.PLATFORM_ROLE_KEY
      const existingDbAssignment = existingAssignmentsMap.get(key)

      if (existingDbAssignment) {
        if (existingDbAssignment.role !== domainAssignment.role) {
          assignmentsToUpdate.push({
            id: existingDbAssignment.id,
            role: domainAssignment.role,
          })
        }
        existingAssignmentsMap.delete(key)
      } else {
        assignmentsToInsert.push({
          id: new UniqueEntityID().value,
          userId: userId,
          collectionId:
            domainAssignment.role === 'PLATFORM_MANAGER'
              ? null
              : (domainAssignment.collectionId?.value ?? null),
          role: domainAssignment.role,
          createdAt: new Date(),
          updatedAt: null,
          deleted: false,
        })
      }
    }

    const idsToDelete: string[] = Array.from(
      existingAssignmentsMap.values(),
    ).map((assignment) => assignment.id)

    return { assignmentsToInsert, assignmentsToUpdate, idsToDelete }
  }

  private async _executeRoleDbOperations(
    assignmentsToInsert: RoleModel[],
    assignmentsToUpdate: Array<{ id: string; role: Role }>,
    idsToDelete: string[],
    dbClient: DatabaseClient,
  ): Promise<void> {
    if (assignmentsToInsert.length > 0) {
      await dbClient.insert(rolesTable).values(assignmentsToInsert)
    }

    for (const update of assignmentsToUpdate) {
      await dbClient
        .update(rolesTable)
        .set({ role: update.role, updatedAt: new Date() })
        .where(eq(rolesTable.id, update.id))
    }

    if (idsToDelete.length > 0) {
      await dbClient
        .delete(rolesTable)
        .where(inArray(rolesTable.id, idsToDelete))
    }
  }

  // Public methods

  async findByEmail(email: string): Promise<Domain | undefined> {
    const user = await this.db.query.usersTable.findFirst({
      where: eq(usersTable.email, email),
      with: {
        assignments: true,
      },
    })

    return user ? this.mapper.toDomain(user) : undefined
  }

  async getAll(options?: BaseFindQuery): Promise<PaginatedDomain<Domain>> {
    const queryOptions = this.buildQueryOptions({
      includeAll: false,
      ...options,
      with: {
        assignments: true,
      },
    })

    const users = await this.db.query.usersTable.findMany(queryOptions)
    const total = await this.db.$count(usersTable)

    return {
      items: users.map((user) => this.mapper.toDomain(user)),
      total: total,
    }
  }

  async getAllByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Domain>> {
    const rolesResult = await this.db.query.rolesTable.findMany({
      where: eq(rolesTable.collectionId, collectionId.value),
    })

    const userIds = rolesResult.map((role) => role.userId)
    if (userIds.length === 0) return { items: [], total: 0 }

    const queryOptions = this.buildQueryOptions({
      includeAll: false,
      ...options,
    })

    const users = await this.db.query.usersTable.findMany({
      ...queryOptions,
      where: inArray(usersTable.id, userIds),
      with: {
        assignments: true,
      },
    })

    return {
      items: users.map((user) => this.mapper.toDomain(user)),
      total: users.length,
    }
  }
}

export { UserRepository }
