import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { PaginatedDomain } from '../../../../shared/types/pagination'
import { User } from '../../domain/user'
import { Role } from '../../types/user'

interface GenericUserRepository extends GenericRepository<User> {
  findByEmail(email: string): Promise<User | undefined>
  getAll(options?: BaseFindQuery): Promise<PaginatedDomain<User>>
  getAllByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<User>>
  getUsersByRoleInCollection(
    collectionId: UniqueEntityID,
    role: Role,
  ): Promise<User[]>
}

export type { GenericUserRepository }
