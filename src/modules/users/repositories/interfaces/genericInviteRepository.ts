import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { PaginatedDomain } from '../../../../shared/types/pagination'
import { Invite } from '../../domain/invite'

interface GenericInviteRepository extends GenericRepository<Invite> {
  getAll(options?: BaseFindQuery): Promise<PaginatedDomain<Invite>>
  getAllByCollectionId(collectionId: UniqueEntityID): Promise<Invite[]>
}

export type { GenericInviteRepository }
