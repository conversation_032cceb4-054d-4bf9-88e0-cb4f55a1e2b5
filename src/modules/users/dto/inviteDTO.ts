import { TEntityDTO } from '../../../shared/dto/entityDTO'
import { IdDTO } from '../../../shared/dto/idDTO'
import { InviteStatus } from '../types/invite'
import { CollectionRole, PlatformRole } from '../types/user'

type CommonInviteProps = TEntityDTO & {
  name: string
  email: string
  status: InviteStatus
}

type CollectionInviteDTO = CommonInviteProps & {
  role: CollectionRole
  collectionId?: IdDTO
}

type PlatformInviteDTO = CommonInviteProps & {
  role: PlatformRole
}

type InviteDTO = CollectionInviteDTO | PlatformInviteDTO

export type { InviteDTO, CollectionInviteDTO, PlatformInviteDTO }
