import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { InviteCreatedEvent } from '../domain/events/InviteCreatedEvent'
import { useCase } from '../useCases/sendInviteEmail/index'
import { SendInviteEmailDTO } from '../useCases/sendInviteEmail/sendInviteEmail.dto'

export class InviteCreatedHandler extends BaseDomainEventHandler<
  InviteCreatedEvent,
  SendInviteEmailDTO
> {
  constructor() {
    super(useCase, InviteCreatedEvent, InviteCreatedEvent.eventIdentifier)
  }

  protected parseInput(event: InviteCreatedEvent): SendInviteEmailDTO {
    return {
      id: event.invite.id.value,
    }
  }
}
