import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { UserAssignment, type UserAssignmentProps } from '../userAssignment'

const baseUserAssignmentProps: UserAssignmentProps = {
  userId: new UniqueEntityID(),
  collectionId: new UniqueEntityID(),
  role: 'COLLECTION_MANAGER',
}

const stubUserAssignment = (
  props?: Partial<UserAssignmentProps>,
): UserAssignment => {
  return new UserAssignment({ ...baseUserAssignmentProps, ...props })
}

export { stubUserAssignment, baseUserAssignmentProps }
