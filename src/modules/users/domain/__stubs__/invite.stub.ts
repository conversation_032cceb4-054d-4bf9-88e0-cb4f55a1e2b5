import { stubEmail } from '../../../../shared/domain/__stubs__/email.stub'
import { stubCollection } from '../../../collections/domain/__stubs__/collection.stub'
import { Invite, type InviteProps } from '../invite'

const baseInviteProps: InviteProps = {
  name: '<PERSON>',
  email: stubEmail({ value: '<EMAIL>' }),
  status: 'PENDING',
  collectionId: stubCollection().id,
  role: 'COLLECTION_MANAGER',
}

const stubInvite = (props?: InviteProps): Invite => {
  return new Invite({ ...baseInviteProps, ...props })
}

export { stubInvite, baseInviteProps }
