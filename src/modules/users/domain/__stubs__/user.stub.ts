import { stubEmail } from '../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { stubCollection } from '../../../collections/domain/__stubs__/collection.stub'
import { User, type UserProps } from '../user'
import { UserSettings } from '../userSettings'
import { stubUserAssignment } from './userAssignment.stub'
import { stubUserSettings } from './userSettings.stub'

const baseUserProps: UserProps = {
  email: stubEmail({ value: '<EMAIL>' }),
  name: '<PERSON>',
  settings: stubUserSettings(),
  userAssignments: [],
}

function getUserDefaults(): UserProps {
  return {
    email: stubEmail({ value: '<EMAIL>' }),
    name: '<PERSON>',
    settings: new UserSettings({ emailNotifications: true }),
    userAssignments: [],
  }
}

function stubUser(props: Partial<UserProps> = {}): User {
  const baseProps = getUserDefaults()
  return new User({ ...baseProps, ...props })
}

function stubUserWithAssignments(
  props: Partial<UserProps> = {},
  collectionId?: UniqueEntityID,
): User {
  const baseProps = getUserDefaults()
  const user = new User({ ...baseProps, ...props })

  user.userAssignments.push(
    stubUserAssignment({
      userId: user.id,
      role: 'PLATFORM_MANAGER',
      collectionId: undefined,
    }),
  )

  user.userAssignments.push(
    stubUserAssignment({
      userId: user.id,
      role: 'EDITOR',
      collectionId: collectionId ?? stubCollection().id,
    }),
  )

  return user
}

export { stubUser, stubUserWithAssignments, baseUserProps }
