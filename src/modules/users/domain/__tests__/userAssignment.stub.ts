import {
  baseUserAssignmentProps,
  stubUserAssignment,
} from '../__stubs__/userAssignment.stub'

describe('UserAssignment', () => {
  it('should create a UserAssignment', () => {
    const userAssignment = stubUserAssignment()

    expect(userAssignment.userId.value).toBe(
      baseUserAssignmentProps.userId.value,
    )
    expect(userAssignment.collectionId?.value).toBe(
      baseUserAssignmentProps.collectionId?.value,
    )
    expect(userAssignment.role).toBe(baseUserAssignmentProps.role)
  })
})
