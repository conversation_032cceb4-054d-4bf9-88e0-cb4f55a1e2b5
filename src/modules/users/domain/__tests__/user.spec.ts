import { baseUserProps, stubUser } from '../__stubs__/user.stub'

describe('User', () => {
  it('should create a User', () => {
    const user = stubUser()

    expect(user.id).toBeDefined()
    expect(user.email.value).toBe(baseUserProps.email.value)
    expect(user.name).toBe(baseUserProps.name)
    expect(user.userAssignments).toStrictEqual(baseUserProps.userAssignments)
    expect(user.emailNotifications).toBe(
      baseUserProps.settings.emailNotifications,
    )
  })
})
