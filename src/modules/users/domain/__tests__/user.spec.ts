import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { UserRemovedEvent } from '../../../activities/domain/events/UserRemovedEvent'
import { UserRoleChangedEvent } from '../../../activities/domain/events/UserRoleChangedEvent'
import { baseUserProps, stubUser } from '../__stubs__/user.stub'
import { stubUserAssignment } from '../__stubs__/userAssignment.stub'

describe('User', () => {
  it('should create a User', () => {
    const user = stubUser()

    expect(user.id).toBeDefined()
    expect(user.email.value).toBe(baseUserProps.email.value)
    expect(user.name).toBe(baseUserProps.name)
    expect(user.userAssignments).toStrictEqual(baseUserProps.userAssignments)
    expect(user.emailNotifications).toBe(
      baseUserProps.settings.emailNotifications,
    )
  })

  describe('Domain Events', () => {
    describe('updateAssignment', () => {
      it('should dispatch UserRoleChangedEvent when updating user assignment role', () => {
        const user = stubUser()
        const collectionId = new UniqueEntityID()
        const changedBy = new UniqueEntityID()

        // Add an initial assignment
        user.userAssignments.push(
          stubUserAssignment({
            userId: user.id,
            collectionId,
            role: 'CONTRIBUTOR',
          }),
        )

        const result = user.updateAssignment(collectionId, 'EDITOR', changedBy)

        expect(result).toBe(true)

        const domainEvents = user.getDomainEvents()
        expect(domainEvents).toHaveLength(1)

        const userRoleChangedEvent = domainEvents[0] as UserRoleChangedEvent
        expect(userRoleChangedEvent).toBeInstanceOf(UserRoleChangedEvent)
        expect(userRoleChangedEvent.user.id.equals(user.id)).toBe(true)
        expect(userRoleChangedEvent.collectionId.equals(collectionId)).toBe(
          true,
        )
        expect(userRoleChangedEvent.changedBy.equals(changedBy)).toBe(true)
        expect(userRoleChangedEvent.oldRole).toBe('CONTRIBUTOR')
        expect(userRoleChangedEvent.newRole).toBe('EDITOR')
        expect(userRoleChangedEvent.activityType).toBe('ROLE_CHANGE')
        expect(userRoleChangedEvent.activityData).toEqual({
          targetUserId: user.id.value,
          collectionId: collectionId.value,
          changedBy: changedBy.value,
          oldRole: 'CONTRIBUTOR',
          newRole: 'EDITOR',
        })

        // Verify the assignment was actually updated
        const updatedAssignment = user.userAssignments.find((assignment) =>
          assignment.collectionId?.equals(collectionId),
        )
        expect(updatedAssignment?.role).toBe('EDITOR')
      })

      it('should not dispatch UserRoleChangedEvent when assignment does not exist', () => {
        const user = stubUser()
        const collectionId = new UniqueEntityID()
        const changedBy = new UniqueEntityID()

        const result = user.updateAssignment(collectionId, 'EDITOR', changedBy)

        expect(result).toBe(false)

        const domainEvents = user.getDomainEvents()
        expect(domainEvents).toHaveLength(0)
      })

      it('should not dispatch UserRoleChangedEvent when role is the same', () => {
        const user = stubUser()
        const collectionId = new UniqueEntityID()
        const changedBy = new UniqueEntityID()

        // Add an initial assignment
        user.userAssignments.push(
          stubUserAssignment({
            userId: user.id,
            collectionId,
            role: 'CONTRIBUTOR',
          }),
        )

        const result = user.updateAssignment(
          collectionId,
          'CONTRIBUTOR',
          changedBy,
        )

        expect(result).toBe(false)

        const domainEvents = user.getDomainEvents()
        expect(domainEvents).toHaveLength(0)
      })
    })

    describe('removeAssignment', () => {
      it('should dispatch UserRemovedEvent when removing user assignment', () => {
        const user = stubUser()
        const collectionId = new UniqueEntityID()
        const removedBy = new UniqueEntityID()

        // Add an initial assignment
        user.userAssignments.push(
          stubUserAssignment({
            userId: user.id,
            collectionId,
            role: 'CONTRIBUTOR',
          }),
        )

        const result = user.removeAssignment(collectionId, removedBy)

        expect(result).toBe(true)

        const domainEvents = user.getDomainEvents()
        expect(domainEvents).toHaveLength(1)

        const userRemovedEvent = domainEvents[0] as UserRemovedEvent
        expect(userRemovedEvent).toBeInstanceOf(UserRemovedEvent)
        expect(userRemovedEvent.user.id.equals(user.id)).toBe(true)
        expect(userRemovedEvent.collectionId.equals(collectionId)).toBe(true)
        expect(userRemovedEvent.removedBy.equals(removedBy)).toBe(true)
        expect(userRemovedEvent.removedRole).toBe('CONTRIBUTOR')
        expect(userRemovedEvent.activityType).toBe('USER_REMOVED')
        expect(userRemovedEvent.activityData).toEqual({
          targetUserId: user.id.value,
          collectionId: collectionId.value,
          changedBy: removedBy.value,
          removedRole: 'CONTRIBUTOR',
        })

        // Verify the assignment was actually removed
        const removedAssignment = user.userAssignments.find((assignment) =>
          assignment.collectionId?.equals(collectionId),
        )
        expect(removedAssignment).toBeUndefined()
      })

      it('should not dispatch UserRemovedEvent when assignment does not exist', () => {
        const user = stubUser()
        const collectionId = new UniqueEntityID()
        const removedBy = new UniqueEntityID()

        const result = user.removeAssignment(collectionId, removedBy)

        expect(result).toBe(false)

        const domainEvents = user.getDomainEvents()
        expect(domainEvents).toHaveLength(0)
      })
    })

    it('should handle multiple domain events correctly', () => {
      const user = stubUser()
      const collectionId1 = new UniqueEntityID()
      const collectionId2 = new UniqueEntityID()
      const changedBy = new UniqueEntityID()

      // Add initial assignments
      user.userAssignments.push(
        stubUserAssignment({
          userId: user.id,
          collectionId: collectionId1,
          role: 'CONTRIBUTOR',
        }),
        stubUserAssignment({
          userId: user.id,
          collectionId: collectionId2,
          role: 'VIEWER',
        }),
      )

      // Update one assignment and remove another
      user.updateAssignment(collectionId1, 'EDITOR', changedBy)
      user.removeAssignment(collectionId2, changedBy)

      const domainEvents = user.getDomainEvents()
      expect(domainEvents).toHaveLength(2)

      const roleChangedEvent = domainEvents.find(
        (event) => event instanceof UserRoleChangedEvent,
      ) as UserRoleChangedEvent
      const removedEvent = domainEvents.find(
        (event) => event instanceof UserRemovedEvent,
      ) as UserRemovedEvent

      expect(roleChangedEvent).toBeDefined()
      expect(removedEvent).toBeDefined()

      expect(roleChangedEvent.collectionId.equals(collectionId1)).toBe(true)
      expect(removedEvent.collectionId.equals(collectionId2)).toBe(true)
    })
  })
})
