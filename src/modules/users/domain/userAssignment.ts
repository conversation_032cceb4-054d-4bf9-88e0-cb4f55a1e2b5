import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { ValueObject } from '../../../shared/domain/valueObject'
import { InvalidState } from '../../../shared/errors/domainErrors'
import { Role } from '../types/user'

type UserAssignmentProps = {
  userId: UniqueEntityID
  role: Role
  collectionId?: UniqueEntityID
}

class UserAssignment extends ValueObject<UserAssignmentProps> {
  constructor(props: UserAssignmentProps) {
    if (props.role !== 'PLATFORM_MANAGER' && !props.collectionId) {
      throw new InvalidState(
        'A non-platform manager assignment must have a collection',
        'UserAssignment',
      )
    }

    super(props)
  }

  get userId() {
    return this.props.userId
  }

  get role() {
    return this.props.role
  }

  get collectionId() {
    return this.props.collectionId
  }
}

export { UserAssignment, type UserAssignmentProps }
