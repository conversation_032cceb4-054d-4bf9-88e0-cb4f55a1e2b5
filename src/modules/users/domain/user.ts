import { Email } from '../../../shared/domain/email'
import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { UserRoleChangedEvent } from '../../activities/domain/events'
import { Role } from '../types/user'
import { UserAssignment } from './userAssignment'
import { UserSettings } from './userSettings'

type UserProps = {
  name: string
  email: Email
  settings: UserSettings
  userAssignments: UserAssignment[]
}

class User extends Entity<UserProps> {
  constructor(
    props: UserProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get name() {
    return this.getProp('name')
  }

  get email() {
    return this.getProp('email')
  }

  get settings() {
    return this.getProp('settings')
  }

  get userAssignments() {
    return this.getProp('userAssignments')
  }

  set userAssignments(userAssignments: UserAssignment[]) {
    this.setProp('userAssignments', userAssignments)
  }

  get emailNotifications() {
    return this.getProp('settings').emailNotifications
  }

  updateName(name: UserProps['name']): boolean {
    // TODO: missing guards
    if (name === this.name) return false
    this.setProp('name', name)
    return true
  }

  updateEmail(email: UserProps['email']): boolean {
    // TODO: missing guards
    if (email.equals(this.email)) return false
    this.setProp('email', email)
    return true
  }

  updateEmailNotifications(
    value: UserProps['settings']['emailNotifications'],
  ): boolean {
    // TODO: missing guards
    if (value === this.emailNotifications) return false
    this.setProp('settings', new UserSettings({ emailNotifications: value }))
    return true
  }

  /**
   * Updates a user's assignment for a specific collection.
   * Dispatches UserRoleChangedEvent if the role actually changes.
   */
  updateAssignment(
    collectionId: UniqueEntityID,
    newRole: Role,
    changedBy: UniqueEntityID,
  ): boolean {
    const existingAssignment = this.userAssignments.find((assignment) =>
      assignment.collectionId?.equals(collectionId),
    )

    if (!existingAssignment) {
      return false // Assignment doesn't exist
    }

    const oldRole = existingAssignment.role

    if (oldRole === newRole) {
      return false // No change needed
    }

    // Update the assignment
    this.userAssignments = this.userAssignments.map((assignment) =>
      assignment.collectionId?.equals(collectionId)
        ? new UserAssignment({
            userId: this.id,
            collectionId,
            role: newRole,
          })
        : assignment,
    )

    // Dispatch domain event for role change
    const event = new UserRoleChangedEvent(
      this,
      collectionId,
      changedBy,
      oldRole,
      newRole,
    )
    this.addDomainEvent(event)

    return true
  }

  /**
   * Removes a user's assignment from a specific collection.
   * Dispatches UserRoleChangedEvent with special activity type for removal.
   */
  removeAssignment(
    collectionId: UniqueEntityID,
    removedBy: UniqueEntityID,
  ): boolean {
    const existingAssignment = this.userAssignments.find((assignment) =>
      assignment.collectionId?.equals(collectionId),
    )

    if (!existingAssignment) {
      return false // Assignment doesn't exist
    }

    const removedRole = existingAssignment.role

    // Remove the assignment
    this.userAssignments = this.userAssignments.filter(
      (assignment) => !assignment.collectionId?.equals(collectionId),
    )

    // Dispatch domain event for role removal with special activity type
    const event = new UserRoleChangedEvent(
      this,
      collectionId,
      removedBy,
      removedRole,
      removedRole, // Keep same role but activity type will indicate removal
    )
    // Override the activity type to indicate removal
    ;(event as any).activityType = 'USER_REMOVED'
    ;(event as any).activityData = {
      targetUserId: this.id.value,
      collectionId: collectionId.value,
      changedBy: removedBy.value,
      removedRole,
    }
    this.addDomainEvent(event)

    return true
  }
}

export { User, type UserProps }
