import { Email } from '../../../shared/domain/email'
import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import {
  UserRoleChangedEvent,
  UserRemovedEvent,
} from '../../activities/domain/events'
import { Role } from '../types/user'
import { UserAssignment } from './userAssignment'
import { UserSettings } from './userSettings'

type UserProps = {
  name: string
  email: Email
  settings: UserSettings
  userAssignments: UserAssignment[]
}

class User extends Entity<UserProps> {
  constructor(
    props: UserProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get name() {
    return this.getProp('name')
  }

  get email() {
    return this.getProp('email')
  }

  get settings() {
    return this.getProp('settings')
  }

  get userAssignments() {
    return this.getProp('userAssignments')
  }

  set userAssignments(userAssignments: UserAssignment[]) {
    this.setProp('userAssignments', userAssignments)
  }

  get emailNotifications() {
    return this.getProp('settings').emailNotifications
  }

  updateName(name: UserProps['name']): boolean {
    // TODO: missing guards
    if (name === this.name) return false
    this.setProp('name', name)
    return true
  }

  updateEmail(email: UserProps['email']): boolean {
    // TODO: missing guards
    if (email.equals(this.email)) return false
    this.setProp('email', email)
    return true
  }

  updateEmailNotifications(
    value: UserProps['settings']['emailNotifications'],
  ): boolean {
    // TODO: missing guards
    if (value === this.emailNotifications) return false
    this.setProp('settings', new UserSettings({ emailNotifications: value }))
    return true
  }

  updateAssignment(
    collectionId: UniqueEntityID,
    newRole: Role,
    changedBy: UniqueEntityID,
  ): boolean {
    const existingAssignment = this.userAssignments.find((assignment) =>
      assignment.collectionId?.equals(collectionId),
    )

    if (!existingAssignment) {
      return false
    }

    const oldRole = existingAssignment.role

    if (oldRole === newRole) {
      return false
    }

    this.userAssignments = this.userAssignments.map((assignment) =>
      assignment.collectionId?.equals(collectionId)
        ? new UserAssignment({
            userId: this.id,
            collectionId,
            role: newRole,
          })
        : assignment,
    )

    const event = new UserRoleChangedEvent(
      this,
      collectionId,
      changedBy,
      oldRole,
      newRole,
    )
    this.addDomainEvent(event)

    return true
  }

  removeAssignment(
    collectionId: UniqueEntityID,
    removedBy: UniqueEntityID,
  ): boolean {
    const existingAssignment = this.userAssignments.find((assignment) =>
      assignment.collectionId?.equals(collectionId),
    )

    if (!existingAssignment) {
      return false
    }

    const removedRole = existingAssignment.role

    this.userAssignments = this.userAssignments.filter(
      (assignment) => !assignment.collectionId?.equals(collectionId),
    )

    const event = new UserRemovedEvent(
      this,
      collectionId,
      removedBy,
      removedRole,
    )
    this.addDomainEvent(event)

    return true
  }
}

export { User, type UserProps }
