import { Email } from '../../../shared/domain/email'
import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { InvalidState } from '../../../shared/errors/domainErrors'
import { InviteStatus } from '../types/invite'
import { Role } from '../types/user'
import { InviteCreatedEvent } from './events/InviteCreatedEvent'

type InviteProps = {
  name: string
  email: Email
  status: InviteStatus
  role: Role
  collectionId?: UniqueEntityID
}

class Invite extends Entity<InviteProps> {
  constructor(
    props: InviteProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
    // New invite
    if (!id) {
      //TODO: When creating a new invite, we should ensure that the initial state is always PENDING
      this.addDomainEvent(new InviteCreatedEvent(this))
    }
  }

  get email() {
    return this.getProp('email')
  }

  get status() {
    return this.getProp('status')
  }

  get collectionId() {
    return this.getProp('collectionId')
  }

  get role() {
    return this.getProp('role')
  }

  get name() {
    return this.getProp('name')
  }

  public cancel() {
    if (this.status !== 'PENDING')
      throw new InvalidState('Cannot cancel invite', 'Invite')

    this.setProp('status', 'CANCELLED')
  }
}

export { Invite, type InviteProps }
