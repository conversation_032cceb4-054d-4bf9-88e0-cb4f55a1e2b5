import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Invite } from '../invite'

export class InviteCreatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'user:inviteCreated'
  public readonly invite: Invite

  constructor(invite: Invite) {
    super(InviteCreatedEvent.eventIdentifier)
    this.invite = invite
  }

  public getEntityId(): UniqueEntityID {
    return this.invite.id
  }
}
