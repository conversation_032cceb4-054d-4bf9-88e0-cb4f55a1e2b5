import { User } from '../domain/user'

type AuthorizationToken = {
  sub: string
  iat: number
  exp: number
  admin: boolean
  collections: {
    id: string
    role: string
  }[]
}

interface AuthorizationService {
  /**
   * Generates a raw token string from the token object
   * @param userData {AuthorizationUserData} The token object
   * @returns {string} The raw token string
   */
  generateToken: (user: User) => Promise<string>

  /**
   * Verifies the raw token string and returns the token object
   * @param token {string} The raw token string
   * @returns {Promise<AuthorizationToken>} The token object
   */
  verifyToken: (token: string) => Promise<AuthorizationToken>
}

export type { AuthorizationToken, AuthorizationService }
