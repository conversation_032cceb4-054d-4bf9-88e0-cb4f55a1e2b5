import { createSecret<PERSON><PERSON>, KeyObject } from 'crypto'
import { jwtVerify, SignJWT } from 'jose'
import { Configuration } from '../../../../shared/infra/configuration'
import { Environment } from '../../../../shared/infra/environment'
import { User } from '../../domain/user'
import { Role } from '../../types/user'
import {
  AuthorizationService,
  AuthorizationToken,
} from '../authorizationService'

class JWTAuthorizationService implements AuthorizationService {
  readonly #secret: KeyObject

  constructor() {
    const rawSecret = Environment.instance.get(
      'JWT_AUTHORIZATION_SERVICE_SECRET',
    )
    this.#secret = createSecretKey(rawSecret, 'utf8')
  }

  async generateToken(user: User): Promise<string> {
    const { jwt } = Configuration.instance.get(
      'services.authorization.configuration',
    )

    const userData = {
      sub: user.id.value,
      admin: false,
      collections: [] as { id: string; role: Role }[],
    }
    for (const assignment of user.userAssignments) {
      if (assignment.collectionId) {
        userData.collections.push({
          id: assignment.collectionId.value,
          role: assignment.role,
        })
      } else if (assignment.role === 'PLATFORM_MANAGER') {
        userData.admin = true
      }
    }

    const token = await new SignJWT(userData)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(jwt.expiration)
      .sign(this.#secret)

    return token
  }

  async verifyToken(token: string): Promise<AuthorizationToken> {
    const result = await jwtVerify<AuthorizationToken>(token, this.#secret)
    return result.payload
  }
}

export { JWTAuthorizationService }
