import { ServicesInitializer } from '../../../shared/services/servicesInitializer'
import { JWTAuthorizationService } from './jwtAuthorizationService'

type UsersServices = {
  authorization: JWTAuthorizationService
}

class UsersServicesInitializer extends ServicesInitializer<UsersServices> {
  protected async setup(): Promise<UsersServices> {
    return {
      authorization: new JWTAuthorizationService(),
    }
  }
}

const usersServices = new UsersServicesInitializer()

export { usersServices }
