import { Route } from '../../../shared/infra/http/routes/route'
import { acceptInviteController } from '../useCases/acceptInvite'
import { cancelInviteController } from '../useCases/cancelInvite'
import { createInviteController } from '../useCases/createInvite'
import { getCurrentUserController } from '../useCases/getCurrentUser'
import { getInviteController } from '../useCases/getInvite'
import { getInvitesController } from '../useCases/getInvites'
import { getUserController } from '../useCases/getUser'
import { getUsersController } from '../useCases/getUsers'
import { loginController } from '../useCases/login'
import { updateUserController } from '../useCases/updateUser'

const usersRoute = new Route(
  'users',
  loginController,
  getUsersController,
  getCurrentUserController,
  getUserController,
  acceptInviteController,
  updateUserController,
)

const invitesRoute = new Route(
  'invites',
  getInvitesController,
  getInviteController,
  createInviteController,
  cancelInviteController,
)

export { usersRoute, invitesRoute }
