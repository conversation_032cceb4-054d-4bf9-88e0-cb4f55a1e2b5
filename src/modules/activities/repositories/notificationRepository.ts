import { count, eq } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { notificationsTable } from '../../../shared/database/schema'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Notification } from '../domain/notification'
import { NotificationDTO } from '../dto/notificationDTO'
import { NotificationMapper } from '../mappers'
import { NotificationModel } from '../models/notification.model'
import { GenericNotificationRepository } from './interfaces/genericNotificationRepository'

class NotificationRepository
  extends AbstractDrizzleRepository<
    typeof notificationsTable,
    Notification,
    NotificationModel,
    NotificationDTO
  >
  implements GenericNotificationRepository
{
  constructor(db: DatabaseClient) {
    super(
      'Notification',
      db,
      NotificationMapper.getInstance(),
      notificationsTable,
      'notificationsTable',
    )
  }

  async getByUserId(
    userId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Notification>> {
    const whereConditions = [eq(notificationsTable.userId, userId.value)]
    const queryOptions = this.buildQueryOptions(options, whereConditions)

    const [countResult] = await this.db
      .select({ totalCount: count() })
      .from(this.table)
      .where(whereConditions[0])

    const totalCount = countResult?.totalCount ?? 0

    const notifications =
      await this.db.query.notificationsTable.findMany(queryOptions)

    return {
      items: (<NotificationModel[]>notifications).map(this.mapper.toDomain),
      total: totalCount,
    }
  }

  async setAllRead(userId: UniqueEntityID): Promise<void> {
    await this.db
      .update(notificationsTable)
      .set({ read: true, updatedAt: new Date() })
      .where(eq(notificationsTable.userId, userId.value))
  }
}

export { NotificationRepository }
