import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { PaginatedDomain } from '../../../../shared/types/pagination'
import { Notification } from '../../domain/notification'

interface GenericNotificationRepository
  extends GenericRepository<Notification> {
  getByUserId(
    userId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Notification>>
  setAllRead(userId: UniqueEntityID): Promise<void>
}

export type { GenericNotificationRepository }
