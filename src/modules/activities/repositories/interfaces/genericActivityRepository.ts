import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  BaseFindQuery,
  GenericRepository,
} from '../../../../shared/repositories/genericRepository'
import { PaginatedDomain } from '../../../../shared/types/pagination'
import { Activity } from '../../domain/activity'

interface GenericActivityRepository extends GenericRepository<Activity> {
  getByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Activity>>
}

export type { GenericActivityRepository }
