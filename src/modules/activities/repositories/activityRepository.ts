import { count, eq } from 'drizzle-orm'
import { DatabaseClient } from '../../../shared/database'
import { activitiesTable } from '../../../shared/database/schema'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { BaseFindQuery } from '../../../shared/repositories/genericRepository'
import { AbstractDrizzleRepository } from '../../../shared/repositories/implementations/abstractDrizzleRepository'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Activity } from '../domain/activity'
import { ActivityDTO } from '../dto/activityDTO'
import { ActivityMapper } from '../mappers'
import { ActivityModel } from '../models/activity.model'
import { GenericActivityRepository } from './interfaces/genericActivityRepository'

class ActivityRepository
  extends AbstractDrizzleRepository<
    typeof activitiesTable,
    Activity,
    ActivityModel,
    ActivityDTO
  >
  implements GenericActivityRepository
{
  constructor(db: DatabaseClient) {
    super(
      'Activity',
      db,
      ActivityMapper.getInstance(),
      activitiesTable,
      'activitiesTable',
    )
  }

  async getByCollectionId(
    collectionId: UniqueEntityID,
    options?: BaseFindQuery,
  ): Promise<PaginatedDomain<Activity>> {
    const whereConditions = [
      eq(activitiesTable.collectionId, collectionId.value),
    ]

    const queryOptions = this.buildQueryOptions(
      { ...options, includeAll: false },
      whereConditions,
    )

    const activities =
      await this.db.query.activitiesTable.findMany(queryOptions)

    const [countResult] = await this.db
      .select({ totalCount: count() })
      .from(this.table)
      .where(whereConditions[0])

    const totalCount = countResult?.totalCount ?? 0

    return {
      items: (<ActivityModel[]>activities).map(this.mapper.toDomain),
      total: totalCount,
    }
  }
}

export { ActivityRepository }
