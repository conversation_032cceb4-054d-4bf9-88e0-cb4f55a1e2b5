import { Route } from '../../../../shared/infra/http/routes/route'
import { deleteAllNotificationsController } from '../../useCases/deleteAllNotifications'
import { deleteNotificationController } from '../../useCases/deleteNotification'
import { getNotificationsController } from '../../useCases/getNotifications'
import { markAllNotificationsAsReadController } from '../../useCases/markAllNotificationsAsRead'
import { markNotificationAsReadController } from '../../useCases/markNotificationAsRead'

const notificationsRoute = new Route(
  'notifications',
  getNotificationsController,
  markNotificationAsReadController,
  markAllNotificationsAsReadController,
  deleteNotificationController,
  deleteAllNotificationsController,
)

export { notificationsRoute }
