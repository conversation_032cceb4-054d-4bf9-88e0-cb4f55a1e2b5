import { Route } from '../../../../shared/infra/http/routes/route'
import { getNotificationsController } from '../../useCases/getNotifications'
import { markAllNotificationsAsReadController } from '../../useCases/markAllNotificationsAsRead'
import { markNotificationAsReadController } from '../../useCases/markNotificationAsRead'

const notificationsRoute = new Route(
  'notifications',
  getNotificationsController,
  markNotificationAsReadController,
  markAllNotificationsAsReadController,
)

export { notificationsRoute }
