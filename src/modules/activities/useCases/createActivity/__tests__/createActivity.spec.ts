import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubChange } from '../../../../entries/domain/__stubs__/change.stub'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { activityRepository } from '../../../repositories'
import { CreateActivity } from '../createActivity'
import { CreateActivityDTO } from '../createActivity.dto'

let useCase: CreateActivity
let user: User
let collectionId: string

describe('CreateActivity', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    await userRepository.save(user)

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    useCase = new CreateActivity(activityRepository, collectionRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to create a new activity', async () => {
    const request: CreateActivityDTO = {
      collectionId,
      type: 'UPDATE',
      data: stubChange().data,
    }
    const result = await execute(useCase, request, user)

    expect(result.collectionId).toEqual(request.collectionId)
    expect(result.userId).toEqual(user.id.value)
    expect(result.type).toEqual(request.type)
    expect(result.data).toStrictEqual(request.data)
  })
})
