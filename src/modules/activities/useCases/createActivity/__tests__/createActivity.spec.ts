import { Email } from '../../../../../shared/domain/email'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubChange } from '../../../../entries/domain/__stubs__/change.stub'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { CreateActivity } from '../createActivity'
import { CreateActivityDTO } from '../createActivity.dto'

let useCase: CreateActivity
let user: User
let collectionId: string

describe('CreateActivity', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    user = stubUser()
    await userRepository.save(user)

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    useCase = new CreateActivity(
      activityRepository,
      collectionRepository,
      notificationRepository,
      userRepository,
    )
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to create a new activity', async () => {
    const request: CreateActivityDTO = {
      collectionId,
      type: 'UPDATE',
      data: stubChange().data,
    }

    await execute(useCase, request, user)

    const collectionIdEntity = new UniqueEntityID(request.collectionId)
    const activitiesResult = await activityRepository.getByCollectionId(
      collectionIdEntity,
      { includeAll: true },
    )
    const createdActivity = activitiesResult.items.find(
      (activity) =>
        activity.userId.value === user.id.value &&
        activity.type === request.type,
    )

    expect(createdActivity).toBeDefined()
    expect(createdActivity!.collectionId.value).toEqual(request.collectionId)
    expect(createdActivity!.userId.value).toEqual(user.id.value)
    expect(createdActivity!.type).toEqual(request.type)
    expect(Array.isArray(createdActivity!.data)).toBe(true)
    expect((createdActivity!.data as unknown[]).length).toEqual(
      (request.data as unknown[]).length,
    )
  })

  it('Should create notifications for target users', async () => {
    const targetUser = stubUser()
    targetUser.updateEmail(
      new Email({ value: `target-${Date.now()}@example.com` }),
    )
    await userRepository.save(targetUser)

    const request: CreateActivityDTO = {
      collectionId,
      type: 'ENTRY_CREATE',
      data: stubChange().data,
      targetUsers: [targetUser.id],
    }

    await execute(useCase, request, user)

    const collectionIdEntity = new UniqueEntityID(request.collectionId)
    const activitiesResult = await activityRepository.getByCollectionId(
      collectionIdEntity,
      { includeAll: true },
    )
    const createdActivity = activitiesResult.items.find(
      (activity) =>
        activity.userId.value === user.id.value &&
        activity.type === request.type,
    )

    expect(createdActivity).toBeDefined()

    const notificationsResult = await notificationRepository.getByUserId(
      targetUser.id,
      { includeAll: true },
    )
    const createdNotification = notificationsResult.items.find((notification) =>
      notification.activityId.equals(createdActivity!.id),
    )

    expect(createdNotification).toBeDefined()
    expect(createdNotification!.userId.equals(targetUser.id)).toBe(true)
    expect(createdNotification!.read).toBe(false)
    expect(createdNotification!.sent).toBe(false)
  })

  it('Should not create notifications when no target users provided', async () => {
    const request: CreateActivityDTO = {
      collectionId,
      type: 'ENTRY_UPDATE',
      data: stubChange().data,
    }

    await execute(useCase, request, user)

    const collectionIdEntity = new UniqueEntityID(request.collectionId)
    const activitiesResult = await activityRepository.getByCollectionId(
      collectionIdEntity,
      { includeAll: true },
    )
    const createdActivity = activitiesResult.items.find(
      (activity) =>
        activity.userId.value === user.id.value &&
        activity.type === request.type,
    )

    expect(createdActivity).toBeDefined()

    // Verify no notifications were created
    const notificationsResult = await notificationRepository.getByUserId(
      user.id,
      { includeAll: true },
    )
    const relatedNotifications = notificationsResult.items.filter(
      (notification) => notification.activityId.equals(createdActivity!.id),
    )

    expect(relatedNotifications).toHaveLength(0)
  })
})
