import { collectionRepository } from '../../../collections/repositories'
import { userRepository } from '../../../users/repositories'
import { activityRepository, notificationRepository } from '../../repositories'
import { CreateActivity } from './createActivity'

const useCase = new CreateActivity(
  activityRepository,
  collectionRepository,
  notificationRepository,
  userRepository,
)

export { useCase }
