import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { IdDTO } from '../../../../shared/dto/idDTO'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericCollectionRepository } from '../../../collections/repositories/interfaces/genericCollectionRepository'
import { GenericUserRepository } from '../../../users/repositories/interfaces/genericUserRepository'
import { Activity } from '../../domain/activity'
import { NotificationCreatedEvent } from '../../domain/events'
import { Notification } from '../../domain/notification'
import { GenericActivityRepository } from '../../repositories/interfaces/genericActivityRepository'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'
import { CreateActivityDTO } from './createActivity.dto'

class CreateActivity extends UseCase<CreateActivityDTO> {
  constructor(
    private readonly _activityRepository: GenericActivityRepository,
    private readonly _collectionRepository: GenericCollectionRepository,
    private readonly _notificationRepository: GenericNotificationRepository,
    private readonly _userRepository: GenericUserRepository,
  ) {
    super(CreateActivity.name)
  }

  private async getCollection(collectionId: IdDTO) {
    return this._collectionRepository.getById(new UniqueEntityID(collectionId))
  }

  async execute(request: Readonly<CreateActivityDTO>): Promise<void> {
    const { type, data, collectionId, targetUsers } = request

    const collection = await this.getCollection(collectionId)

    const currentUser = AuthContext.getUser()

    const activity = new Activity({
      collectionId: collection.id,
      userId: currentUser.id,
      type,
      data,
    })

    // Create notifications for target users if provided
    const notifications = targetUsers
      ? await this.createNotifications(activity, targetUsers)
      : []

    // Save activity and notifications in a transaction-like manner
    // Note: In a real implementation, this should be wrapped in a database transaction
    await this._activityRepository.save(activity)

    // Save all notifications
    for (const notification of notifications) {
      await this._notificationRepository.save(notification)

      // Dispatch email notification event for each created notification
      notification.addDomainEvent(new NotificationCreatedEvent(notification))
    }
  }

  private async createNotifications(
    activity: Activity,
    targetUserIds: UniqueEntityID[],
  ): Promise<Notification[]> {
    const notifications: Notification[] = []

    for (const userId of targetUserIds) {
      // Verify user exists and has email notifications enabled
      const user = await this._userRepository.getById(userId)

      if (user.emailNotifications) {
        const notification = new Notification({
          userId: user.id,
          activityId: activity.id,
          read: false,
          sent: false,
        })

        notifications.push(notification)
      }
    }

    return notifications
  }
}

export { CreateActivity }
