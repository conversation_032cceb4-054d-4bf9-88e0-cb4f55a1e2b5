import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { IdDTO } from '../../../../shared/dto/idDTO'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericCollectionRepository } from '../../../collections/repositories/interfaces/genericCollectionRepository'
import { Activity } from '../../domain/activity'
import { ActivityDTO } from '../../dto/activityDTO'
import { ActivityMapper } from '../../mappers'
import { GenericActivityRepository } from '../../repositories/interfaces/genericActivityRepository'
import { CreateActivityDTO } from './createActivity.dto'

class CreateActivity extends UseCase<CreateActivityDTO, ActivityDTO> {
  constructor(
    private readonly _activityRepository: GenericActivityRepository,
    private readonly _collectionRepository: GenericCollectionRepository,
  ) {
    super(CreateActivity.name)
  }

  private async getCollection(collectionId: IdDTO) {
    return this._collectionRepository.getById(new UniqueEntityID(collectionId))
  }

  async execute(request: Readonly<CreateActivityDTO>): Promise<ActivityDTO> {
    const { type, data, collectionId } = request

    const collection = await this.getCollection(collectionId)

    const currentUser = AuthContext.getUser()

    const activity = new Activity({
      collectionId: collection.id,
      userId: currentUser.id,
      type,
      data,
    })

    await this._activityRepository.save(activity)

    return ActivityMapper.getInstance().toDTO(activity)
  }
}

export { CreateActivity }
