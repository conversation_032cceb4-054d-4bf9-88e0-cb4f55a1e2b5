import { constants } from 'http2'
import { InferType, object } from 'yup'
import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { Controller } from '../../../../shared/infra/controller'
import { QueryParams, QuerySchema } from '../../../../shared/types/query'
import { NotificationDTO } from '../../dto/notificationDTO'
import { GetNotifications } from './getNotifications'
import { GetNotificationsDTO } from './getNotifications.dto'

const schema = object({ params: object(), query: QuerySchema })

class GetNotificationsController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryParams,
  GetNotificationsDTO,
  PaginatedResponseDTO<NotificationDTO>
> {
  public constructor(useCase: GetNotifications) {
    super('get', '', constants.HTTP_STATUS_OK, useCase, schema)
  }

  protected parseInput(request: InferType<typeof schema>): GetNotificationsDTO {
    return {
      userId: '00000000-0000-0000-0000-000000000000', // TODO: need a way to pass the current user
      ...request.query,
    }
  }
}

export { GetNotificationsController }
