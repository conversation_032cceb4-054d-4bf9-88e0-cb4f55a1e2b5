import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import { Notification } from '../../../domain/notification'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { GetNotifications } from '../getNotifications'

let useCase: GetNotifications
let user: User
let notification: Notification

describe('GetNotifications', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    await userRepository.save(user)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    const activity = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity)

    notification = stubNotification({
      userId: user.id,
      activityId: activity.id,
    })
    await notificationRepository.save(notification)

    useCase = new GetNotifications(notificationRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to get user notifications', async () => {
    const result = await execute(useCase, undefined, user)

    expect(result.items.length).toEqual(1)
    expect(result.items[0]?.userId).toEqual(user.id.value)
  })
})
