import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { QueryParams } from '../../../../shared/types/query'
import { NotificationDTO } from '../../dto/notificationDTO'
import { NotificationMapper } from '../../mappers'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'
import { GetNotificationsDTO } from './getNotifications.dto'

class GetNotifications extends UseCase<
  GetNotificationsDTO,
  PaginatedResponseDTO<NotificationDTO>
> {
  constructor(
    private readonly _notificationRepository: GenericNotificationRepository,
  ) {
    super(GetNotifications.name)
  }

  async execute(): Promise<PaginatedResponseDTO<NotificationDTO>> {
    const currentUser = AuthContext.getUser()

    const result = await this.getNotifications(currentUser.id)

    return NotificationMapper.getInstance().toPageDTO(result)
  }
  private getNotifications(userId: UniqueEntityID, queryParams?: QueryParams) {
    return this._notificationRepository.getByUserId(userId, queryParams)
  }
}

export { GetNotifications }
