import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { User } from '../../../users/domain/user'
import { Notification } from '../../domain/notification'
import { NotificationDTO } from '../../dto/notificationDTO'
import { NotificationMapper } from '../../mappers'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'
import { MarkNotificationAsReadDTO } from './markNotificationAsRead.dto'

class MarkNotificationAsRead extends UseCase<
  MarkNotificationAsReadDTO,
  NotificationDTO
> {
  constructor(
    private readonly _notificationRepository: GenericNotificationRepository,
  ) {
    super(MarkNotificationAsRead.name)
  }

  private checkPermissions(notification: Notification, currentUser: User) {
    if (!currentUser.id.equals(notification.userId)) {
      throw new NotAllowed(MarkNotificationAsRead.name)
    }
  }

  async execute(
    request: Readonly<MarkNotificationAsReadDTO>,
  ): Promise<NotificationDTO> {
    const currentUser = AuthContext.getUser()

    const notificationId = new UniqueEntityID(request.notificationId)

    const notification =
      await this._notificationRepository.getById(notificationId)

    this.checkPermissions(notification, currentUser)

    notification.markAsRead()
    await this._notificationRepository.save(notification)

    return NotificationMapper.getInstance().toDTO(notification)
  }
}

export { MarkNotificationAsRead }
