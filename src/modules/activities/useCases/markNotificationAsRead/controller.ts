import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { NotificationDTO } from '../../dto/notificationDTO'
import { MarkNotificationAsRead } from './markNotificationAsRead'
import { MarkNotificationAsReadDTO } from './markNotificationAsRead.dto'

const schema = object({
  params: object({
    notificationId: string().uuid().required(),
  }).required(),
})

class MarkNotificationAsReadController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  MarkNotificationAsReadDTO,
  NotificationDTO
> {
  public constructor(useCase: MarkNotificationAsRead) {
    super(
      'patch',
      ':notificationId/read',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): MarkNotificationAsReadDTO {
    return {
      notificationId: request.params.notificationId,
      // userId: '00000000-0000-0000-0000-000000000000', // TODO: need a way to pass the current user
    }
  }
}

export { MarkNotificationAsReadController }
