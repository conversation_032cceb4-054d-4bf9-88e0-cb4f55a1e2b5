import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { MarkNotificationAsRead } from '../markNotificationAsRead'

let useCase: MarkNotificationAsRead
let user: User
let notificationId: UniqueEntityID

describe('MarkNotificationAsRead', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    await userRepository.save(user)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    const activity = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity)

    const notification = stubNotification({
      activityId: activity.id,
      userId: user.id,
    })
    notificationId = notification.id
    await notificationRepository.save(notification)

    useCase = new MarkNotificationAsRead(notificationRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to mark notification as read', async () => {
    await expect(
      execute(useCase, { notificationId: notificationId.value }, user),
    ).resolves.not.toThrow()

    const notification = await notificationRepository.getById(notificationId)
    expect(notification.read).toBeTruthy()
  })
})
