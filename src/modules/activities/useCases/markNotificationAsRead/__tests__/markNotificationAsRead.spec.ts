import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import { Activity } from '../../../domain/activity'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { MarkNotificationAsRead } from '../markNotificationAsRead'

let useCase: MarkNotificationAsRead
let user: User
let activity: Activity
let notificationId: UniqueEntityID

describe('MarkNotificationAsRead', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    await userRepository.save(user)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    activity = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity)

    const notification = stubNotification({
      activityId: activity.id,
      userId: user.id,
    })
    notificationId = notification.id
    await notificationRepository.save(notification)

    useCase = new MarkNotificationAsRead(notificationRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to mark notification as read', async () => {
    await expect(
      execute(useCase, { notificationId: notificationId.value }, user),
    ).resolves.not.toThrow()

    const notification = await notificationRepository.getById(notificationId)
    expect(notification.read).toBeTruthy()
  })

  it('Should not be able to mark another user notification as read', async () => {
    const otherUser = stubUser({
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(otherUser)

    const otherNotification = stubNotification({
      activityId: activity.id,
      userId: otherUser.id,
    })
    await notificationRepository.save(otherNotification)

    await expect(
      execute(useCase, { notificationId: otherNotification.id.value }, user),
    ).rejects.toThrow(NotAllowed)
  })
})
