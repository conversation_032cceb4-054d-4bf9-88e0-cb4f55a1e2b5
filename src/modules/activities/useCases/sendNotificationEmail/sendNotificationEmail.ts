import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { logger } from '../../../../shared/infra/logger'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserRepository } from '../../../users/repositories/userRepository'
import { ActivityRepository } from '../../repositories/activityRepository'
import { NotificationRepository } from '../../repositories/notificationRepository'
import { SendNotificationEmailDTO } from './sendNotificationEmail.dto'

export class SendNotificationEmail extends UseCase<
  SendNotificationEmailDTO,
  void
> {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly notificationRepository: NotificationRepository,
    private readonly activityRepository: ActivityRepository,
  ) {
    super('SendNotificationEmail')
  }

  async execute(dto: SendNotificationEmailDTO): Promise<void> {
    const { notificationId, userId, activityId } = dto

    try {
      const userIdEntity = new UniqueEntityID(userId)
      const user = await this.userRepository.getById(userIdEntity)

      // Skip sending email if user has email notifications disabled
      if (!user.emailNotifications) return

      const notificationIdEntity = new UniqueEntityID(notificationId)
      const notification =
        await this.notificationRepository.getById(notificationIdEntity)

      const activityIdEntity = new UniqueEntityID(activityId)
      const activity = await this.activityRepository.getById(activityIdEntity)

      // TODO: Replace this temporary logging with actual email sending
      logger.info('Email notification would be sent', {
        notificationId,
        userId,
        activityId,
        userEmail: user.email.value,
        userName: user.name,
        activityType: activity.type,
        activityCollectionId: activity.collectionId.value,
        message: 'Temporary log - actual email service integration pending',
      })
    } catch (error) {
      logger.error('Failed to process notification email', {
        notificationId,
        userId,
        activityId,
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }
}
