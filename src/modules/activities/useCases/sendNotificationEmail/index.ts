import { userRepository } from '../../../users/repositories'
import { activityRepository, notificationRepository } from '../../repositories'
import { SendNotificationEmail } from './sendNotificationEmail'

export const sendNotificationEmailUseCase = new SendNotificationEmail(
  userRepository,
  notificationRepository,
  activityRepository,
)

export { SendNotificationEmail } from './sendNotificationEmail'
