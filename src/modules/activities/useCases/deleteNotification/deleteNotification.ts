import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { User } from '../../../users/domain/user'
import { Notification } from '../../domain/notification'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'
import { DeleteNotificationDTO } from './deleteNotification.dto'

class DeleteNotification extends UseCase<DeleteNotificationDTO, void> {
  constructor(
    private readonly _notificationRepository: GenericNotificationRepository,
  ) {
    super(DeleteNotification.name)
  }

  private checkPermissions(notification: Notification, currentUser: User) {
    if (!currentUser.id.equals(notification.userId)) {
      throw new NotAllowed(DeleteNotification.name)
    }
  }

  async execute(request: Readonly<DeleteNotificationDTO>): Promise<void> {
    const currentUser = AuthContext.getUser()

    const notificationId = new UniqueEntityID(request.notificationId)

    const notification =
      await this._notificationRepository.getById(notificationId)

    this.checkPermissions(notification, currentUser)

    await this._notificationRepository.delete(notificationId)
  }
}

export { DeleteNotification }
