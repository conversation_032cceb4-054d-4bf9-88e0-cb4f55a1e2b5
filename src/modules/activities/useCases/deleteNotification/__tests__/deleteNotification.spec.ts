import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { EntityNotFound } from '../../../../../shared/errors/repositoryErrors'
import { NotAllowed } from '../../../../../shared/errors/useCaseErrors'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { DeleteNotification } from '../deleteNotification'

let useCase: DeleteNotification
let user: User
let otherUser: User
let notificationId: UniqueEntityID
let otherUserNotificationId: UniqueEntityID

describe('DeleteNotification', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser({
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(user)

    otherUser = stubUser({
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(otherUser)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    const activity = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity)

    const otherActivity = stubActivity({
      collectionId: collection.id,
      userId: otherUser.id,
    })
    await activityRepository.save(otherActivity)

    const notification = stubNotification({
      activityId: activity.id,
      userId: user.id,
    })
    notificationId = notification.id
    await notificationRepository.save(notification)

    const otherUserNotification = stubNotification({
      activityId: otherActivity.id,
      userId: otherUser.id,
    })
    otherUserNotificationId = otherUserNotification.id
    await notificationRepository.save(otherUserNotification)

    useCase = new DeleteNotification(notificationRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to delete own notification', async () => {
    const notificationBeforeDelete =
      await notificationRepository.getById(notificationId)
    expect(notificationBeforeDelete).toBeDefined()
    expect(notificationBeforeDelete.deleted).toBeFalsy()

    await expect(
      execute(useCase, { notificationId: notificationId.value }, user),
    ).resolves.not.toThrow()

    await expect(
      notificationRepository.getById(notificationId),
    ).rejects.toThrow()

    const notification = await notificationRepository.getById(notificationId, {
      includeDeleted: true,
    })
    expect(notification.deleted).toBeTruthy()
  })

  it('Should not be able to delete another user notification', async () => {
    await expect(
      execute(useCase, { notificationId: otherUserNotificationId.value }, user),
    ).rejects.toThrow(NotAllowed)
  })

  it('Should throw error when notification does not exist', async () => {
    const nonExistentId = new UniqueEntityID().value
    await expect(
      execute(useCase, { notificationId: nonExistentId }, user),
    ).rejects.toThrow(EntityNotFound)
  })
})
