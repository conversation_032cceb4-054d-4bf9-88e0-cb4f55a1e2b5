import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { DeleteNotification } from './deleteNotification'
import { DeleteNotificationDTO } from './deleteNotification.dto'

const schema = object({
  params: object({
    notificationId: string().uuid().required(),
  }).required(),
})

class DeleteNotificationController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  DeleteNotificationDTO
> {
  public constructor(useCase: DeleteNotification) {
    super(
      'delete',
      ':notificationId',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(
    request: InferType<typeof schema>,
  ): DeleteNotificationDTO {
    return {
      notificationId: request.params.notificationId,
    }
  }
}

export { DeleteNotificationController }
