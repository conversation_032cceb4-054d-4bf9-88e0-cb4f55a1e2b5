import { constants } from 'http2'
import { InferType, object, string } from 'yup'
import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { QueryDTO } from '../../../../shared/dto/queryDTO'
import { Controller } from '../../../../shared/infra/controller'
import { QuerySchema } from '../../../../shared/types/query'
import { ActivityDTO } from '../../dto/activityDTO'
import { GetActivities } from './getActivities'
import { GetActivitiesDTO } from './getActivities.dto'

const schema = object({
  params: object({
    collectionId: string().uuid().required(),
  }).required(),
  query: QuerySchema,
})

class GetActivitiesController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  QueryDTO,
  GetActivitiesDTO,
  PaginatedResponseDTO<ActivityDTO>
> {
  public constructor(useCase: GetActivities) {
    super(
      'get',
      ':collectionId/activity',
      constants.HTTP_STATUS_OK,
      useCase,
      schema,
    )
  }

  protected parseInput(request: InferType<typeof schema>): GetActivitiesDTO {
    return { collectionId: request.params.collectionId, ...request.query }
  }
}

export { GetActivitiesController }
