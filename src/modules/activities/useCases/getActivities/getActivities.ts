import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../../shared/dto/paginatedResponseDTO'
import { NotAllowed } from '../../../../shared/errors/useCaseErrors'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { UserUtils } from '../../../../utils/userUtils'
import { ActivityDTO } from '../../dto/activityDTO'
import { ActivityMapper } from '../../mappers'
import { GenericActivityRepository } from '../../repositories/interfaces/genericActivityRepository'
import { GetActivitiesDTO } from './getActivities.dto'

class GetActivities extends UseCase<
  GetActivitiesDTO,
  PaginatedResponseDTO<ActivityDTO>
> {
  constructor(private readonly _activityRepository: GenericActivityRepository) {
    super(GetActivities.name)
  }

  private checkPermissions(collectionId: UniqueEntityID) {
    const currentUser = AuthContext.getUser()

    const hasRoleList = [
      UserUtils.hasRole(currentUser, 'PLATFORM_MANAGER'),
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId),
      UserUtils.hasRole(currentUser, 'EDITOR', collectionId),
      UserUtils.hasRole(currentUser, 'COLLECTION_MANAGER', collectionId),
      UserUtils.hasRole(currentUser, 'EDITOR', collectionId),
    ]

    const hasPermission = hasRoleList.some((hasRole) => hasRole === true)

    if (!hasPermission) {
      throw new NotAllowed(GetActivities.name)
    }
  }

  async execute(
    request: Readonly<GetActivitiesDTO>,
  ): Promise<PaginatedResponseDTO<ActivityDTO>> {
    const collectionId = new UniqueEntityID(request.collectionId)

    this.checkPermissions(collectionId)
    this.checkPermissions(collectionId)

    const result = await this._activityRepository.getByCollectionId(
      collectionId,
      request,
    )

    return ActivityMapper.getInstance().toPageDTO(result)
  }
}

export { GetActivities }
