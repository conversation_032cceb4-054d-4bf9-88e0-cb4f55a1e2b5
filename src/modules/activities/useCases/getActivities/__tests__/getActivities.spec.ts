import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../../users/domain/__stubs__/userAssignment.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { Activity } from '../../../domain/activity'
import { activityRepository } from '../../../repositories'
import { GetActivities } from '../getActivities'

let useCase: GetActivities
let user: User
let collectionId: string
let activity: Activity

describe('GetActivities', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    user = stubUser()
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
    await userRepository.save(user)

    activity = stubActivity({ collectionId: collection.id, userId: user.id })
    await activityRepository.save(activity)

    useCase = new GetActivities(activityRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to get activities', async () => {
    const result = await execute(useCase, { collectionId }, user)

    expect(result.items.length).toEqual(1)
    expect(result.items[0]?.collectionId).toEqual(activity.collectionId.value)
    expect(result.items[0]?.userId).toEqual(activity.userId.value)
    expect(result.items[0]?.type).toEqual(activity.type)
  })
})
