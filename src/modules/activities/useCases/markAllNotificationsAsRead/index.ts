import { notificationRepository } from '../../repositories'
import { MarkAllNotificationsAsReadController } from './controller'
import { MarkAllNotificationsAsRead } from './markAllNotificationsAsRead'

const useCase = new MarkAllNotificationsAsRead(notificationRepository)
const markAllNotificationsAsReadController =
  new MarkAllNotificationsAsReadController(useCase)

export { markAllNotificationsAsReadController }
