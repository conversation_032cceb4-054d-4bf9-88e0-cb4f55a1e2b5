import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'

class MarkAllNotificationsAsRead extends UseCase<void, void> {
  constructor(
    private readonly _notificationRepository: GenericNotificationRepository,
  ) {
    super(MarkAllNotificationsAsRead.name)
  }

  async execute(): Promise<void> {
    const currentUser = AuthContext.getUser()

    await this._notificationRepository.setAllRead(currentUser.id)
  }
}

export { MarkAllNotificationsAsRead }
