import { constants } from 'http2'
import { InferType, object } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { MarkAllNotificationsAsRead } from './markAllNotificationsAsRead'

const schema = object({ params: object() })

class MarkAllNotificationsAsReadController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  undefined
> {
  public constructor(useCase: MarkAllNotificationsAsRead) {
    super(
      'patch',
      'all-read',
      constants.HTTP_STATUS_NO_CONTENT,
      useCase,
      schema,
    )
  }

  protected parseInput(): undefined {
    return
  }
}

export { MarkAllNotificationsAsReadController }
