import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { MarkAllNotificationsAsRead } from '../markAllNotificationsAsRead'

let useCase: MarkAllNotificationsAsRead
let user: User
const notificationIds: UniqueEntityID[] = []

describe('MarkAllNotificationsAsRead', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser()
    await userRepository.save(user)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    const activity = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity)

    const oneNotification = stubNotification({
      activityId: activity.id,
      userId: user.id,
    })
    await notificationRepository.save(oneNotification)
    notificationIds.push(oneNotification.id)

    const anotherNotification = stubNotification({
      activityId: activity.id,
      userId: user.id,
    })
    await notificationRepository.save(anotherNotification)
    notificationIds.push(anotherNotification.id)

    useCase = new MarkAllNotificationsAsRead(notificationRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should be able to mark all notifications as read', async () => {
    await expect(execute(useCase, undefined, user)).resolves.not.toThrow()

    for (const notificationId of notificationIds) {
      const notification = await notificationRepository.getById(notificationId)
      // TODO: uncomment line bellow after current user logic is implemented in the use case
      expect(notification.read).toBeTruthy()
    }
  })
})
