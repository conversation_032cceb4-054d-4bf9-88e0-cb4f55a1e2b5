import { collectionRepository } from '../../../collections/repositories'
import { userRepository } from '../../../users/repositories'
import { activityRepository, notificationRepository } from '../../repositories'
import { CreateActivityWithNotifications } from './createActivityWithNotifications'

const useCase = new CreateActivityWithNotifications(
  activityRepository,
  notificationRepository,
  collectionRepository,
  userRepository,
)

export { useCase }
