import { stubEmail } from '../../../../../shared/domain/__stubs__/email.stub'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../../../../../testUtils/databaseSetup'
import { execute } from '../../../../../utils/tests/tests'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../../collections/repositories'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { User } from '../../../../users/domain/user'
import { userRepository } from '../../../../users/repositories'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import {
  activityRepository,
  notificationRepository,
} from '../../../repositories'
import { DeleteAllNotifications } from '../deleteAllNotifications'

let useCase: DeleteAllNotifications
let user: User
let otherUser: User

describe('DeleteAllNotifications', () => {
  beforeAll(async () => {
    await setupTestDatabase()

    user = stubUser({
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(user)

    otherUser = stubUser({
      email: stubEmail({ value: '<EMAIL>' }),
    })
    await userRepository.save(otherUser)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    const activity1 = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity1)

    const activity2 = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity2)

    const otherActivity = stubActivity({
      collectionId: collection.id,
      userId: otherUser.id,
    })
    await activityRepository.save(otherActivity)

    const notification1 = stubNotification({
      activityId: activity1.id,
      userId: user.id,
    })
    await notificationRepository.save(notification1)

    const notification2 = stubNotification({
      activityId: activity2.id,
      userId: user.id,
    })
    await notificationRepository.save(notification2)

    const otherUserNotification = stubNotification({
      activityId: otherActivity.id,
      userId: otherUser.id,
    })
    await notificationRepository.save(otherUserNotification)

    useCase = new DeleteAllNotifications(notificationRepository)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  it('Should delete all notifications for the authenticated user', async () => {
    const initialUserNotifications = await notificationRepository.getByUserId(
      user.id,
    )
    expect(initialUserNotifications.items.length).toBeGreaterThan(0)

    const initialOtherUserNotifications =
      await notificationRepository.getByUserId(otherUser.id)
    expect(initialOtherUserNotifications.items.length).toBeGreaterThan(0)

    await expect(execute(useCase, undefined, user)).resolves.not.toThrow()

    const userNotificationsAfter = await notificationRepository.getByUserId(
      user.id,
    )
    expect(userNotificationsAfter.items.length).toBe(0)

    const otherUserNotificationsAfter =
      await notificationRepository.getByUserId(otherUser.id)
    expect(otherUserNotificationsAfter.items.length).toBe(
      initialOtherUserNotifications.items.length,
    )
  })
})
