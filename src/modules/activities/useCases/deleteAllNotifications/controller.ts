import { constants } from 'http2'
import { InferType, object } from 'yup'
import { Controller } from '../../../../shared/infra/controller'
import { DeleteAllNotifications } from './deleteAllNotifications'

const schema = object({ params: object() })

class DeleteAllNotificationsController extends Controller<
  InferType<typeof schema>['params'],
  undefined,
  undefined,
  undefined
> {
  public constructor(useCase: DeleteAllNotifications) {
    super('delete', '', constants.HTTP_STATUS_NO_CONTENT, useCase, schema)
  }

  protected parseInput(): undefined {
    return
  }
}

export { DeleteAllNotificationsController }
