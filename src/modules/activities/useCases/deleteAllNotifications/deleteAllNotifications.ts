import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'

class DeleteAllNotifications extends UseCase<void, void> {
  constructor(
    private readonly _notificationRepository: GenericNotificationRepository,
  ) {
    super(DeleteAllNotifications.name)
  }

  async execute(): Promise<void> {
    const currentUser = AuthContext.getUser()

    await this._notificationRepository.deleteAllByUserId(currentUser.id)
  }
}

export { DeleteAllNotifications }
