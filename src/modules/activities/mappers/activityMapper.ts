import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Activity } from '../domain/activity'
import { ActivityDTO } from '../dto/activityDTO'
import { ActivityModel } from '../models/activity.model'

class ActivityMapper implements Mapper<Activity, ActivityModel, ActivityDTO> {
  private static _instance: ActivityMapper

  toDomain(model: ActivityModel): Activity {
    return new Activity(
      {
        collectionId: new UniqueEntityID(model.collectionId),
        userId: new UniqueEntityID(model.userId),
        type: model.type,
        data: model.data,
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt ?? undefined,
      model.deleted,
    )
  }

  toModel(domain: Activity): ActivityModel {
    return {
      id: domain.id.value,
      userId: domain.userId.value,
      collectionId: domain.collectionId.value,
      type: domain.type,
      data: domain.data,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
      deleted: domain.deleted,
    }
  }

  toDTO(domain: Activity): ActivityDTO {
    return {
      id: domain.id.value,
      userId: domain.userId.value,
      collectionId: domain.collectionId.value,
      type: domain.type,
      data: domain.data,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
      deleted: domain.deleted,
    }
  }

  toPageDTO(
    domain: PaginatedDomain<Activity>,
  ): PaginatedResponseDTO<ActivityDTO> {
    return {
      items: domain.items.map(this.toDTO),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!ActivityMapper._instance) {
      ActivityMapper._instance = new ActivityMapper()
    }
    return ActivityMapper._instance
  }
}

export { ActivityMapper }
