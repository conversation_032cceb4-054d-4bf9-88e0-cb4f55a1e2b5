import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { PaginatedResponseDTO } from '../../../shared/dto/paginatedResponseDTO'
import { Mapper } from '../../../shared/mappers'
import { PaginatedDomain } from '../../../shared/types/pagination'
import { Notification } from '../domain/notification'
import { NotificationDTO } from '../dto/notificationDTO'
import { NotificationModel } from '../models/notification.model'

class NotificationMapper
  implements Mapper<Notification, NotificationModel, NotificationDTO>
{
  private static _instance: NotificationMapper

  toDomain(model: NotificationModel): Notification {
    return new Notification(
      {
        userId: new UniqueEntityID(model.userId),
        activityId: new UniqueEntityID(model.activityId),
        read: !!model.read,
        sent: !!model.sent,
      },
      new UniqueEntityID(model.id),
      model.createdAt,
      model.updatedAt ?? undefined,
      model.deleted,
    )
  }

  toModel(domain: Notification): NotificationModel {
    return {
      id: domain.id.value,
      userId: domain.userId.value,
      activityId: domain.activityId.value,
      read: domain.read,
      sent: domain.sent,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt ?? null,
      deleted: domain.deleted,
    }
  }

  toDTO(domain: Notification): NotificationDTO {
    return {
      id: domain.id.value,
      activityId: domain.activityId.value,
      read: domain.read,
      sent: domain.sent,
      userId: domain.userId.value,
      createdAt: domain.createdAt.toISOString(),
      updatedAt: domain.updatedAt?.toISOString(),
      deleted: domain.deleted,
    }
  }

  toPageDTO(
    domain: PaginatedDomain<Notification>,
  ): PaginatedResponseDTO<NotificationDTO> {
    return {
      items: domain.items.map(this.toDTO),
      totalItems: domain.total,
    }
  }

  static getInstance() {
    if (!NotificationMapper._instance) {
      NotificationMapper._instance = new NotificationMapper()
    }
    return NotificationMapper._instance
  }
}

export { NotificationMapper }
