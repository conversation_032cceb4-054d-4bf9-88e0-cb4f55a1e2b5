import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { User } from '../../../users/domain/user'
import { Role } from '../../../users/types/user'

export class UserRemovedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:userRemoved'
  public readonly user: User
  public readonly activityType: string
  public readonly activityData: {
    targetUserId: string
    collectionId: string
    changedBy: string
    removedRole: Role
  }
  public readonly collectionId: UniqueEntityID
  public readonly removedBy: UniqueEntityID
  public readonly removedRole: Role

  constructor(
    user: User,
    collectionId: UniqueEntityID,
    removedBy: UniqueEntityID,
    removedRole: Role,
  ) {
    super(UserRemovedEvent.eventIdentifier)
    this.user = user
    this.collectionId = collectionId
    this.removedBy = removedBy
    this.removedRole = removedRole
    this.activityType = 'USER_REMOVED'
    this.activityData = {
      targetUserId: user.id.value,
      collectionId: collectionId.value,
      changedBy: removedBy.value,
      removedRole,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
