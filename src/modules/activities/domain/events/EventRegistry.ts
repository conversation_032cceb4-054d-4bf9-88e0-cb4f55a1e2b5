/**
 * Centralized registry of all activity domain event identifiers.
 * Provides type-safe constants for event identification and prevents typos.
 *
 * Using 'as const' assertion to create literal types for better type safety.
 */
export const ACTIVITY_EVENT_IDENTIFIERS = {
  ENTRY_CREATED: 'activities:entryCreated',
  ENTRY_UPDATED: 'activities:entryUpdated',
  CHANGE_CREATED: 'activities:changeCreated',
  CHANGE_APPROVED: 'activities:changeApproved',
  CHANGE_REJECTED: 'activities:changeRejected',
  USER_ROLE_CHANGED: 'activities:userRoleChanged',
  USER_REMOVED: 'activities:userRemoved',
  NOTIFICATION_CREATED: 'activities:notificationCreated',
} as const

/**
 * Type representing all valid activity event identifier strings.
 * Derived from the ACTIVITY_EVENT_IDENTIFIERS constant for type safety.
 */
export type ActivityEventIdentifier =
  (typeof ACTIVITY_EVENT_IDENTIFIERS)[keyof typeof ACTIVITY_EVENT_IDENTIFIERS]

/**
 * Activity types that correspond to the different kinds of activities
 * that can be created in the system. These map to the 'type' field
 * in the Activity entity and are used by activity handlers.
 */
export const ACTIVITY_TYPES = {
  ENTRY_CREATE: 'ENTRY_CREATE',
  ENTRY_UPDATE: 'ENTRY_UPDATE',
  CHANGE_CREATE: 'CHANGE_CREATE',
  CHANGE_APPROVE: 'CHANGE_APPROVE',
  CHANGE_REJECT: 'CHANGE_REJECT',
  ROLE_CHANGE: 'ROLE_CHANGE',
  USER_REMOVED: 'USER_REMOVED',
  NOTIFICATION_EMAIL_DISPATCH: 'NOTIFICATION_EMAIL_DISPATCH',
} as const

/**
 * Type representing all valid activity type strings.
 * Used for type-safe activity type handling throughout the system.
 */
export type ActivityType = (typeof ACTIVITY_TYPES)[keyof typeof ACTIVITY_TYPES]

/**
 * Mapping between event identifiers and their corresponding activity types.
 * Useful for handlers and activity creation logic.
 */
export const EVENT_TO_ACTIVITY_TYPE_MAP: Record<
  ActivityEventIdentifier,
  ActivityType
> = {
  [ACTIVITY_EVENT_IDENTIFIERS.ENTRY_CREATED]: ACTIVITY_TYPES.ENTRY_CREATE,
  [ACTIVITY_EVENT_IDENTIFIERS.ENTRY_UPDATED]: ACTIVITY_TYPES.ENTRY_UPDATE,
  [ACTIVITY_EVENT_IDENTIFIERS.CHANGE_CREATED]: ACTIVITY_TYPES.CHANGE_CREATE,
  [ACTIVITY_EVENT_IDENTIFIERS.CHANGE_APPROVED]: ACTIVITY_TYPES.CHANGE_APPROVE,
  [ACTIVITY_EVENT_IDENTIFIERS.CHANGE_REJECTED]: ACTIVITY_TYPES.CHANGE_REJECT,
  [ACTIVITY_EVENT_IDENTIFIERS.USER_ROLE_CHANGED]: ACTIVITY_TYPES.ROLE_CHANGE,
  [ACTIVITY_EVENT_IDENTIFIERS.USER_REMOVED]: ACTIVITY_TYPES.USER_REMOVED,
  [ACTIVITY_EVENT_IDENTIFIERS.NOTIFICATION_CREATED]:
    ACTIVITY_TYPES.NOTIFICATION_EMAIL_DISPATCH,
}

/**
 * Utility function to get the activity type for a given event identifier.
 * Provides type-safe conversion from event identifiers to activity types.
 *
 * @param eventIdentifier - The event identifier to convert
 * @returns The corresponding activity type
 */
export function getActivityTypeForEvent(
  eventIdentifier: ActivityEventIdentifier,
): ActivityType {
  return EVENT_TO_ACTIVITY_TYPE_MAP[eventIdentifier]
}
