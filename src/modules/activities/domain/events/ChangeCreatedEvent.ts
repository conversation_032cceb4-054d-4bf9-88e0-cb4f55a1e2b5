import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeCreatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeCreated'
  public readonly change: Change
  public readonly targetUsers: UniqueEntityID[]
  public readonly activityType: string
  public readonly activityData: unknown
  public readonly collectionId: UniqueEntityID

  constructor(
    change: Change,
    collectionId: UniqueEntityID,
    targetUsers: UniqueEntityID[],
    activityData?: unknown,
  ) {
    super(ChangeCreatedEvent.eventIdentifier)
    this.change = change
    this.collectionId = collectionId
    this.targetUsers = targetUsers
    this.activityType = 'CHANGE_CREATE'
    this.activityData = activityData || {
      changeId: change.id.value,
      entryId: change.entryId.value,
      collectionId: collectionId.value,
      createdBy: change.createdBy.value,
      status: change.status,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
