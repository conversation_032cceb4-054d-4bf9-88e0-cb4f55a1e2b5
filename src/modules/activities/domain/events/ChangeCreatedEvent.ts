import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeCreatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeCreated'
  public readonly change: Change
  public readonly activityType: string
  public readonly activityData: unknown

  constructor(change: Change, activityData?: unknown) {
    super(ChangeCreatedEvent.eventIdentifier)
    this.change = change
    this.activityType = 'CHANGE_CREATE'
    this.activityData = activityData || {
      changeId: change.id.value,
      entryId: change.entryId.value,
      createdBy: change.createdBy.value,
      status: change.status,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
