import { Role } from '../../../../users/types/user'

/**
 * Base interface for all activity data structures.
 * Provides common metadata fields that can be extended.
 */
export interface BaseActivityData {
  readonly timestamp?: Date
  readonly version?: string
}

/**
 * Activity data for entry creation events.
 * Contains information about the newly created entry.
 */
export interface EntryActivityData extends BaseActivityData {
  readonly entryId: string
  readonly collectionId: string
  readonly createdBy: string
  readonly visibility: 'PUBLIC' | 'PRIVATE'
}

/**
 * Activity data for entry update events.
 * Contains information about the updated entry and who updated it.
 */
export interface EntryUpdateActivityData extends BaseActivityData {
  readonly entryId: string
  readonly collectionId: string
  readonly updatedBy: string
  readonly visibility: 'PUBLIC' | 'PRIVATE'
  readonly previousVisibility?: 'PUBLIC' | 'PRIVATE'
}

/**
 * Activity data for change creation events.
 * Contains information about the newly proposed change.
 */
export interface ChangeActivityData extends BaseActivityData {
  readonly changeId: string
  readonly entryId: string
  readonly createdBy: string
  readonly status: 'PENDING' | 'ACCEPTED' | 'REJECTED'
}

/**
 * Activity data for change approval events.
 * Contains detailed information about the approval decision.
 */
export interface ChangeApprovalActivityData extends BaseActivityData {
  readonly changeId: string
  readonly entryId: string
  readonly approvedBy: string
  readonly createdBy: string
  readonly reviewerMessage?: string
  readonly data: unknown
}

/**
 * Activity data for change rejection events.
 * Contains detailed information about the rejection decision.
 */
export interface ChangeRejectionActivityData extends BaseActivityData {
  readonly changeId: string
  readonly entryId: string
  readonly rejectedBy: string
  readonly createdBy: string
  readonly reviewerMessage?: string
  readonly data: unknown
}

/**
 * Activity data for user role change events.
 * Contains information about role transitions within collections.
 */
export interface UserRoleActivityData extends BaseActivityData {
  readonly targetUserId: string
  readonly collectionId: string
  readonly changedBy: string
  readonly oldRole: Role
  readonly newRole: Role
}

/**
 * Activity data for user removal events.
 * Contains information about users being removed from collections.
 */
export interface UserRemovedActivityData extends BaseActivityData {
  readonly targetUserId: string
  readonly collectionId: string
  readonly changedBy: string
  readonly removedRole: Role
}

/**
 * Activity data for notification creation events.
 * Contains information about notifications being created for users.
 */
export interface NotificationActivityData extends BaseActivityData {
  readonly notificationId: string
  readonly userId: string
  readonly activityId: string
}

/**
 * Union type representing all possible activity data structures.
 * Useful for type-safe handling of activity data across the system.
 */
export type ActivityData =
  | EntryActivityData
  | EntryUpdateActivityData
  | ChangeActivityData
  | ChangeApprovalActivityData
  | ChangeRejectionActivityData
  | UserRoleActivityData
  | UserRemovedActivityData
  | NotificationActivityData
