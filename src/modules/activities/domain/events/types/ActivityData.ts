import { Role } from '../../../../users/types/user'

export interface BaseActivityData {
  readonly timestamp?: Date
}

export interface EntryActivityData extends BaseActivityData {
  readonly entryId: string
  readonly collectionId: string
  readonly createdBy: string
  readonly visibility: 'PUBLIC' | 'PRIVATE'
}

export interface EntryUpdateActivityData extends BaseActivityData {
  readonly entryId: string
  readonly collectionId: string
  readonly updatedBy: string
  readonly visibility: 'PUBLIC' | 'PRIVATE'
  readonly previousVisibility?: 'PUBLIC' | 'PRIVATE'
}

export interface ChangeActivityData extends BaseActivityData {
  readonly changeId: string
  readonly entryId: string
  readonly createdBy: string
  readonly status: 'PENDING' | 'ACCEPTED' | 'REJECTED'
}

export interface ChangeApprovalActivityData extends BaseActivityData {
  readonly changeId: string
  readonly entryId: string
  readonly approvedBy: string
  readonly createdBy: string
  readonly reviewerMessage?: string
  readonly data: unknown
}

export interface ChangeRejectionActivityData extends BaseActivityData {
  readonly changeId: string
  readonly entryId: string
  readonly rejectedBy: string
  readonly createdBy: string
  readonly reviewerMessage?: string
  readonly data: unknown
}

export interface UserRoleActivityData extends BaseActivityData {
  readonly targetUserId: string
  readonly collectionId: string
  readonly changedBy: string
  readonly oldRole: Role
  readonly newRole: Role
}

export interface UserRemovedActivityData extends BaseActivityData {
  readonly targetUserId: string
  readonly collectionId: string
  readonly changedBy: string
  readonly removedRole: Role
}

export interface NotificationActivityData extends BaseActivityData {
  readonly notificationId: string
  readonly userId: string
  readonly activityId: string
}

export type ActivityData =
  | EntryActivityData
  | EntryUpdateActivityData
  | ChangeActivityData
  | ChangeApprovalActivityData
  | ChangeRejectionActivityData
  | UserRoleActivityData
  | UserRemovedActivityData
  | NotificationActivityData
