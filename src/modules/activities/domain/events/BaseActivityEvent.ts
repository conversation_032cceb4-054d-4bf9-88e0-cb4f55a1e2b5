import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'

/**
 * Abstract base class for all activity domain events.
 * Provides common functionality and type safety for activity events.
 *
 * @template TEntity - The domain entity type that triggered this event
 * @template TActivityData - The strongly-typed activity data structure
 */
export abstract class BaseActivityEvent<
  TEntity,
  TActivityData = unknown,
> extends DomainEvent {
  public readonly activityType: string
  public readonly entity: TEntity

  private _activityData?: TActivityData
  private readonly _activityDataFactory: (entity: TEntity) => TActivityData
  private readonly _customActivityData?: TActivityData

  constructor(
    eventIdentifier: string,
    activityType: string,
    entity: TEntity,
    activityDataFactory: (entity: TEntity) => TActivityData,
    customActivityData?: TActivityData,
  ) {
    super(eventIdentifier)
    this.activityType = activityType
    this.entity = entity
    this._activityDataFactory = activityDataFactory
    this._customActivityData = customActivityData
  }

  /**
   * Lazy-loaded activity data. Only computed when first accessed.
   * Uses custom data if provided, otherwise generates from entity.
   */
  public get activityData(): TActivityData {
    if (!this._activityData) {
      this._activityData =
        this._customActivityData || this._activityDataFactory(this.entity)
    }
    return this._activityData
  }

  /**
   * Gets the entity ID for domain event dispatching.
   * Assumes entity has an 'id' property of type UniqueEntityID.
   */
  public getEntityId(): UniqueEntityID {
    return (this.entity as any).id
  }
}
