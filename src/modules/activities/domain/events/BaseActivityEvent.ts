import { Entity } from '../../../../shared/domain/entity'
import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'

export abstract class BaseActivityEvent<
  TEntity extends Entity<unknown>,
  TActivityData = unknown,
> extends DomainEvent {
  public readonly activityType: string
  public readonly entity: TEntity

  private _activityData?: TActivityData
  private readonly _activityDataFactory: (entity: TEntity) => TActivityData
  private readonly _customActivityData?: TActivityData

  constructor(
    eventIdentifier: string,
    activityType: string,
    entity: TEntity,
    activityDataFactory: (entity: TEntity) => TActivityData,
    customActivityData?: TActivityData,
  ) {
    super(eventIdentifier)
    this.activityType = activityType
    this.entity = entity
    this._activityDataFactory = activityDataFactory
    this._customActivityData = customActivityData
  }

  public get activityData(): TActivityData {
    if (!this._activityData) {
      this._activityData =
        this._customActivityData || this._activityDataFactory(this.entity)
    }
    return this._activityData
  }

  public getEntityId(): UniqueEntityID {
    return this.entity.id
  }
}
