import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeApprovedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeApproved'
  public readonly change: Change
  public readonly activityType: string
  public readonly activityData: unknown

  constructor(change: Change, activityData?: unknown) {
    super(ChangeApprovedEvent.eventIdentifier)
    this.change = change
    this.activityType = 'CHANGE_APPROVE'
    this.activityData = activityData || {
      changeId: change.id.value,
      entryId: change.entryId.value,
      approvedBy: change.reviewedBy?.value,
      originalCreatedBy: change.createdBy.value,
      reviewerMessage: change.reviewerMessage,
      status: change.status,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
