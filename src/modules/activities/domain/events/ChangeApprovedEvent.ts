import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeApprovedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeApproved'
  public readonly change: Change
  public readonly activityType: string = 'CHANGE_APPROVE'
  public readonly activityData: {
    changeId: string
    entryId: string
    approvedBy: string | undefined
    createdBy: string
    reviewerMessage: string | undefined
    data: unknown
  }

  constructor(change: Change) {
    super(ChangeApprovedEvent.eventIdentifier)
    this.change = change
    this.activityData = {
      changeId: change.id.value,
      entryId: change.entryId.value,
      approvedBy: change.reviewedBy?.value || 'unknown',
      createdBy: change.createdBy.value,
      reviewerMessage: change.reviewerMessage,
      data: change.data,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
