import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeApprovedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeApproved'
  public readonly change: Change
  public readonly targetUsers: UniqueEntityID[]
  public readonly activityType: string
  public readonly activityData: unknown
  public readonly collectionId: UniqueEntityID
  public readonly approvedBy: UniqueEntityID

  constructor(
    change: Change,
    collectionId: UniqueEntityID,
    approvedBy: UniqueEntityID,
    targetUsers: UniqueEntityID[],
    activityData?: unknown,
  ) {
    super(ChangeApprovedEvent.eventIdentifier)
    this.change = change
    this.collectionId = collectionId
    this.approvedBy = approvedBy
    this.targetUsers = targetUsers
    this.activityType = 'CHANGE_APPROVE'
    this.activityData = activityData || {
      changeId: change.id.value,
      entryId: change.entryId.value,
      collectionId: collectionId.value,
      approvedBy: approvedBy.value,
      originalCreatedBy: change.createdBy.value,
      reviewerMessage: change.reviewerMessage,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
