import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Notification } from '../notification'
import { BaseActivityEvent } from './BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from './EventRegistry'
import { NotificationActivityData } from './types/ActivityData'

export class NotificationCreatedEvent extends BaseActivityEvent<
  Notification,
  NotificationActivityData
> {
  public static readonly eventIdentifier: string =
    ACTIVITY_EVENT_IDENTIFIERS.NOTIFICATION_CREATED

  constructor(notification: Notification, activityData?: unknown) {
    super(
      NotificationCreatedEvent.eventIdentifier,
      ACTIVITY_TYPES.NOTIFICATION_EMAIL_DISPATCH,
      notification,
      (notification): NotificationActivityData => ({
        notificationId: notification.id.value,
        userId: notification.userId.value,
        activityId: notification.activityId.value,
      }),
      activityData as NotificationActivityData,
    )
  }

  // Backward compatibility: maintain the same public interface
  public get notification(): Notification {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.notification.id
  }
}
