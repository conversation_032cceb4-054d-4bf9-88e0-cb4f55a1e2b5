import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Notification } from '../notification'

export class NotificationCreatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string =
    'activities:notificationCreated'
  public readonly notification: Notification
  public readonly activityType: string
  public readonly activityData: unknown

  constructor(notification: Notification, activityData?: unknown) {
    super(NotificationCreatedEvent.eventIdentifier)
    this.notification = notification
    this.activityType = 'NOTIFICATION_EMAIL_DISPATCH'
    this.activityData = activityData || {
      notificationId: notification.id.value,
      userId: notification.userId.value,
      activityId: notification.activityId.value,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.notification.id
  }
}
