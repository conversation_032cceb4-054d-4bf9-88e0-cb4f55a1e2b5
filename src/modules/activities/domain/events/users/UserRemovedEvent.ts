import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { User } from '../../../../users/domain/user'
import { Role } from '../../../../users/types/user'
import { BaseActivityEvent } from '../BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from '../EventRegistry'
import { UserRemovedActivityData } from '../types/ActivityData'

export class UserRemovedEvent extends BaseActivityEvent<
  User,
  UserRemovedActivityData
> {
  public static readonly eventIdentifier: typeof ACTIVITY_EVENT_IDENTIFIERS.USER_REMOVED =
    ACTIVITY_EVENT_IDENTIFIERS.USER_REMOVED
  public readonly collectionId: UniqueEntityID
  public readonly removedBy: UniqueEntityID
  public readonly removedRole: Role

  constructor(
    user: User,
    collectionId: UniqueEntityID,
    removedBy: UniqueEntityID,
    removedRole: Role,
  ) {
    super(
      UserRemovedEvent.eventIdentifier,
      ACTIVITY_TYPES.USER_REMOVED,
      user,
      (): UserRemovedActivityData => ({
        targetUserId: user.id.value,
        collectionId: collectionId.value,
        changedBy: removedBy.value,
        removedRole,
      }),
    )
    this.collectionId = collectionId
    this.removedBy = removedBy
    this.removedRole = removedRole
  }

  public get user(): User {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
