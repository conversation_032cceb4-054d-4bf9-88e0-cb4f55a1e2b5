import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeRejectedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeRejected'
  public readonly change: Change
  public readonly activityType: string = 'CHANGE_REJECT'
  public readonly activityData: {
    changeId: string
    entryId: string
    rejectedBy: string | undefined
    createdBy: string
    reviewerMessage: string | undefined
    data: unknown
  }

  constructor(change: Change) {
    super(ChangeRejectedEvent.eventIdentifier)
    this.change = change
    this.activityData = {
      changeId: change.id.value,
      entryId: change.entryId.value,
      rejectedBy: change.reviewedBy?.value || 'unknown',
      createdBy: change.createdBy.value,
      reviewerMessage: change.reviewerMessage,
      data: change.data,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
