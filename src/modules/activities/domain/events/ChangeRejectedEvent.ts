import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../entries/domain/change'

export class ChangeRejectedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:changeRejected'
  public readonly change: Change
  public readonly targetUsers: UniqueEntityID[]
  public readonly activityType: string
  public readonly activityData: unknown
  public readonly collectionId: UniqueEntityID
  public readonly rejectedBy: UniqueEntityID

  constructor(
    change: Change,
    collectionId: UniqueEntityID,
    rejectedBy: UniqueEntityID,
    targetUsers: UniqueEntityID[],
    activityData?: unknown,
  ) {
    super(ChangeRejectedEvent.eventIdentifier)
    this.change = change
    this.collectionId = collectionId
    this.rejectedBy = rejectedBy
    this.targetUsers = targetUsers
    this.activityType = 'CHANGE_REJECT'
    this.activityData = activityData || {
      changeId: change.id.value,
      entryId: change.entryId.value,
      collectionId: collectionId.value,
      rejectedBy: rejectedBy.value,
      originalCreatedBy: change.createdBy.value,
      reviewerMessage: change.reviewerMessage,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
