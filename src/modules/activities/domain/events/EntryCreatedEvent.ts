import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Entry } from '../../../entries/domain/entry'
import { BaseActivityEvent } from './BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from './EventRegistry'
import { EntryActivityData } from './types/ActivityData'

export class EntryCreatedEvent extends BaseActivityEvent<
  Entry,
  EntryActivityData
> {
  public static readonly eventIdentifier: string =
    ACTIVITY_EVENT_IDENTIFIERS.ENTRY_CREATED

  constructor(entry: Entry, activityData?: unknown) {
    super(
      EntryCreatedEvent.eventIdentifier,
      ACTIVITY_TYPES.ENTRY_CREATE,
      entry,
      (entry): EntryActivityData => ({
        entryId: entry.id.value,
        collectionId: entry.collectionId.value,
        createdBy: entry.createdBy.value,
        visibility: entry.visibility,
      }),
      activityData as EntryActivityData,
    )
  }

  // Backward compatibility: maintain the same public interface
  public get entry(): Entry {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
