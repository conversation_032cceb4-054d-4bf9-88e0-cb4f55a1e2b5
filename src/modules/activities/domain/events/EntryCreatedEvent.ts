import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Entry } from '../../../entries/domain/entry'

export class EntryCreatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:entryCreated'
  public readonly entry: Entry
  public readonly activityType: string
  public readonly activityData: unknown

  constructor(entry: Entry, activityData?: unknown) {
    super(EntryCreatedEvent.eventIdentifier)
    this.entry = entry
    this.activityType = 'ENTRY_CREATE'
    this.activityData = activityData || {
      entryId: entry.id.value,
      collectionId: entry.collectionId.value,
      createdBy: entry.createdBy.value,
      visibility: entry.visibility,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
