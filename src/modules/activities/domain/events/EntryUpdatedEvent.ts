import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Entry } from '../../../entries/domain/entry'
import { BaseActivityEvent } from './BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from './EventRegistry'
import { EntryUpdateActivityData } from './types/ActivityData'

export class EntryUpdatedEvent extends BaseActivityEvent<
  Entry,
  EntryUpdateActivityData
> {
  public static readonly eventIdentifier: string =
    ACTIVITY_EVENT_IDENTIFIERS.ENTRY_UPDATED
  public readonly updatedBy: UniqueEntityID

  constructor(entry: Entry, updatedBy: UniqueEntityID, activityData?: unknown) {
    super(
      EntryUpdatedEvent.eventIdentifier,
      ACTIVITY_TYPES.ENTRY_UPDATE,
      entry,
      (entry): EntryUpdateActivityData => ({
        entryId: entry.id.value,
        collectionId: entry.collectionId.value,
        updatedBy: updatedBy.value,
        visibility: entry.visibility,
      }),
      activityData as EntryUpdateActivityData,
    )
    this.updatedBy = updatedBy
  }

  // Backward compatibility: maintain the same public interface
  public get entry(): Entry {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
