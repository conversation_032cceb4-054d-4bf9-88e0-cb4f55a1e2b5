import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Entry } from '../../../entries/domain/entry'

export class EntryUpdatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:entryUpdated'
  public readonly entry: Entry
  public readonly targetUsers: UniqueEntityID[]
  public readonly activityType: string
  public readonly activityData: unknown
  public readonly updatedBy: UniqueEntityID

  constructor(
    entry: Entry,
    updatedBy: UniqueEntityID,
    targetUsers: UniqueEntityID[],
    activityData?: unknown,
  ) {
    super(EntryUpdatedEvent.eventIdentifier)
    this.entry = entry
    this.updatedBy = updatedBy
    this.targetUsers = targetUsers
    this.activityType = 'ENTRY_UPDATE'
    this.activityData = activityData || {
      entryId: entry.id.value,
      collectionId: entry.collectionId.value,
      updatedBy: updatedBy.value,
      visibility: entry.visibility,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
