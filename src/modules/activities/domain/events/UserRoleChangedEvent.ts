import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { User } from '../../../users/domain/user'
import { Role } from '../../../users/types/user'
import { BaseActivityEvent } from './BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from './EventRegistry'
import { UserRoleActivityData } from './types/ActivityData'

export class UserRoleChangedEvent extends BaseActivityEvent<
  User,
  UserRoleActivityData
> {
  public static readonly eventIdentifier: string =
    ACTIVITY_EVENT_IDENTIFIERS.USER_ROLE_CHANGED
  public readonly collectionId: UniqueEntityID
  public readonly changedBy: UniqueEntityID
  public readonly oldRole: Role
  public readonly newRole: Role

  constructor(
    user: User,
    collectionId: UniqueEntityID,
    changedBy: UniqueEntityID,
    oldRole: Role,
    newRole: Role,
    activityData?: unknown,
  ) {
    super(
      UserRoleChangedEvent.eventIdentifier,
      ACTIVITY_TYPES.ROLE_CHANGE,
      user,
      (): UserRoleActivityData => ({
        targetUserId: user.id.value,
        collectionId: collectionId.value,
        changedBy: changedBy.value,
        oldRole,
        newRole,
      }),
      activityData as UserRoleActivityData,
    )
    this.collectionId = collectionId
    this.changedBy = changedBy
    this.oldRole = oldRole
    this.newRole = newRole
  }

  // Backward compatibility: maintain the same public interface
  public get user(): User {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
