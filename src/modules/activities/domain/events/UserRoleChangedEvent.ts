import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { User } from '../../../users/domain/user'
import { Role } from '../../../users/types/user'

export class UserRoleChangedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'activities:userRoleChanged'
  public readonly user: User
  public readonly activityType: string
  public readonly activityData: unknown
  public readonly collectionId: UniqueEntityID
  public readonly changedBy: UniqueEntityID
  public readonly oldRole: Role
  public readonly newRole: Role

  constructor(
    user: User,
    collectionId: UniqueEntityID,
    changedBy: UniqueEntityID,
    oldRole: Role,
    newRole: Role,
    activityData?: unknown,
  ) {
    super(UserRoleChangedEvent.eventIdentifier)
    this.user = user
    this.collectionId = collectionId
    this.changedBy = changedBy
    this.oldRole = oldRole
    this.newRole = newRole
    this.activityType = 'ROLE_CHANGE'
    this.activityData = activityData || {
      targetUserId: user.id.value,
      collectionId: collectionId.value,
      changedBy: changedBy.value,
      oldRole,
      newRole,
    }
  }

  public getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
