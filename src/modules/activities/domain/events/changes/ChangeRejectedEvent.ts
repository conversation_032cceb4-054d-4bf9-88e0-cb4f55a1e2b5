import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../../entries/domain/change'
import { BaseActivityEvent } from '../BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from '../EventRegistry'
import { ChangeRejectionActivityData } from '../types/ActivityData'

export class ChangeRejectedEvent extends BaseActivityEvent<
  Change,
  ChangeRejectionActivityData
> {
  public static readonly eventIdentifier: typeof ACTIVITY_EVENT_IDENTIFIERS.CHANGE_REJECTED =
    ACTIVITY_EVENT_IDENTIFIERS.CHANGE_REJECTED

  constructor(change: Change, activityData?: unknown) {
    super(
      ChangeRejectedEvent.eventIdentifier,
      ACTIVITY_TYPES.CHANGE_REJECT,
      change,
      (change): ChangeRejectionActivityData => ({
        changeId: change.id.value,
        entryId: change.entryId.value,
        createdBy: change.createdBy.value,
        rejectedBy: change.reviewedBy?.value || 'unknown',
        reviewerMessage: change.reviewerMessage || '',
        data: change.data,
      }),
      activityData as ChangeRejectionActivityData,
    )
  }

  public get change(): Change {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
