import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../../entries/domain/change'
import { BaseActivityEvent } from '../BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from '../EventRegistry'
import { ChangeActivityData } from '../types/ActivityData'

export class ChangeCreatedEvent extends BaseActivityEvent<
  Change,
  ChangeActivityData
> {
  public static readonly eventIdentifier: string =
    ACTIVITY_EVENT_IDENTIFIERS.CHANGE_CREATED

  constructor(change: Change, activityData?: unknown) {
    super(
      ChangeCreatedEvent.eventIdentifier,
      ACTIVITY_TYPES.CHANGE_CREATE,
      change,
      (change): ChangeActivityData => ({
        changeId: change.id.value,
        entryId: change.entryId.value,
        createdBy: change.createdBy.value,
        status: change.status,
      }),
      activityData as ChangeActivityData,
    )
  }

  public get change(): Change {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
