import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { Change } from '../../../../entries/domain/change'
import { BaseActivityEvent } from '../BaseActivityEvent'
import { ACTIVITY_EVENT_IDENTIFIERS, ACTIVITY_TYPES } from '../EventRegistry'
import { ChangeApprovalActivityData } from '../types/ActivityData'

export class ChangeApprovedEvent extends BaseActivityEvent<
  Change,
  ChangeApprovalActivityData
> {
  public static readonly eventIdentifier: string =
    ACTIVITY_EVENT_IDENTIFIERS.CHANGE_APPROVED

  constructor(change: Change, activityData?: unknown) {
    super(
      ChangeApprovedEvent.eventIdentifier,
      ACTIVITY_TYPES.CHANGE_APPROVE,
      change,
      (change): ChangeApprovalActivityData => ({
        changeId: change.id.value,
        entryId: change.entryId.value,
        createdBy: change.createdBy.value,
        approvedBy: change.reviewedBy?.value || 'unknown',
        reviewerMessage: change.reviewerMessage || '',
        data: change.data,
      }),
      activityData as ChangeApprovalActivityData,
    )
  }

  public get change(): Change {
    return this.entity
  }

  public override getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
