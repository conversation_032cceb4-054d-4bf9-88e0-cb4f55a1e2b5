import { baseActivityStubProps, stubActivity } from '../__stubs__/activity.stub'

describe('Log', () => {
  it('should create a Log entity', () => {
    const activity = stubActivity()

    expect(activity.id).toBeDefined()
    expect(activity.userId).toBe(baseActivityStubProps.userId)
    expect(activity.data).toStrictEqual(baseActivityStubProps.data)
    expect(activity.type).toBe(baseActivityStubProps.type)
    expect(activity.collectionId).toBe(baseActivityStubProps.collectionId)
  })
})
