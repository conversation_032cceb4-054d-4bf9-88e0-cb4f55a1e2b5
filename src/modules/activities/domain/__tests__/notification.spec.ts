import {
  baseNotificationStubProps,
  stubNotification,
} from '../__stubs__/notification.stub'

describe('Notification', () => {
  it('should create a Notification entity', () => {
    const notification = stubNotification()

    expect(notification.id).toBeDefined()
    expect(notification.userId).toBe(baseNotificationStubProps.userId)
    expect(notification.activityId).toBe(baseNotificationStubProps.activityId)
    expect(notification.read).toBe(baseNotificationStubProps.read)
    expect(notification.sent).toBe(baseNotificationStubProps.sent)
  })

  it('should throw if notification already sent', () => {
    const notification = stubNotification({ sent: true })

    expect(() => notification.notify()).toThrow()
  })
})
