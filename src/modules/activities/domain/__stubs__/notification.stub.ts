import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Notification, NotificationProps } from '../notification'

const baseNotificationStubProps: NotificationProps = {
  userId: new UniqueEntityID(),
  activityId: new UniqueEntityID(),
  read: false,
  sent: false,
}

const stubNotification = (props?: Partial<NotificationProps>): Notification => {
  return new Notification({ ...baseNotificationStubProps, ...props })
}

export { stubNotification, baseNotificationStubProps }
