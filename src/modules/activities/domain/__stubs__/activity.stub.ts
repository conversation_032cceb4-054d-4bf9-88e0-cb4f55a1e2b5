import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Activity, ActivityProps } from '../activity'

const baseActivityStubProps: ActivityProps = {
  userId: new UniqueEntityID(),
  type: 'type',
  data: '',
  collectionId: new UniqueEntityID(),
}

const stubActivity = (props?: Partial<ActivityProps>): Activity => {
  return new Activity({ ...baseActivityStubProps, ...props })
}

export { stubActivity, baseActivityStubProps }
