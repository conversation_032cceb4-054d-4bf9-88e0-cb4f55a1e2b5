import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'

type ActivityProps = {
  userId: UniqueEntityID
  type: string
  data: unknown
  collectionId: UniqueEntityID
}

class Activity extends Entity<ActivityProps> {
  constructor(
    props: ActivityProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get userId() {
    return this.getProp('userId')
  }

  get type() {
    return this.getProp('type')
  }

  get data() {
    return this.getProp('data')
  }

  get collectionId() {
    return this.getProp('collectionId')
  }
}

export { Activity, type ActivityProps }
