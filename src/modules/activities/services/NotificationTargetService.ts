import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { GenericUserRepository } from '../../users/repositories/interfaces/genericUserRepository'
import { Role } from '../../users/types/user'

export type NotificationScenario =
  | 'ENTRY_CREATE'
  | 'ENTRY_UPDATE'
  | 'CHANGE_CREATE'
  | 'CHANGE_APPROVE'
  | 'CHANGE_REJECT'
  | 'ROLE_CHANGE'

export interface NotificationTargetContext {
  collectionId: UniqueEntityID
  scenario: NotificationScenario
  currentUserId: UniqueEntityID
  specificTargetUserId?: UniqueEntityID // For change approval/rejection
}

/**
 * Service to determine which users should receive notifications
 * based on the business scenario and their roles
 */
export class NotificationTargetService {
  constructor(private readonly userRepository: GenericUserRepository) {}

  /**
   * Determines target users for notifications based on the scenario
   */
  async getTargetUsers(
    context: NotificationTargetContext,
  ): Promise<UniqueEntityID[]> {
    const { collectionId, scenario, currentUserId, specificTargetUserId } =
      context

    // For change approval/rejection, only notify the original change creator
    if (
      (scenario === 'CHANGE_APPROVE' || scenario === 'CHANGE_REJECT') &&
      specificTargetUserId
    ) {
      // Don't notify if the approver/rejector is the same as the creator
      if (specificTargetUserId.equals(currentUserId)) {
        return []
      }
      return [specificTargetUserId]
    }

    // For role changes, notify the target user + collection managers
    if (scenario === 'ROLE_CHANGE' && specificTargetUserId) {
      const targetUsers: UniqueEntityID[] = []

      // Add the user whose role was changed (if not the same as current user)
      if (!specificTargetUserId.equals(currentUserId)) {
        targetUsers.push(specificTargetUserId)
      }

      // Add collection managers
      const collectionManagers = await this.getUsersByRoleInCollection(
        collectionId,
        'COLLECTION_MANAGER',
        currentUserId,
      )
      targetUsers.push(...collectionManagers)

      return this.removeDuplicates(targetUsers)
    }

    // For other scenarios, notify based on roles
    return this.getTargetUsersByRoles(collectionId, scenario, currentUserId)
  }

  /**
   * Gets users by role in a collection, excluding the current user
   */
  private async getUsersByRoleInCollection(
    collectionId: UniqueEntityID,
    role: Role,
    excludeUserId: UniqueEntityID,
  ): Promise<UniqueEntityID[]> {
    const users = await this.userRepository.getUsersByRoleInCollection(
      collectionId,
      role,
    )

    return users
      .filter((user) => !user.id.equals(excludeUserId))
      .map((user) => user.id)
  }

  /**
   * Gets target users based on notification scenario and roles
   */
  private async getTargetUsersByRoles(
    collectionId: UniqueEntityID,
    scenario: NotificationScenario,
    currentUserId: UniqueEntityID,
  ): Promise<UniqueEntityID[]> {
    const targetUsers: UniqueEntityID[] = []

    // Always include platform managers (they see everything)
    const platformManagers = await this.getUsersByRoleInCollection(
      collectionId,
      'PLATFORM_MANAGER',
      currentUserId,
    )
    targetUsers.push(...platformManagers)

    // Always include collection managers
    const collectionManagers = await this.getUsersByRoleInCollection(
      collectionId,
      'COLLECTION_MANAGER',
      currentUserId,
    )
    targetUsers.push(...collectionManagers)

    // Include editors for entry and change related activities
    if (
      scenario === 'ENTRY_CREATE' ||
      scenario === 'ENTRY_UPDATE' ||
      scenario === 'CHANGE_CREATE'
    ) {
      const editors = await this.getUsersByRoleInCollection(
        collectionId,
        'EDITOR',
        currentUserId,
      )
      targetUsers.push(...editors)
    }

    return this.removeDuplicates(targetUsers)
  }

  /**
   * Removes duplicate user IDs from the array
   */
  private removeDuplicates(userIds: UniqueEntityID[]): UniqueEntityID[] {
    const seen = new Set<string>()
    return userIds.filter((userId) => {
      const idValue = userId.value
      if (seen.has(idValue)) {
        return false
      }
      seen.add(idValue)
      return true
    })
  }
}
