import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { GenericUserRepository } from '../../users/repositories/interfaces/genericUserRepository'
import { Role } from '../../users/types/user'
import { ACTIVITY_TYPES, ActivityType } from '../domain/events/EventRegistry'

// Map activity types to notification scenarios
export type NotificationScenario = Extract<
  ActivityType,
  | 'ENTRY_CREATE'
  | 'ENTRY_UPDATE'
  | 'CHANGE_CREATE'
  | 'CHANGE_APPROVE'
  | 'CHANGE_REJECT'
  | 'ROLE_CHANGE'
>

export interface NotificationTargetContext {
  collectionId: UniqueEntityID
  scenario: NotificationScenario
  currentUserId: UniqueEntityID
  specificTargetUserId?: UniqueEntityID
}

export class NotificationTargetService {
  constructor(private readonly userRepository: GenericUserRepository) {}

  async getTargetUsers(
    context: NotificationTargetContext,
  ): Promise<UniqueEntityID[]> {
    const { collectionId, scenario, currentUserId, specificTargetUserId } =
      context

    if (
      (scenario === ACTIVITY_TYPES.CHANGE_APPROVE ||
        scenario === ACTIVITY_TYPES.CHANGE_REJECT) &&
      specificTargetUserId
    ) {
      if (specificTargetUserId.equals(currentUserId)) {
        return []
      }
      return [specificTargetUserId]
    }

    if (scenario === ACTIVITY_TYPES.ROLE_CHANGE && specificTargetUserId) {
      const targetUsers: UniqueEntityID[] = []

      if (!specificTargetUserId.equals(currentUserId)) {
        targetUsers.push(specificTargetUserId)
      }

      const collectionManagers = await this.getUsersByRoleInCollection(
        collectionId,
        'COLLECTION_MANAGER',
        currentUserId,
      )
      targetUsers.push(...collectionManagers)

      return this.removeDuplicates(targetUsers)
    }

    return this.getTargetUsersByRoles(collectionId, scenario, currentUserId)
  }

  private async getUsersByRoleInCollection(
    collectionId: UniqueEntityID,
    role: Role,
    excludeUserId: UniqueEntityID,
  ): Promise<UniqueEntityID[]> {
    const users = await this.userRepository.getUsersByRoleInCollection(
      collectionId,
      role,
    )

    return users
      .filter((user) => !user.id.equals(excludeUserId))
      .map((user) => user.id)
  }

  private async getTargetUsersByRoles(
    collectionId: UniqueEntityID,
    scenario: NotificationScenario,
    currentUserId: UniqueEntityID,
  ): Promise<UniqueEntityID[]> {
    const targetUsers: UniqueEntityID[] = []

    const platformManagers = await this.getUsersByRoleInCollection(
      collectionId,
      'PLATFORM_MANAGER',
      currentUserId,
    )
    targetUsers.push(...platformManagers)

    const collectionManagers = await this.getUsersByRoleInCollection(
      collectionId,
      'COLLECTION_MANAGER',
      currentUserId,
    )
    targetUsers.push(...collectionManagers)

    if (
      scenario === ACTIVITY_TYPES.ENTRY_CREATE ||
      scenario === ACTIVITY_TYPES.ENTRY_UPDATE ||
      scenario === ACTIVITY_TYPES.CHANGE_CREATE
    ) {
      const editors = await this.getUsersByRoleInCollection(
        collectionId,
        'EDITOR',
        currentUserId,
      )
      targetUsers.push(...editors)
    }

    return this.removeDuplicates(targetUsers)
  }

  private removeDuplicates(userIds: UniqueEntityID[]): UniqueEntityID[] {
    const seen = new Set<string>()
    return userIds.filter((userId) => {
      const idValue = userId.value
      if (seen.has(idValue)) {
        return false
      }
      seen.add(idValue)
      return true
    })
  }
}
