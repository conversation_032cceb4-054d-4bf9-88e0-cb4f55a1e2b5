import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { Change } from '../../entries/domain/change'
import { Entry } from '../../entries/domain/entry'
import { User } from '../../users/domain/user'
import { GenericUserRepository } from '../../users/repositories/interfaces/genericUserRepository'
import { Role } from '../../users/types/user'
import {
  EntryCreatedEvent,
  EntryUpdatedEvent,
  ChangeCreatedEvent,
  ChangeApprovedEvent,
  ChangeRejectedEvent,
  UserRoleChangedEvent,
} from '../domain/events'
import { NotificationTargetService } from './NotificationTargetService'

/**
 * Service to create and dispatch activity notification events
 * This service handles the business logic of determining target users
 * and creating appropriate domain events for different scenarios
 */
export class ActivityNotificationService {
  private notificationTargetService: NotificationTargetService

  constructor(private readonly userRepository: GenericUserRepository) {
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  /**
   * Creates and dispatches EntryCreatedEvent
   */
  async dispatchEntryCreatedEvent(
    entry: Entry,
    currentUserId: UniqueEntityID,
  ): Promise<void> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: entry.collectionId,
      scenario: 'ENTRY_CREATE',
      currentUserId,
    })

    const event = new EntryCreatedEvent(entry, targetUsers)
    entry.addDomainEvent(event)
  }

  /**
   * Creates and dispatches EntryUpdatedEvent
   */
  async dispatchEntryUpdatedEvent(
    entry: Entry,
    updatedBy: UniqueEntityID,
  ): Promise<void> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: entry.collectionId,
      scenario: 'ENTRY_UPDATE',
      currentUserId: updatedBy,
    })

    const event = new EntryUpdatedEvent(entry, updatedBy, targetUsers)
    entry.addDomainEvent(event)
  }

  /**
   * Creates and dispatches ChangeCreatedEvent
   */
  async dispatchChangeCreatedEvent(
    change: Change,
    collectionId: UniqueEntityID,
    currentUserId: UniqueEntityID,
  ): Promise<void> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'CHANGE_CREATE',
      currentUserId,
    })

    const event = new ChangeCreatedEvent(change, collectionId, targetUsers)
    change.addDomainEvent(event)
  }

  /**
   * Creates and dispatches ChangeApprovedEvent
   */
  async dispatchChangeApprovedEvent(
    change: Change,
    collectionId: UniqueEntityID,
    approvedBy: UniqueEntityID,
  ): Promise<void> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'CHANGE_APPROVE',
      currentUserId: approvedBy,
      specificTargetUserId: change.createdBy,
    })

    const event = new ChangeApprovedEvent(
      change,
      collectionId,
      approvedBy,
      targetUsers,
    )
    change.addDomainEvent(event)
  }

  /**
   * Creates and dispatches ChangeRejectedEvent
   */
  async dispatchChangeRejectedEvent(
    change: Change,
    collectionId: UniqueEntityID,
    rejectedBy: UniqueEntityID,
  ): Promise<void> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'CHANGE_REJECT',
      currentUserId: rejectedBy,
      specificTargetUserId: change.createdBy,
    })

    const event = new ChangeRejectedEvent(
      change,
      collectionId,
      rejectedBy,
      targetUsers,
    )
    change.addDomainEvent(event)
  }

  /**
   * Creates and dispatches UserRoleChangedEvent
   */
  async dispatchUserRoleChangedEvent(
    user: User,
    collectionId: UniqueEntityID,
    changedBy: UniqueEntityID,
    oldRole: Role,
    newRole: Role,
  ): Promise<void> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'ROLE_CHANGE',
      currentUserId: changedBy,
      specificTargetUserId: user.id,
    })

    const event = new UserRoleChangedEvent(
      user,
      collectionId,
      changedBy,
      oldRole,
      newRole,
      targetUsers,
    )
    user.addDomainEvent(event)
  }
}
