import { Email } from '../../../../shared/domain/email'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  closeTestDatabase,
  setupTestDatabase,
  truncateTestDatabase,
} from '../../../../testUtils/databaseSetup'
import { stubCollection } from '../../../collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../../../collections/repositories'
import { stubUser } from '../../../users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../../../users/domain/__stubs__/userAssignment.stub'
import { userRepository } from '../../../users/repositories'
import { NotificationTargetService } from '../NotificationTargetService'

describe('NotificationTargetService', () => {
  let service: NotificationTargetService
  let collection: ReturnType<typeof stubCollection>
  let platformManager: ReturnType<typeof stubUser>
  let collectionManager: ReturnType<typeof stubUser>
  let editor: ReturnType<typeof stubUser>
  let contributor: ReturnType<typeof stubUser>
  let viewer: ReturnType<typeof stubUser>

  beforeAll(async () => {
    await setupTestDatabase()
  })

  beforeEach(async () => {
    await truncateTestDatabase()

    service = new NotificationTargetService(userRepository)

    // Setup test data
    collection = stubCollection()
    await collectionRepository.save(collection)

    // Create users with different roles and unique emails
    const timestamp = Date.now()

    platformManager = stubUser()
    platformManager.updateEmail(
      new Email({ value: `platform-${timestamp}-<EMAIL>` }),
    )
    platformManager.userAssignments.push(
      stubUserAssignment({
        userId: platformManager.id,
        collectionId: collection.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
    await userRepository.save(platformManager)

    collectionManager = stubUser()
    collectionManager.updateEmail(
      new Email({ value: `collection-${timestamp}-<EMAIL>` }),
    )
    collectionManager.userAssignments.push(
      stubUserAssignment({
        userId: collectionManager.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(collectionManager)

    editor = stubUser()
    editor.updateEmail(
      new Email({ value: `editor-${timestamp}-<EMAIL>` }),
    )
    editor.userAssignments.push(
      stubUserAssignment({
        userId: editor.id,
        collectionId: collection.id,
        role: 'EDITOR',
      }),
    )
    await userRepository.save(editor)

    contributor = stubUser()
    contributor.updateEmail(
      new Email({ value: `contributor-${timestamp}-<EMAIL>` }),
    )
    contributor.userAssignments.push(
      stubUserAssignment({
        userId: contributor.id,
        collectionId: collection.id,
        role: 'CONTRIBUTOR',
      }),
    )
    await userRepository.save(contributor)

    viewer = stubUser()
    viewer.updateEmail(
      new Email({ value: `viewer-${timestamp}-<EMAIL>` }),
    )
    viewer.userAssignments.push(
      stubUserAssignment({
        userId: viewer.id,
        collectionId: collection.id,
        role: 'VIEWER',
      }),
    )
    await userRepository.save(viewer)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('getTargetUsers', () => {
    describe('ENTRY_CREATE scenario', () => {
      it('should include platform managers, collection managers, and editors', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'ENTRY_CREATE',
          currentUserId: contributor.id,
        })

        expect(targetUsers).toContainEqual(platformManager.id)
        expect(targetUsers).toContainEqual(collectionManager.id)
        expect(targetUsers).toContainEqual(editor.id)
        expect(targetUsers).not.toContainEqual(contributor.id) // Current user excluded
        expect(targetUsers).not.toContainEqual(viewer.id) // Viewers not included
      })

      it('should exclude current user from target users', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'ENTRY_CREATE',
          currentUserId: editor.id,
        })

        expect(targetUsers).not.toContainEqual(editor.id)
        expect(targetUsers).toContainEqual(collectionManager.id)
        expect(targetUsers).toContainEqual(platformManager.id)
      })
    })

    describe('ENTRY_UPDATE scenario', () => {
      it('should include platform managers, collection managers, and editors', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'ENTRY_UPDATE',
          currentUserId: contributor.id,
        })

        expect(targetUsers).toContainEqual(platformManager.id)
        expect(targetUsers).toContainEqual(collectionManager.id)
        expect(targetUsers).toContainEqual(editor.id)
        expect(targetUsers).not.toContainEqual(contributor.id)
        expect(targetUsers).not.toContain(viewer.id)
      })
    })

    describe('CHANGE_CREATE scenario', () => {
      it('should include platform managers, collection managers, and editors', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'CHANGE_CREATE',
          currentUserId: contributor.id,
        })

        expect(targetUsers).toContainEqual(platformManager.id)
        expect(targetUsers).toContainEqual(collectionManager.id)
        expect(targetUsers).toContainEqual(editor.id)
        expect(targetUsers).not.toContainEqual(contributor.id)
        expect(targetUsers).not.toContain(viewer.id)
      })
    })

    describe('CHANGE_APPROVE scenario', () => {
      it('should only include specific target user if provided', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'CHANGE_APPROVE',
          currentUserId: collectionManager.id,
          specificTargetUserId: contributor.id,
        })

        expect(targetUsers).toEqual([contributor.id])
        expect(targetUsers).not.toContain(collectionManager.id) // Current user excluded
      })

      it('should return empty array if specific target is same as current user', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'CHANGE_APPROVE',
          currentUserId: contributor.id,
          specificTargetUserId: contributor.id,
        })

        expect(targetUsers).toEqual([])
      })
    })

    describe('CHANGE_REJECT scenario', () => {
      it('should only include specific target user if provided', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'CHANGE_REJECT',
          currentUserId: collectionManager.id,
          specificTargetUserId: contributor.id,
        })

        expect(targetUsers).toEqual([contributor.id])
        expect(targetUsers).not.toContain(collectionManager.id)
      })
    })

    describe('ROLE_CHANGE scenario', () => {
      it('should include specific target user and collection managers', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'ROLE_CHANGE',
          currentUserId: collectionManager.id,
          specificTargetUserId: contributor.id,
        })

        expect(targetUsers).toContain(contributor.id)
        expect(targetUsers).not.toContain(collectionManager.id) // Current user excluded
      })

      it('should remove duplicates when target user is also a collection manager', async () => {
        const targetUsers = await service.getTargetUsers({
          collectionId: collection.id,
          scenario: 'ROLE_CHANGE',
          currentUserId: platformManager.id,
          specificTargetUserId: collectionManager.id,
        })

        // Should only contain collectionManager once, even though they're both
        // the specific target and a collection manager
        const collectionManagerCount = targetUsers.filter((id) =>
          id.equals(collectionManager.id),
        ).length
        expect(collectionManagerCount).toBe(1)
      })
    })

    describe('edge cases', () => {
      it('should handle empty collection (no users)', async () => {
        const emptyCollection = stubCollection()
        await collectionRepository.save(emptyCollection)

        const targetUsers = await service.getTargetUsers({
          collectionId: emptyCollection.id,
          scenario: 'ENTRY_CREATE',
          currentUserId: new UniqueEntityID(),
        })

        // Should still include platform managers even for empty collections
        expect(targetUsers).toContainEqual(platformManager.id)
        expect(targetUsers).not.toContainEqual(collectionManager.id)
        expect(targetUsers).not.toContainEqual(editor.id)
      })

      it('should handle collection with only current user', async () => {
        const singleUserCollection = stubCollection()
        await collectionRepository.save(singleUserCollection)

        const singleUser = stubUser()
        singleUser.updateEmail(
          new Email({
            value: `single-${Date.now()}-${Math.random()}@example.com`,
          }),
        )
        singleUser.userAssignments.push(
          stubUserAssignment({
            userId: singleUser.id,
            collectionId: singleUserCollection.id,
            role: 'COLLECTION_MANAGER',
          }),
        )
        await userRepository.save(singleUser)

        const targetUsers = await service.getTargetUsers({
          collectionId: singleUserCollection.id,
          scenario: 'ENTRY_CREATE',
          currentUserId: singleUser.id,
        })

        // Should still include platform managers even if current user is the only collection user
        expect(targetUsers).toContainEqual(platformManager.id)
        expect(targetUsers).not.toContainEqual(singleUser.id) // Current user excluded
      })
    })
  })
})
