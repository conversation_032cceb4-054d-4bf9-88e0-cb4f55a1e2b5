import { IDomainEvent } from '../../../shared/domain/events/interfaces/IDomainEvent'
import { IDomainEventHandler } from '../../../shared/domain/events/interfaces/IDomainEventHandler'
import { ChangeApprovedHandler } from './ChangeApprovedHandler'
import { ChangeCreatedHandler } from './ChangeCreatedHandler'
import { ChangeRejectedHandler } from './ChangeRejectedHandler'
import { EntryCreatedHandler } from './EntryCreatedHandler'
import { EntryUpdatedHandler } from './EntryUpdatedHandler'
import { UserRemovedHandler } from './UserRemovedHandler'
import { UserRoleChangedHandler } from './UserRoleChangedHandler'

export const activityEventHandlers: IDomainEventHandler<IDomainEvent>[] = [
  new EntryCreatedHandler(),
  new EntryUpdatedHandler(),
  new ChangeCreatedHandler(),
  new ChangeApprovedHandler(),
  new ChangeRejectedHandler(),
  new UserRoleChangedHandler(),
  new UserRemovedHandler(),
]
