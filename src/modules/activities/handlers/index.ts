import { IDomainEvent } from '../../../shared/domain/events/interfaces/IDomainEvent'
import { IDomainEventHandler } from '../../../shared/domain/events/interfaces/IDomainEventHandler'

// Change handlers
import { ChangeApprovedHandler } from './changes/ChangeApprovedHandler'
import { ChangeCreatedHandler } from './changes/ChangeCreatedHandler'
import { ChangeRejectedHandler } from './changes/ChangeRejectedHandler'

// Entry handlers
import { EntryCreatedHandler } from './entries/EntryCreatedHandler'
import { EntryUpdatedHandler } from './entries/EntryUpdatedHandler'

// Notification handlers
import { NotificationCreatedHandler } from './notifications/NotificationCreatedHandler'

// User handlers
import { UserRemovedHandler } from './users/UserRemovedHandler'
import { UserRoleChangedHandler } from './users/UserRoleChangedHandler'

export const activityEventHandlers: IDomainEventHandler<IDomainEvent>[] = [
  new EntryCreatedHandler(),
  new EntryUpdatedHandler(),
  new ChangeCreatedHandler(),
  new ChangeApprovedHandler(),
  new ChangeRejectedHandler(),
  new UserRoleChangedHandler(),
  new UserRemovedHandler(),
  new NotificationCreatedHandler(),
]
