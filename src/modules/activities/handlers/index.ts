import { IDomainEvent } from '../../../shared/domain/events/interfaces/IDomainEvent'
import { IDomainEventHandler } from '../../../shared/domain/events/interfaces/IDomainEventHandler'
import { ChangeApprovedHandler } from './changes/ChangeApprovedHandler'
import { ChangeCreatedHandler } from './changes/ChangeCreatedHandler'
import { ChangeRejectedHandler } from './changes/ChangeRejectedHandler'
import { EntryCreatedHandler } from './entries/EntryCreatedHandler'
import { EntryUpdatedHandler } from './entries/EntryUpdatedHandler'
import { NotificationCreatedHandler } from './notifications/NotificationCreatedHandler'
import { UserRemovedHandler } from './users/UserRemovedHandler'
import { UserRoleChangedHandler } from './users/UserRoleChangedHandler'

export const activityEventHandlers: IDomainEventHandler<IDomainEvent>[] = [
  new EntryCreatedHandler(),
  new EntryUpdatedHandler(),
  new ChangeCreated<PERSON>andler(),
  new ChangeApprovedHandler(),
  new ChangeRejectedHandler(),
  new UserRoleChangedHandler(),
  new UserRemovedHandler(),
  new NotificationCreatedHandler(),
]
