import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryCreatedEvent } from '../domain/events/EntryCreatedEvent'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class Entry<PERSON><PERSON>d<PERSON><PERSON><PERSON> extends BaseDomainEventHandler<
  EntryCreatedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, EntryCreatedEvent, EntryCreatedEvent.eventIdentifier)
  }

  protected parseInput(event: EntryCreatedEvent): CreateActivityDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
