import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryCreatedEvent } from '../domain/events/EntryCreatedEvent'
import { notificationTargetService } from '../services'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class EntryCreatedHandler extends BaseDomainEventHandler<
  EntryCreatedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, EntryCreatedEvent, EntryCreatedEvent.eventIdentifier)
  }

  protected async parseInput(
    event: EntryCreatedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await notificationTargetService.getTargetUsers({
      collectionId: event.entry.collectionId,
      scenario: 'ENTRY_CREATE',
      currentUserId: event.entry.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers,
    }
  }
}
