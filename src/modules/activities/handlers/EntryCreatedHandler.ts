import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { userRepository } from '../../users/repositories'
import { EntryCreatedEvent } from '../domain/events/EntryCreatedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class EntryCreated<PERSON>and<PERSON> extends BaseDomainEventHandler<
  EntryCreatedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, EntryCreatedEvent, EntryCreatedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: EntryCreatedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: event.entry.collectionId,
      scenario: 'ENTRY_CREATE',
      currentUserId: event.entry.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers,
    }
  }
}
