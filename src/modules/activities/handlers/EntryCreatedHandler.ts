import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryCreatedEvent } from '../domain/events/EntryCreatedEvent'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivityWithNotifications/createActivityWithNotifications.dto'
import { useCase } from '../useCases/createActivityWithNotifications/index'

export class EntryCreatedHandler extends BaseDomainEventHandler<
  EntryCreatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(useCase, EntryCreatedEvent, EntryCreatedEvent.eventIdentifier)
  }

  protected parseInput(
    event: EntryCreatedEvent,
  ): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
