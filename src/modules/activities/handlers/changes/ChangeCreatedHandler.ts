import { BaseDomainEventHandler } from '../../../../shared/domain/events/BaseDomainEventHandler'
import { entryRepository } from '../../../entries/repositories'
import { ChangeCreatedEvent } from '../../domain/events/changes/ChangeCreatedEvent'
import { notificationTargetService } from '../../services'
import { CreateActivityDTO } from '../../useCases/createActivity/createActivity.dto'
import { useCase } from '../../useCases/createActivity/index'

export class ChangeCreatedHandler extends BaseDomainEventHandler<
  ChangeCreatedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, ChangeCreatedEvent, ChangeCreatedEvent.eventIdentifier)
  }

  protected async parseInput(
    event: ChangeCreatedEvent,
  ): Promise<CreateActivityDTO> {
    const entry = await entryRepository.getById(event.change.entryId)

    const targetUsers = await notificationTargetService.getTargetUsers({
      collectionId: entry.collectionId,
      scenario: 'CHANGE_CREATE',
      currentUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: entry.collectionId.value,
      targetUsers,
    }
  }
}
