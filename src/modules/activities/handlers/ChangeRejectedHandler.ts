import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { entryRepository } from '../../entries/repositories'
import { userRepository } from '../../users/repositories'
import { ChangeRejectedEvent } from '../domain/events/ChangeRejectedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeRejectedHandler extends BaseDomainEventHandler<
  ChangeRejectedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, ChangeRejectedEvent, ChangeRejectedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: ChangeRejectedEvent,
  ): Promise<CreateActivityDTO> {
    const entry = await entryRepository.getById(event.change.entryId)
    const collectionId = entry.collectionId
    const rejectedBy = event.change.reviewedBy || new UniqueEntityID('unknown')

    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'CHANGE_REJECT',
      currentUserId: rejectedBy,
      specificTargetUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: collectionId.value,
      targetUsers,
    }
  }
}
