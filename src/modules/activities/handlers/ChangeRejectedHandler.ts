import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeRejectedEvent } from '../domain/events/ChangeRejectedEvent'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeRejectedHandler extends BaseDomainEventHandler<
  ChangeRejectedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, ChangeRejectedEvent, ChangeRejectedEvent.eventIdentifier)
  }

  protected parseInput(event: ChangeRejectedEvent): CreateActivityDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
