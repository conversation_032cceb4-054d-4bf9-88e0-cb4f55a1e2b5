import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { userRepository } from '../../users/repositories'
import { ChangeRejectedEvent } from '../domain/events/ChangeRejectedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeRejectedHandler extends BaseDomainEventHandler<
  ChangeRejectedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, ChangeRejectedEvent, ChangeRejectedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: ChangeRejectedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: event.collectionId,
      scenario: 'CHANGE_REJECT',
      currentUserId: event.rejectedBy,
      specificTargetUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers,
    }
  }
}
