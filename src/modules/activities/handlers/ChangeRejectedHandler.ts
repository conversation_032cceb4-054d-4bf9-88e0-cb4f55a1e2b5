import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeRejectedEvent } from '../domain/events/ChangeRejectedEvent'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivityWithNotifications/createActivityWithNotifications.dto'
import { useCase } from '../useCases/createActivityWithNotifications/index'

export class ChangeRejectedHandler extends BaseDomainEventHandler<
  ChangeRejectedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(useCase, ChangeRejectedEvent, ChangeRejectedEvent.eventIdentifier)
  }

  protected parseInput(
    event: ChangeRejectedEvent,
  ): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
