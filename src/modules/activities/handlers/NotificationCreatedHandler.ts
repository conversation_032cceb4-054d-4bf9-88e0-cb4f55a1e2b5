import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { logger } from '../../../shared/infra/logger'
import { NotificationCreatedEvent } from '../domain/events/NotificationCreatedEvent'
import { SendNotificationEmailDTO } from './dto/sendNotificationEmail.dto'

export class NotificationCreatedHandler extends BaseDomainEventHandler<
  NotificationCreatedEvent,
  SendNotificationEmailDTO
> {
  constructor() {
    // TODO: Replace with actual send email use case
    super(
      {
        execute: async (dto: SendNotificationEmailDTO): Promise<void> => {
          logger.info('Notification email dispatch requested', {
            notificationId: dto.notificationId,
            userId: dto.userId,
            activityId: dto.activityId,
            message:
              'This is a temporary log - actual email dispatch not yet implemented',
          })
        },
      } as any,
      NotificationCreatedEvent,
      NotificationCreatedEvent.eventIdentifier,
    )
  }

  protected parseInput(
    event: NotificationCreatedEvent,
  ): SendNotificationEmailDTO {
    return {
      notificationId: event.notification.id.value,
      userId: event.notification.userId.value,
      activityId: event.notification.activityId.value,
    }
  }
}
