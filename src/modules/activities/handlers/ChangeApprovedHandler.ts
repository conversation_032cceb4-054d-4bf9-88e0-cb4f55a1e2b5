import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { entryRepository } from '../../entries/repositories'
import { userRepository } from '../../users/repositories'
import { ChangeApprovedEvent } from '../domain/events/ChangeApprovedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeApproved<PERSON>and<PERSON> extends BaseDomainEventHandler<
  ChangeApprovedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, ChangeApprovedEvent, ChangeApprovedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: ChangeApprovedEvent,
  ): Promise<CreateActivityDTO> {
    const entry = await entryRepository.getById(event.change.entryId)
    const collectionId = entry.collectionId
    const approvedBy = event.change.reviewedBy || new UniqueEntityID('unknown')

    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'CHANGE_APPROVE',
      currentUserId: approvedBy,
      specificTargetUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: collectionId.value,
      targetUsers,
    }
  }
}
