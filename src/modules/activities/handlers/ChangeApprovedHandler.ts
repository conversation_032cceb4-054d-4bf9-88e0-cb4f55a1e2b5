import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeApprovedEvent } from '../domain/events/ChangeApprovedEvent'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeApprovedHandler extends BaseDomainEventHandler<
  ChangeApprovedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, ChangeApprovedEvent, ChangeApprovedEvent.eventIdentifier)
  }

  protected parseInput(event: ChangeApprovedEvent): CreateActivityDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
