import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { entryRepository } from '../../entries/repositories'
import { ChangeApprovedEvent } from '../domain/events/ChangeApprovedEvent'
import { notificationTargetService } from '../services'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeApprovedHandler extends BaseDomainEventHandler<
  ChangeApprovedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, ChangeApprovedEvent, ChangeApprovedEvent.eventIdentifier)
  }

  protected async parseInput(
    event: ChangeApprovedEvent,
  ): Promise<CreateActivityDTO> {
    const entry = await entryRepository.getById(event.change.entryId)
    const collectionId = entry.collectionId
    const approvedBy = event.change.reviewedBy || new UniqueEntityID('unknown')

    const targetUsers = await notificationTargetService.getTargetUsers({
      collectionId,
      scenario: 'CHANGE_APPROVE',
      currentUserId: approvedBy,
      specificTargetUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: collectionId.value,
      targetUsers,
    }
  }
}
