import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { userRepository } from '../../users/repositories'
import { ChangeApprovedEvent } from '../domain/events/ChangeApprovedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class ChangeApprovedHandler extends BaseDomainEventHandler<
  ChangeApprovedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, ChangeApprovedEvent, ChangeApprovedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: ChangeApprovedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: event.collectionId,
      scenario: 'CHANGE_APPROVE',
      currentUserId: event.approvedBy,
      specificTargetUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers,
    }
  }
}
