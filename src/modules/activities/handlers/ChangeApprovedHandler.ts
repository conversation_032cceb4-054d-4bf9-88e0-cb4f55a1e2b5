import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeApprovedEvent } from '../domain/events/ChangeApprovedEvent'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivityWithNotifications/createActivityWithNotifications.dto'
import { useCase } from '../useCases/createActivityWithNotifications/index'

export class ChangeApprovedHandler extends BaseDomainEventHandler<
  ChangeApprovedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(useCase, ChangeApprovedEvent, ChangeApprovedEvent.eventIdentifier)
  }

  protected parseInput(
    event: ChangeApprovedEvent,
  ): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
