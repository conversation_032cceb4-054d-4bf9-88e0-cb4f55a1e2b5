import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { userRepository } from '../../users/repositories'
import { UserRoleChangedEvent } from '../domain/events/UserRoleChangedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class UserRoleChangedHandler extends BaseDomainEventHandler<
  UserRoleChangedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, UserRoleChangedEvent, UserRoleChangedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: UserRoleChangedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: event.collectionId,
      scenario: 'ROLE_CHANGE',
      currentUserId: event.changedBy,
      specificTargetUserId: event.user.id,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers,
    }
  }
}
