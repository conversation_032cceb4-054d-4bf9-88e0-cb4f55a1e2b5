import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UserRoleChangedEvent } from '../domain/events/UserRoleChangedEvent'
import { notificationTargetService } from '../services'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class UserRoleChangedHandler extends BaseDomainEventHandler<
  UserRoleChangedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, UserRoleChangedEvent, UserRoleChangedEvent.eventIdentifier)
  }

  protected async parseInput(
    event: UserRoleChangedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await notificationTargetService.getTargetUsers({
      collectionId: event.collectionId,
      scenario: 'ROLE_CHANGE',
      currentUserId: event.changedBy,
      specificTargetUserId: event.user.id,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers,
    }
  }
}
