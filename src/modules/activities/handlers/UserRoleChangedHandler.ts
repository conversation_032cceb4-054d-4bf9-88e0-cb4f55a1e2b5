import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UserRoleChangedEvent } from '../domain/events/UserRoleChangedEvent'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivityWithNotifications/createActivityWithNotifications.dto'
import { useCase } from '../useCases/createActivityWithNotifications/index'

export class UserRoleChangedHandler extends BaseDomainEventHandler<
  UserRoleChangedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(useCase, UserRoleChangedEvent, UserRoleChangedEvent.eventIdentifier)
  }

  protected parseInput(
    event: UserRoleChangedEvent,
  ): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
