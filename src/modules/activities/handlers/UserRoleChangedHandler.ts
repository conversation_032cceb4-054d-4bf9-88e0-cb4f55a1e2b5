import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UserRoleChangedEvent } from '../domain/events/UserRoleChangedEvent'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class UserRoleChangedHandler extends BaseDomainEventHandler<
  UserRoleChangedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, UserRoleChangedEvent, UserRoleChangedEvent.eventIdentifier)
  }

  protected parseInput(event: UserRoleChangedEvent): CreateActivityDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
