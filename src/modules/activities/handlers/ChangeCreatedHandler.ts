import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { entryRepository } from '../../entries/repositories'
import { userRepository } from '../../users/repositories'
import { ChangeCreatedEvent } from '../domain/events/ChangeCreatedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class Change<PERSON>reated<PERSON>and<PERSON> extends BaseDomainEventHandler<
  ChangeCreatedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, ChangeCreatedEvent, ChangeCreatedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: ChangeCreatedEvent,
  ): Promise<CreateActivityDTO> {
    // Get the entry to resolve the collection ID
    const entry = await entryRepository.getById(event.change.entryId)

    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: entry.collectionId,
      scenario: 'CHANGE_CREATE',
      currentUserId: event.change.createdBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: entry.collectionId.value,
      targetUsers,
    }
  }
}
