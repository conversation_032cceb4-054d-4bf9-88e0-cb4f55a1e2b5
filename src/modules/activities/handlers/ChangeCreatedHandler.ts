import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeCreatedEvent } from '../domain/events/ChangeCreatedEvent'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivityWithNotifications/createActivityWithNotifications.dto'
import { useCase } from '../useCases/createActivityWithNotifications/index'

export class ChangeCreatedHandler extends BaseDomainEventHandler<
  ChangeCreatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(useCase, ChangeCreatedEvent, ChangeCreatedEvent.eventIdentifier)
  }

  protected parseInput(
    event: ChangeCreatedEvent,
  ): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
