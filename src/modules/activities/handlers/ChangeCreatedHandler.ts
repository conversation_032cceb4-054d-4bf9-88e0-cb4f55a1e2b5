import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeCreatedEvent } from '../domain/events/ChangeCreatedEvent'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class Change<PERSON><PERSON>d<PERSON><PERSON><PERSON> extends BaseDomainEventHandler<
  ChangeCreatedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, ChangeCreatedEvent, ChangeCreatedEvent.eventIdentifier)
  }

  protected parseInput(event: ChangeCreatedEvent): CreateActivityDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
