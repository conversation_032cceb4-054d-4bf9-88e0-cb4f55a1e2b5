import { BaseDomainEventHandler } from '../../../../shared/domain/events/BaseDomainEventHandler'
import { NotificationCreatedEvent } from '../../domain/events/notifications/NotificationCreatedEvent'
import { sendNotificationEmailUseCase } from '../../useCases/sendNotificationEmail'
import { SendNotificationEmailDTO } from '../../useCases/sendNotificationEmail/sendNotificationEmail.dto'

export class NotificationCreatedHandler extends BaseDomainEventHandler<
  NotificationCreatedEvent,
  SendNotificationEmailDTO
> {
  constructor() {
    super(
      sendNotificationEmailUseCase,
      NotificationCreatedEvent,
      NotificationCreatedEvent.eventIdentifier,
    )
  }

  protected parseInput(
    event: NotificationCreatedEvent,
  ): SendNotificationEmailDTO {
    return {
      notificationId: event.notification.id.value,
      userId: event.notification.userId.value,
      activityId: event.notification.activityId.value,
    }
  }
}
