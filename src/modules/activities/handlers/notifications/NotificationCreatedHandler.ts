import { BaseDomainEventHandler } from '../../../../shared/domain/events/BaseDomainEventHandler'
import { logger } from '../../../../shared/infra/logger'
import { UseCase } from '../../../../shared/infra/useCase'
import { NotificationCreatedEvent } from '../../domain/events/notifications/NotificationCreatedEvent'
import { SendNotificationEmailDTO } from '../dto/sendNotificationEmail.dto'

/**
 * Temporary use case for logging notification email dispatch requests.
 * This will be replaced with actual email service implementation.
 */
class TemporaryNotificationEmailUseCase extends UseCase<
  SendNotificationEmailDTO,
  void
> {
  constructor() {
    super('TemporaryNotificationEmail')
  }

  async execute(dto: SendNotificationEmailDTO): Promise<void> {
    logger.info('Notification email dispatch requested', {
      notificationId: dto.notificationId,
      userId: dto.userId,
      activityId: dto.activityId,
      message:
        'This is a temporary log - actual email dispatch not yet implemented',
    })
  }
}

export class NotificationCreatedHandler extends BaseDomainEventHandler<
  NotificationCreatedEvent,
  SendNotificationEmailDTO
> {
  constructor() {
    super(
      new TemporaryNotificationEmailUseCase(),
      NotificationCreatedEvent,
      NotificationCreatedEvent.eventIdentifier,
    )
  }

  protected parseInput(
    event: NotificationCreatedEvent,
  ): SendNotificationEmailDTO {
    return {
      notificationId: event.notification.id.value,
      userId: event.notification.userId.value,
      activityId: event.notification.activityId.value,
    }
  }
}
