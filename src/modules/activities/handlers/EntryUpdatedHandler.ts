import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryUpdatedEvent } from '../domain/events/EntryUpdatedEvent'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivityWithNotifications/createActivityWithNotifications.dto'
import { useCase } from '../useCases/createActivityWithNotifications/index'

export class EntryUpdatedHandler extends BaseDomainEventHandler<
  EntryUpdatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(useCase, EntryUpdatedEvent, EntryUpdatedEvent.eventIdentifier)
  }

  protected parseInput(
    event: EntryUpdatedEvent,
  ): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
