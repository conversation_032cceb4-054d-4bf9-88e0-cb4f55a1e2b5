import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryUpdatedEvent } from '../domain/events/EntryUpdatedEvent'
import { notificationTargetService } from '../services'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class EntryUpdatedHandler extends BaseDomainEventHandler<
  EntryUpdatedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, EntryUpdatedEvent, EntryUpdatedEvent.eventIdentifier)
  }

  protected async parseInput(
    event: EntryUpdatedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await notificationTargetService.getTargetUsers({
      collectionId: event.entry.collectionId,
      scenario: 'ENTRY_UPDATE',
      currentUserId: event.updatedBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers,
    }
  }
}
