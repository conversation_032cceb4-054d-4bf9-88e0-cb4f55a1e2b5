import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { userRepository } from '../../users/repositories'
import { EntryUpdatedEvent } from '../domain/events/EntryUpdatedEvent'
import { NotificationTargetService } from '../services/NotificationTargetService'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class EntryUpdatedHandler extends BaseDomainEventHandler<
  EntryUpdatedEvent,
  CreateActivityDTO
> {
  private notificationTargetService: NotificationTargetService

  constructor() {
    super(useCase, EntryUpdatedEvent, EntryUpdatedEvent.eventIdentifier)
    this.notificationTargetService = new NotificationTargetService(
      userRepository,
    )
  }

  protected async parseInput(
    event: EntryUpdatedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await this.notificationTargetService.getTargetUsers({
      collectionId: event.entry.collectionId,
      scenario: 'ENTRY_UPDATE',
      currentUserId: event.updatedBy,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers,
    }
  }
}
