import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryUpdatedEvent } from '../domain/events/EntryUpdatedEvent'
import { CreateActivityDTO } from '../useCases/createActivity/createActivity.dto'
import { useCase } from '../useCases/createActivity/index'

export class EntryUpdatedHand<PERSON> extends BaseDomainEventHandler<
  EntryUpdatedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, EntryUpdatedEvent, EntryUpdatedEvent.eventIdentifier)
  }

  protected parseInput(event: EntryUpdatedEvent): CreateActivityDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.entry.collectionId.value,
      targetUsers: event.targetUsers,
    }
  }
}
