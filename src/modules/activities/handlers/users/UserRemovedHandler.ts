import { BaseDomainEventHandler } from '../../../../shared/domain/events/BaseDomainEventHandler'
import { UserRemovedEvent } from '../../domain/events/users/UserRemovedEvent'
import { notificationTargetService } from '../../services'
import { CreateActivityDTO } from '../../useCases/createActivity/createActivity.dto'
import { useCase } from '../../useCases/createActivity/index'

export class UserRemovedHandler extends BaseDomainEventHandler<
  UserRemovedEvent,
  CreateActivityDTO
> {
  constructor() {
    super(useCase, UserRemovedEvent, UserRemovedEvent.eventIdentifier)
  }

  protected async parseInput(
    event: UserRemovedEvent,
  ): Promise<CreateActivityDTO> {
    const targetUsers = await notificationTargetService.getTargetUsers({
      collectionId: event.collectionId,
      scenario: 'ROLE_CHANGE', // Reuse existing scenario for user role changes
      currentUserId: event.removedBy,
      specificTargetUserId: event.user.id,
    })

    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers,
    }
  }
}
