/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */

import { UniqueEntityID } from '../shared/domain/uniqueEntityID'

// Mock for jose <PERSON>S module to work with Jest
export const MOCKED_PAYLOAD = {
  sub: new UniqueEntityID().value,
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600,
  admin: true,
  collections: [],
}

export const jwtVerify = jest.fn().mockResolvedValue({
  payload: {
    ...MOCKED_PAYLOAD,
  },
  protectedHeader: { alg: 'HS256' },
})

export class SignJWT {
  private payload: any

  constructor(payload: any) {
    this.payload = payload
  }

  setProtectedHeader(header: any) {
    return this
  }

  setIssuedAt() {
    return this
  }

  setExpirationTime(exp: any) {
    return this
  }

  async sign(key: any) {
    return 'mocked-jwt-token'
  }
}
