import { sql } from 'drizzle-orm'
import { migrate } from 'drizzle-orm/pglite/migrator'
import { database, databaseWrapper } from '../shared/database'

async function setupTestDatabase(): Promise<void> {
  try {
    await migrate(database, { migrationsFolder: 'src/shared/database/drizzle' })
  } catch (error) {
    console.error('Test setup: Failed to apply migrations:', error)
    throw error
  }
}

async function truncateTestDatabase(): Promise<void> {
  try {
    // Disable foreign key constraints to allow truncation
    await database.execute(sql.raw('SET session_replication_role = replica;'))

    const queries = Object.values(database._.schema!)
      .map((table) => {
        if (table) {
          return {
            query: sql.raw(`TRUNCATE TABLE "${table.dbName}" CASCADE;`),
            tableName: table.dbName,
          }
        }
        return null
      })
      .filter((item) => item !== null)

    for (const item of queries) {
      if (item) {
        try {
          await database.transaction(async (tx) => {
            await tx.execute(item.query)
          })
        } catch (error) {
          if (
            error instanceof Error &&
            error.message.includes('does not exist')
          ) {
            continue
          }
          console.error(error instanceof Error ? error.message : String(error))
          throw error
        }
      }
    }
  } finally {
    try {
      // Ensure foreign key constraints are re-enabled
      await database.execute(sql.raw('SET session_replication_role = origin;'))
    } catch (enableError) {
      console.error(
        'Test teardown: Failed to re-enable foreign key constraints:',
        enableError,
      )
    }
  }
}

async function closeTestDatabase(): Promise<void> {
  try {
    await databaseWrapper.close()
  } catch (error) {
    console.error('Test teardown: Failed to close database connection:', error)
    throw error
  }
}

export { setupTestDatabase, truncateTestDatabase, closeTestDatabase }
