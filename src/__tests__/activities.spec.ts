import { constants } from 'http2'
import request from 'supertest'
import { MOCKED_PAYLOAD } from '../__mocks__/jose'
import { stubActivity } from '../modules/activities/domain/__stubs__/activity.stub'
import { stubNotification } from '../modules/activities/domain/__stubs__/notification.stub'
import {
  activityRepository,
  notificationRepository,
} from '../modules/activities/repositories'
import { stubCollection } from '../modules/collections/domain/__stubs__/collection.stub'
import { collectionRepository } from '../modules/collections/repositories'
import { stubUserSettings } from '../modules/users/domain/__stubs__/userSettings.stub'
import { User } from '../modules/users/domain/user'
import { userRepository } from '../modules/users/repositories'
import { usersServices } from '../modules/users/services'
import { stubEmail } from '../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../shared/domain/uniqueEntityID'
import { initApp } from '../shared/infra/http'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../testUtils/databaseSetup'
import { paginatedSchema } from '../utils/tests/schemas'
import { route } from '../utils/tests/tests'
import { notificationSchema } from './schemas'

const app = initApp()

let token: string
let notificationId: string

describe('Activities module routes', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await usersServices.initialize()
    const authService = usersServices.get('authorization')

    const user = new User(
      {
        email: stubEmail({ value: '<EMAIL>' }),
        name: 'John Doe',
        settings: stubUserSettings(),
        userAssignments: [],
      },
      new UniqueEntityID(MOCKED_PAYLOAD.sub),
    )
    await userRepository.save(user)
    token = await authService.generateToken(user)

    const collection = stubCollection()
    await collectionRepository.save(collection)

    const activity = stubActivity({
      collectionId: collection.id,
      userId: user.id,
    })
    await activityRepository.save(activity)

    const notification = stubNotification({
      userId: user.id,
      activityId: activity.id,
    })
    notificationId = notification.id.value
    await notificationRepository.save(notification)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('Notifications', () => {
    describe('GET /notifications', () => {
      it('should return a list of notifications', async () => {
        const response = await request(app)
          .get(route('/notifications'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          paginatedSchema(notificationSchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('PATCH /notifications/:notificationId/read', () => {
      it('should mark a notification as read', async () => {
        const response = await request(app)
          .patch(route(`/notifications/${notificationId}/read`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(notificationSchema.validate(response.body)).toBeTruthy()
        expect(response.body.read).toBe(true)
      })
    })

    describe('PATCH /notifications/all-read', () => {
      it('should mark all notifications as read', async () => {
        const response = await request(app)
          .patch(route('/notifications/all-read'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)

        expect(response.body).toStrictEqual({})
      })
    })

    describe('DELETE /notifications/:notificationId', () => {
      it('should delete a specific notification', async () => {
        const response = await request(app)
          .delete(route(`/notifications/${notificationId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
        expect(response.body).toStrictEqual({})
      })

      it('should return 404 for non-existent notification', async () => {
        const nonExistentId = new UniqueEntityID().value
        const response = await request(app)
          .delete(route(`/notifications/${nonExistentId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NOT_FOUND)
      })

      it('should return 401 without authentication', async () => {
        const response = await request(app).delete(
          route(`/notifications/${notificationId}`),
        )

        expect(response.status).toBe(constants.HTTP_STATUS_UNAUTHORIZED)
      })
    })

    describe('DELETE /notifications', () => {
      it('should delete all notifications for authenticated user', async () => {
        const response = await request(app)
          .delete(route('/notifications'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
        expect(response.body).toStrictEqual({})
      })

      it('should return 401 without authentication', async () => {
        const response = await request(app).delete(route('/notifications'))

        expect(response.status).toBe(constants.HTTP_STATUS_UNAUTHORIZED)
      })
    })
  })
})
