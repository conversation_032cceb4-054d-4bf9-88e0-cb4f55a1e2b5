import { object, string, boolean, array, number, mixed } from 'yup'

// User Module

const userSchema = object({
  name: string().required(),
  email: string().email().required(),
  settings: object({
    emailNotifications: boolean().required(),
  }),
  createdAt: string().datetime().required(),
  updatedAt: string().datetime(),
  assignments: array().of(
    object({
      collectionId: string().uuid(),
      role: string().required(),
    }),
  ),
})

const inviteSchema = object({
  collectionId: string().uuid().required(),
  email: string().email().required(),
  role: string().required(),
  name: string().required(),
})

export { userSchema, inviteSchema }

// Activities Module

const activitySchema = object({
  collectionId: string().uuid().required(),
  userId: string().uuid().required(),
  type: string().required(),
  data: mixed(),
})

const notificationSchema = object({
  userId: string().uuid().required(),
  activityId: string().uuid().required(),
  read: boolean().required(),
  sent: boolean().required(),
})

export { activitySchema, notificationSchema }

// Collections Module

// TODO: add assisgnments
const collectionSchema = object({
  name: string().required(),
})

const serviceSchema = object({
  collectionId: string().uuid().required(),
  name: string().required(),
  description: string().required(),
  category: string().required(),
  availability: string().required(),
  link: string().required(),
})

const fieldDataSchema = object({
  fieldId: string().required(),
  value: string().required(),
})

const fieldOptionsSchema = object({
  label: string().required(),
  value: string().required(),
})

const fieldSchema = object({
  fieldGroupId: string().uuid().required(),
  name: string().required(),
  position: number().required(),
  maxLength: number(),
  mandatory: boolean().required(),
  visibility: string().required(),
  type: string().required(),
  preview: boolean().required(),
  options: array().of(fieldOptionsSchema),
})

const fieldGroupSchema = object({
  name: string().required(),
  position: number().required(),
  fields: array().of(fieldSchema),
})

const statisticSchema = object({
  collectionId: string().uuid().required(),
  members: number().required(),
  entries: object({
    total: number().required(),
    public: number().required(),
  }).required(),
})

export {
  collectionSchema,
  serviceSchema,
  fieldSchema,
  fieldGroupSchema,
  statisticSchema,
}

// Entries Module

const entrySchema = object({
  collectionId: string().uuid().required(),
  visibility: string().required(),
  data: array().of(fieldDataSchema).required(),
  changes: array().of(object()),
  createdBy: string().uuid().required(),
})

const changeSchema = object({
  entryId: string().uuid().required(),
  createdBy: string().uuid().required(),
  reviewedBy: string().uuid(),
  data: array().of(fieldDataSchema).required(),
  status: string().required(),
  reviewerMessage: string(),
})

export { entrySchema, changeSchema }
