import { constants } from 'http2'
import request from 'supertest'
import { MOCKED_PAYLOAD } from '../__mocks__/jose'
import { stubCollection } from '../modules/collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../modules/collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../modules/collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../modules/collections/domain/__stubs__/fields.stub'
import { Collection } from '../modules/collections/domain/collection'
import { Field } from '../modules/collections/domain/field'
import {
  collectionRepository,
  fieldGroupRepository,
} from '../modules/collections/repositories'
import { stubChange } from '../modules/entries/domain/__stubs__/change.stub'
import { stubEntry } from '../modules/entries/domain/__stubs__/entry.stub'
import { stubFieldData } from '../modules/entries/domain/__stubs__/fieldData.stub'
import {
  changeRepository,
  entryRepository,
} from '../modules/entries/repositories'
import { stubUserAssignment } from '../modules/users/domain/__stubs__/userAssignment.stub'
import { stubUserSettings } from '../modules/users/domain/__stubs__/userSettings.stub'
import { User } from '../modules/users/domain/user'
import { userRepository } from '../modules/users/repositories'
import { usersServices } from '../modules/users/services'
import { stubEmail } from '../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../shared/domain/uniqueEntityID'
import { initApp } from '../shared/infra/http'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../testUtils/databaseSetup'
import { paginatedSchema } from '../utils/tests/schemas'
import { route } from '../utils/tests/tests'
import { changeSchema, entrySchema } from './schemas'

const app = initApp()

let token: string
let collection: Collection
let user: User
let field: Field
let entryId: string
let changeId: string

describe('Entries module routes', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await usersServices.initialize()
    const authService = usersServices.get('authorization')

    collection = stubCollection()
    await collectionRepository.save(collection)

    user = new User(
      {
        email: stubEmail({ value: '<EMAIL>' }),
        name: 'John Doe',
        settings: stubUserSettings(),
        userAssignments: [],
      },
      new UniqueEntityID(MOCKED_PAYLOAD.sub),
    )
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )
    await userRepository.save(user)
    token = await authService.generateToken(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)
  })

  beforeEach(async () => {
    const entry = stubEntry({
      collectionId: collection.id,
      createdBy: user.id,
      data: [stubFieldData({ field, value: 'value' })],
    })
    entryId = entry.id.value
    await entryRepository.save(entry)

    const change = stubChange({
      createdBy: user.id,
      entryId: entry.id,
      data: [stubFieldData({ field, value: 'value' })],
      reviewedBy: user.id,
    })
    changeId = change.id.value
    await changeRepository.save(change)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('Entries', () => {
    describe('GET /entries/:entryId', () => {
      it('should return an entry', async () => {
        const response = await request(app)
          .get(route(`/entries/${entryId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(entrySchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('PATCH /entries/:entryId', () => {
      it('should update an entry', async () => {
        const response = await request(app)
          .patch(route(`/entries/${entryId}`))
          .send({ visibility: 'PRIVATE' })
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(entrySchema.validate(response.body)).toBeTruthy()

        expect(response.body.visibility).toBe('PRIVATE')
      })
    })

    describe('DELETE /entries/:entryId', () => {
      it('should delete an entry', async () => {
        const response = await request(app)
          .delete(route(`/entries/${entryId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
      })
    })

    describe('GET /entries/:entryId/changes', () => {
      it('should return a list of changes', async () => {
        const response = await request(app)
          .get(route(`/entries/${entryId}/changes`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          paginatedSchema(changeSchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('POST /entries/:entryId/changes', () => {
      it('should create a change', async () => {
        const body = {
          visibility: 'PUBLIC',
          data: [
            {
              fieldId: field.id.value,
              value: 'A value',
            },
          ],
        }

        const response = await request(app)
          .post(route(`/entries/${entryId}/changes`))
          .send(body)
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_CREATED)

        expect(changeSchema.validate(response.body)).toBeTruthy()

        expect(response.body.status).toBe('PENDING')
      })
    })
  })

  describe('Changes', () => {
    describe('GET /changes/:changeId', () => {
      it('should return a change', async () => {
        const response = await request(app)
          .get(route(`/changes/${changeId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(changeSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('PATCH /changes/:changeId/approve', () => {
      it('should approve a change', async () => {
        const approvalMessage = 'Approval message'

        const response = await request(app)
          .patch(route(`/changes/${changeId}/approve`))
          .send({
            reviewerMessage: approvalMessage,
          })
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)
        expect(changeSchema.validate(response.body)).toBeTruthy()
        expect(response.body.status).toBe('ACCEPTED')
        expect(response.body.reviewerMessage).toBe(approvalMessage)
      })
    })

    describe('PATCH /changes/:changeId/reject', () => {
      it('should reject a change', async () => {
        const rejectionMessage = 'Rejection message'

        const response = await request(app)
          .patch(route(`/changes/${changeId}/reject`))
          .send({
            reviewerMessage: rejectionMessage,
          })
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(changeSchema.validate(response.body)).toBeTruthy()
        expect(response.body.status).toBe('REJECTED')
        expect(response.body.reviewerMessage).toBe(rejectionMessage)
      })
    })

    describe('DELETE /changes/:changeId', () => {
      it('should delete a change', async () => {
        const response = await request(app)
          .delete(route(`/changes/${changeId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
      })
    })
  })
})
