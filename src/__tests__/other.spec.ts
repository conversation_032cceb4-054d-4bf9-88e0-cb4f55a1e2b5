import { constants } from 'http2'
import request from 'supertest'
import { initApp } from '../shared/infra/http'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../testUtils/databaseSetup'
import { route } from '../utils/tests/tests'

const app = initApp()

describe('Other routes', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('GET /health', () => {
    it('should return no content', async () => {
      const response = await request(app).get(route('/health'))

      expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
    })
  })
})
