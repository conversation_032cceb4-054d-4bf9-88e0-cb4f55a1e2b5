import { constants } from 'http2'
import request from 'supertest'
import { MOCKED_PAYLOAD } from '../__mocks__/jose'
import { stubCollection } from '../modules/collections/domain/__stubs__/collection.stub'
import { stubSingleValueField } from '../modules/collections/domain/__stubs__/field.stub'
import { stubFieldGroup } from '../modules/collections/domain/__stubs__/fieldGroup.stub'
import { stubFields } from '../modules/collections/domain/__stubs__/fields.stub'
import { stubService } from '../modules/collections/domain/__stubs__/service.stub'
import {
  collectionRepository,
  fieldGroupRepository,
  serviceRepository,
} from '../modules/collections/repositories'
import { stubUser } from '../modules/users/domain/__stubs__/user.stub'
import { stubUserAssignment } from '../modules/users/domain/__stubs__/userAssignment.stub'
import { stubUserSettings } from '../modules/users/domain/__stubs__/userSettings.stub'
import { User } from '../modules/users/domain/user'
import { userRepository } from '../modules/users/repositories'
import { usersServices } from '../modules/users/services'
import { stubEmail } from '../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../shared/domain/uniqueEntityID'
import { initApp } from '../shared/infra/http'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../testUtils/databaseSetup'
import { arraySchema, paginatedSchema } from '../utils/tests/schemas'
import { route } from '../utils/tests/tests'
import {
  activitySchema,
  collectionSchema,
  entrySchema,
  fieldGroupSchema,
  fieldSchema,
  serviceSchema,
  statisticSchema,
  userSchema,
} from './schemas'

const app = initApp()

let token: string
let collectionId: string
let userId: string
let fieldId: string
let fieldGroupId: string
let serviceId: string

describe('Collections module routes', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await usersServices.initialize()
    const authService = usersServices.get('authorization')

    const collection = stubCollection()
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    const user = new User(
      {
        email: stubEmail({ value: '<EMAIL>' }),
        name: 'John Doe',
        settings: stubUserSettings(),
        userAssignments: [],
      },
      new UniqueEntityID(MOCKED_PAYLOAD.sub),
    )
    userId = user.id.value

    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
      stubUserAssignment({
        userId: user.id,
        collectionId: collection.id,
        role: 'COLLECTION_MANAGER',
      }),
    )

    await userRepository.save(user)
    token = await authService.generateToken(user)

    const fieldGroup = stubFieldGroup({ fields: stubFields([]) })
    fieldGroupId = fieldGroup.id.value
    const field = stubSingleValueField({ fieldGroupId: fieldGroup.id })
    fieldId = field.id.value
    fieldGroup.fields?.add(field)
    await fieldGroupRepository.save(fieldGroup)

    const service = stubService({
      name: 'Test Service',
      collectionId: collection.id,
    })

    serviceId = service.id.value
    await serviceRepository.save(service)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('Collections', () => {
    describe('GET /collections', () => {
      it('should return a list of collections', async () => {
        const response = await request(app)
          .get(route('/collections'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          arraySchema(collectionSchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('GET /collections/:collectionId', () => {
      it('should return a specific collection', async () => {
        const response = await request(app)
          .get(route(`/collections/${collectionId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(collectionSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('PATCH /collections/:collectionId', () => {
      it('should update a specific collection', async () => {
        const response = await request(app)
          .patch(route(`/collections/${collectionId}`))
          .send({ name: 'Updated collection' })
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(response.body.name).toBe('Updated collection')

        expect(collectionSchema.validate(response.body))
      })
    })

    describe('GET /collections/:collectionId/activity', () => {
      it('should return a list of activities for a specific collection', async () => {
        const response = await request(app)
          .get(route(`/collections/${collectionId}/activity`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          paginatedSchema(activitySchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('GET /collections/:collectionId/entries', () => {
      it('should return a list of entries for a specific collection', async () => {
        const response = await request(app)
          .get(route(`/collections/${collectionId}/entries`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          paginatedSchema(entrySchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('POST /collections/:collectionId/entries', () => {
      it('should create an entry for a specific collection', async () => {
        const response = await request(app)
          .post(route(`/collections/${collectionId}/entries`))
          .send({
            visibility: 'PUBLIC',
            data: [
              {
                fieldId,
                value: 'A value',
              },
            ],
          })
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_CREATED)

        expect(entrySchema.validate(response.body))
      })
    })

    describe('GET /collections/:collectionId/field-groups', () => {
      it('should return a list of field groups for a specific collection', async () => {
        const response = await request(app)
          .get(route(`/collections/${collectionId}/field-groups`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(arraySchema(collectionSchema).validate(response.body))
      })
    })

    describe('POST /collections/:collectionId/field-groups', () => {
      it('should create a field group for a specific collection', async () => {
        const body = {
          name: 'Field Group 1',
          position: 0,
          fieldIds: [fieldId],
        }

        const response = await request(app)
          .post(route(`/collections/${collectionId}/field-groups`))
          .send(body)
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_CREATED)

        expect(fieldGroupSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('GET /collections/:collectionId/services', () => {
      it('should return a list of services for a specific collection', async () => {
        const response = await request(app)
          .get(route(`/collections/${collectionId}/services`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          paginatedSchema(serviceSchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('POST /collections/:collectionId/services', () => {
      it('should create a service for a specific collection', async () => {
        const body = {
          collectionId,
          name: 'Service 1',
          description: 'Service 1 description',
          category: 'Category 1',
          availability: true,
          link: 'https://acme.tld/services/service-1',
        }

        const response = await request(app)
          .post(route(`/collections/${collectionId}/services`))
          .send(body)
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_CREATED)

        expect(serviceSchema.validate(response.body)).toBeTruthy()

        // TODO fix comparison
        // expect(body).toMatchObject(response.body)
      })
    })

    describe('GET /collections/:collectionId/users', () => {
      it('should return a list of users for a specific collection', async () => {
        const response = await request(app)
          .get(route(`/collections/${collectionId}/users`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(paginatedSchema(userSchema).validate(response.body)).toBeTruthy()
      })
    })

    describe('PATCH /collections/:collectionId/users/:userId', () => {
      it('should update a user role for a specific collection', async () => {
        const response = await request(app)
          .patch(route(`/collections/${collectionId}/users/${userId}`))
          .set('Authorization', `Bearer ${token}`)
          .send({ role: 'CONTRIBUTOR' })

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(userSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('DELETE /collections/:collectionId/users/:userId', () => {
      it('should delete a user assignment from a collection', async () => {
        const testUser = stubUser({
          name: 'Test User To Delete',
          email: stubEmail({ value: '<EMAIL>' }),
        })

        testUser.userAssignments.push(
          stubUserAssignment({
            userId: testUser.id,
            collectionId: new UniqueEntityID(collectionId),
            role: 'VIEWER',
          }),
        )

        await userRepository.save(testUser)

        const response = await request(app)
          .delete(
            route(`/collections/${collectionId}/users/${testUser.id.value}`),
          )
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)

        const updatedUser = await userRepository.getById(testUser.id)
        expect(updatedUser.userAssignments.length).toBe(0)
      })
    })
  })

  describe('GET /collections/stats', () => {
    it('should return collection stats', async () => {
      const response = await request(app)
        .get(route('/collections/stats'))
        .set('Authorization', `Bearer ${token}`)

      expect(response.status).toBe(constants.HTTP_STATUS_OK)

      expect(arraySchema(statisticSchema).validate(response.body)).toBeTruthy()
    })
  })

  describe('Services', () => {
    describe('GET /services/:serviceId', () => {
      it('should return a specific service', async () => {
        const response = await request(app)
          .get(route(`/services/${serviceId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(serviceSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('PATCH /services/:serviceId', () => {
      it('should update a specific service', async () => {
        const response = await request(app)
          .patch(route(`/services/${serviceId}`))
          .send({ name: 'Updated service' })
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(serviceSchema.validate(response.body)).toBeTruthy()

        expect(response.body.name).toBe('Updated service')
      })
    })

    describe('DELETE /services/:serviceId', () => {
      it('should delete a service', async () => {
        const response = await request(app)
          .delete(route(`/services/${serviceId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
      })
    })

    describe('Fields', () => {
      describe('GET /fields/:fieldId', () => {
        it('should return a specific field', async () => {
          const response = await request(app)
            .get(route(`/fields/${fieldId}`))
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_OK)

          expect(fieldSchema.validate(response.body)).toBeTruthy()
        })
      })

      describe('PATCH /fields/:fieldId', () => {
        it('should update a specific field', async () => {
          const response = await request(app)
            .patch(route(`/fields/${fieldId}`))
            .send({ name: 'Updated field' })
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_OK)

          expect(fieldSchema.validate(response.body)).toBeTruthy()

          expect(response.body.name).toBe('Updated field')
        })
      })

      describe('DELETE /fields/:fieldId', () => {
        it('should delete a field', async () => {
          const response = await request(app)
            .delete(route(`/fields/${fieldId}`))
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
        })
      })
    })

    describe('Field Groups', () => {
      describe('GET /field-groups/:fieldGroupId', () => {
        it('should return a specific field group', async () => {
          const response = await request(app)
            .get(route(`/field-groups/${fieldGroupId}`))
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_OK)

          expect(fieldGroupSchema.validate(response.body)).toBeTruthy()
        })
      })

      describe('PATCH /field-groups/:fieldGroupId', () => {
        it('should update a specific field group', async () => {
          const response = await request(app)
            .patch(route(`/field-groups/${fieldGroupId}`))
            .send({ name: 'Updated field group' })
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_OK)

          expect(fieldGroupSchema.validate(response.body)).toBeTruthy()

          expect(response.body.name).toBe('Updated field group')
        })
      })

      describe('POST /field-groups/:fieldGroupId/fields', () => {
        it('should add a field to a specific field group', async () => {
          const body = {
            name: 'New field',
            position: 0,
            type: 'TEXT',
            mandatory: true,
            visibility: 'PUBLIC',
            preview: true,
          }

          const response = await request(app)
            .post(route(`/field-groups/${fieldGroupId}/fields`))
            .send(body)
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_CREATED)

          expect(fieldSchema.validate(response.body)).toBeTruthy()
        })
      })

      describe('DELETE /field-groups/:fieldGroupId', () => {
        it('should delete a field group', async () => {
          const response = await request(app)
            .delete(route(`/field-groups/${fieldGroupId}`))
            .set('Authorization', `Bearer ${token}`)

          expect(response.status).toBe(constants.HTTP_STATUS_NO_CONTENT)
        })
      })
    })
  })
})
