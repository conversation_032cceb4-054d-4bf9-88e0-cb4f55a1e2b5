import { constants } from 'http2'
import request from 'supertest'
import { MOCKED_PAYLOAD } from '../__mocks__/jose'
import { stubCollection } from '../modules/collections/domain/__stubs__/collection.stub'
import { FieldGroups } from '../modules/collections/domain/fieldGroups'
import { Services } from '../modules/collections/domain/services'
import { collectionRepository } from '../modules/collections/repositories'
import { stubInvite } from '../modules/users/domain/__stubs__/invite.stub'
import { stubUserAssignment } from '../modules/users/domain/__stubs__/userAssignment.stub'
import { Invite } from '../modules/users/domain/invite'
import { User } from '../modules/users/domain/user'
import { UserSettings } from '../modules/users/domain/userSettings'
import { inviteRepository, userRepository } from '../modules/users/repositories'
import { usersServices } from '../modules/users/services'
import { InviteStatus } from '../modules/users/types/invite'
import { CollectionRole } from '../modules/users/types/user'
import { stubEmail } from '../shared/domain/__stubs__/email.stub'
import { UniqueEntityID } from '../shared/domain/uniqueEntityID'
import { initApp } from '../shared/infra/http'
import {
  closeTestDatabase,
  setupTestDatabase,
} from '../testUtils/databaseSetup'
import { paginatedSchema } from '../utils/tests/schemas'
import { route } from '../utils/tests/tests'
import { userSchema, inviteSchema } from './schemas'

const app = initApp()

let token: string
let userId: string
let invite: Invite
let collectionId: string

describe('Users module routes', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await usersServices.initialize()
    const authService = usersServices.get('authorization')

    const user = new User(
      {
        email: stubEmail({ value: '<EMAIL>' }),
        name: 'John Doe',
        settings: new UserSettings({ emailNotifications: true }),
        userAssignments: [],
      },
      new UniqueEntityID(MOCKED_PAYLOAD.sub),
    )
    userId = user.id.value
    user.userAssignments.push(
      stubUserAssignment({
        userId: user.id,
        role: 'PLATFORM_MANAGER',
      }),
    )
    await userRepository.save(user)
    token = await authService.generateToken(user)

    const collection = stubCollection({
      name: 'User Test Collection',
      services: new Services([]),
      fieldGroups: new FieldGroups([]),
    })
    collectionId = collection.id.value
    await collectionRepository.save(collection)

    invite = stubInvite({
      name: 'Existing User',
      email: stubEmail({ value: '<EMAIL>' }),
      role: 'EDITOR' as CollectionRole,
      collectionId: collection.id,
      status: 'PENDING' as InviteStatus,
    })
    await inviteRepository.save(invite)
  })

  afterAll(async () => {
    await closeTestDatabase()
  })

  describe('Users', () => {
    describe('GET /users', () => {
      it('should return a list of users', async () => {
        const response = await request(app)
          .get(route('/users'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(paginatedSchema(userSchema).validate(response.body)).toBeTruthy()
      })
    })

    describe('GET /users/me', () => {
      it('should return the current user', async () => {
        const response = await request(app)
          .get(route('/users/me'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(userSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('GET /users/:userId', () => {
      it('should return a user', async () => {
        const response = await request(app)
          .get(route(`/users/${userId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(userSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('POST /users/:userId', () => {
      it('should create a user', async () => {
        const response = await request(app)
          .post(route(`/users/${invite.id.value}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(userSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('PATCH /users/:userId', () => {
      it('should update a user', async () => {
        const response = await request(app)
          .patch(route(`/users/${userId}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(userSchema.validate(response.body)).toBeTruthy()
      })
    })
  })

  describe('Invites', () => {
    beforeEach(async () => {
      invite = stubInvite({
        name: 'Existing User',
        email: stubEmail({ value: '<EMAIL>' }),
        role: 'EDITOR' as CollectionRole,
        collectionId: new UniqueEntityID(collectionId),
        status: 'PENDING' as InviteStatus,
      })
      await inviteRepository.save(invite)
    })

    describe('GET /invites', () => {
      it('should return a list of invites', async () => {
        const response = await request(app)
          .get(route('/invites'))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(
          paginatedSchema(inviteSchema).validate(response.body),
        ).toBeTruthy()
      })
    })

    describe('GET /invites/:inviteId', () => {
      it('should return an invite', async () => {
        const response = await request(app)
          .get(route(`/invites/${invite.id.value}`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(inviteSchema.validate(response.body)).toBeTruthy()
      })
    })

    describe('POST /invites', () => {
      it('should create an invite', async () => {
        const body = {
          collectionId,
          role: 'CONTRIBUTOR',
          email: '<EMAIL>',
          name: 'John Doe',
        }

        const response = await request(app)
          .post(route('/invites'))
          .send(body)
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_CREATED)

        expect(inviteSchema.validate(response.body)).toBeTruthy()

        // TODO fix comparison
        // expect(body).toMatchObject(response.body)

        expect(response.body.status).toBe('PENDING')
      })
    })

    describe('PATCH /invites/:inviteId/cancel', () => {
      it('should cancel an invite', async () => {
        const response = await request(app)
          .patch(route(`/invites/${invite.id.value}/cancel`))
          .set('Authorization', `Bearer ${token}`)

        expect(response.status).toBe(constants.HTTP_STATUS_OK)

        expect(inviteSchema.validate(response.body)).toBeTruthy()

        expect(response.body.status).toBe('CANCELLED')
      })
    })
  })
})
