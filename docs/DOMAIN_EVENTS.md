# Domain Events Implementation

This document describes the domain events system implemented in the CIIMAR Biobanks Backend application, following Domain-Driven Design (DDD) principles.

## Overview

The domain events system enables decoupled communication between different parts of the application. When domain entities change state, they emit events that trigger activities and notifications without tight coupling between modules.

## Architecture

### Core Components

1. **Domain Events** - Events emitted by domain entities when their state changes
2. **Event Handlers** - Process domain events and trigger appropriate actions
3. **Domain Events Service** - Central dispatcher for registering and dispatching events
4. **Activity System** - Creates activities and notifications based on domain events

### Event Flow

```
Domain Entity → Domain Event → Event Handler → CreateActivity Use Case → Activity + Notification
```

## Domain Events

### User Domain Events

#### UserRoleChangedEvent
- **Triggered**: When a user's role in a collection is updated
- **Data**: Target user, collection, changed by user, old role, new role
- **Handler**: `UserRoleChangedHandler`
- **Activity Type**: `ROLE_CHANGE`

#### UserRemovedEvent
- **Triggered**: When a user is removed from a collection
- **Data**: Target user, collection, removed by user, removed role
- **Handler**: `UserRemovedHandler`
- **Activity Type**: `USER_REMOVED`

### Entry Domain Events

#### EntryCreatedEvent
- **Triggered**: When a new entry is created
- **Data**: Entry details, collection, creator
- **Handler**: `EntryCreatedHandler`
- **Activity Type**: `ENTRY_CREATE`

#### EntryUpdatedEvent
- **Triggered**: When an entry is updated
- **Data**: Entry details, collection, updater
- **Handler**: `EntryUpdatedHandler`
- **Activity Type**: `ENTRY_UPDATE`

### Change Domain Events

#### ChangeCreatedEvent
- **Triggered**: When a new change request is created
- **Data**: Change details, entry, creator
- **Handler**: `ChangeCreatedHandler`
- **Activity Type**: `CHANGE_CREATE`

#### ChangeApprovedEvent
- **Triggered**: When a change request is approved
- **Data**: Change details, approver, creator
- **Handler**: `ChangeApprovedHandler`
- **Activity Type**: `CHANGE_APPROVE`

#### ChangeRejectedEvent
- **Triggered**: When a change request is rejected
- **Data**: Change details, rejector, creator
- **Handler**: `ChangeRejectedHandler`
- **Activity Type**: `CHANGE_REJECT`

## Event Handlers

All event handlers extend `BaseDomainEventHandler` and follow a consistent pattern:

1. **Parse Input**: Convert domain event to `CreateActivityDTO`
2. **Determine Target Users**: Use `NotificationTargetService` to find who should be notified
3. **Execute Use Case**: Call `CreateActivity` to create activity and notifications

### Target User Resolution

The `NotificationTargetService` determines who should receive notifications based on:

- **Collection Managers**: Always notified for collection-related events
- **Platform Managers**: Notified for all events
- **Specific Users**: For role changes, approvals, rejections
- **Event Creator**: Always excluded from notifications

## Implementation Details

### Domain Entity Integration

Domain entities dispatch events using the `addDomainEvent` method:

```typescript
// In User domain entity
updateAssignment(collectionId: UniqueEntityID, newRole: Role, changedBy: UniqueEntityID): boolean {
  const assignment = this.getAssignmentForCollection(collectionId)
  if (assignment && assignment.role !== newRole) {
    const oldRole = assignment.role
    assignment.role = newRole
    
    // Dispatch domain event
    this.addDomainEvent(
      new UserRoleChangedEvent(this, collectionId, changedBy, oldRole, newRole)
    )
    return true
  }
  return false
}
```

### Event Registration

Events are registered during application bootstrap:

```typescript
// In src/index.ts
domainEvents.bootstrap([...userEventHandlers, ...activityEventHandlers])
```

### Event Dispatching

Events are dispatched after successful repository operations:

```typescript
// In repository implementations
async save(entity: Entity): Promise<void> {
  // Save to database
  await this.persistEntity(entity)
  
  // Dispatch domain events
  await domainEvents.dispatchEventsForEntity(entity.id)
}
```

## Benefits

1. **Decoupling**: Domain logic is separated from notification/activity concerns
2. **Consistency**: All activities and notifications follow the same pattern
3. **Extensibility**: New event types and handlers can be added easily
4. **Testability**: Each component can be tested independently
5. **Reliability**: Events are processed after successful persistence

## Testing

The domain events system includes comprehensive tests:

- **Unit Tests**: Individual event handlers and domain events
- **Integration Tests**: End-to-end event flow
- **Service Tests**: NotificationTargetService functionality

## Configuration

Event handlers are automatically registered during application startup. No additional configuration is required.

## Error Handling

- Events are processed asynchronously after successful persistence
- Handler failures are logged but don't affect the main operation
- Failed events can be retried or handled through monitoring

## Future Enhancements

- **Event Sourcing**: Store events for audit trails
- **External Events**: Publish events to external systems
- **Event Replay**: Replay events for debugging or recovery
- **Batch Processing**: Process multiple events in batches for performance
