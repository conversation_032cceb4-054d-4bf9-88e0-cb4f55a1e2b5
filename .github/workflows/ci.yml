name: CI

on:
  pull_request:
    branches:
      - develop
    types:
      - opened
      - synchronize
      - reopened
      - review_requested
  push:
    branches:
      - develop
      - master

jobs:
  lint-build-test:
    name: Lint, Build & Test
    if: "!contains(github.event.head_commit.message, '[skip ci]') && github.actor != 'dependabot[bot]' && github.event.pull_request.draft == false"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build
        run: pnpm build

      - name: Lint
        run: pnpm lint
        continue-on-error: true # TODO: Temporary until we resolve most TODOS

      - name: Test
        run: pnpm test
