import eslintJs from '@eslint/js'
import * as importPlugin from 'eslint-plugin-import'
import onlyWarnPlugin from 'eslint-plugin-only-warn'
import prettierPlugin from 'eslint-plugin-prettier'
import unusedImportsPlugin from 'eslint-plugin-unused-imports'
import tseslint from 'typescript-eslint'

export default [
  eslintJs.configs.recommended,
  ...tseslint.configs.recommended,

  {
    files: ['**/*.ts'],

    // TypeScript language options
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
    },

    // Plugin configuration
    plugins: {
      '@typescript-eslint': tseslint.plugin,
      prettier: prettierPlugin,
      import: importPlugin,
      'unused-imports': unusedImportsPlugin,
      'only-warn': onlyWarnPlugin,
    },

    rules: {
      'prefer-const': 'error',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': 'warn',
      'unused-imports/no-unused-imports': 'error',
      'import/order': [
        'warn',
        {
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
        },
      ],

      'prettier/prettier': ['error', { endOfLine: 'auto' }],
      'import/first': 'warn',
      'import/newline-after-import': 'warn',
      'import/no-duplicates': 'warn',

      'no-warning-comments': 'warn',
    },
  },
  {
    ignores: [
      'dist/**',
      'node_modules/**',
      '**/*.js',
      '.gitignore',
      'spec/**',
      'eslint.config.ts',
    ],
  },
]
