{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides automated code quality analysis including code smells, design patterns, and best practices suggestions", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.ts", "src/**/*.js"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells such as:\n   - Long methods or classes\n   - Duplicate code\n   - Large parameter lists\n   - Complex conditional logic\n   - Dead code or unused imports\n\n2. **Design Patterns**: Suggest appropriate design patterns where beneficial:\n   - Repository pattern usage\n   - Factory patterns for object creation\n   - Strategy pattern for varying algorithms\n   - Observer pattern for event handling\n\n3. **Best Practices**: Check adherence to:\n   - TypeScript best practices\n   - Domain-Driven Design principles\n   - Clean architecture patterns\n   - SOLID principles\n   - Error handling patterns\n\n4. **Performance Optimizations**: Look for:\n   - Inefficient database queries\n   - Memory leaks potential\n   - Unnecessary object creation\n   - Blocking operations\n\n5. **Maintainability**: Assess:\n   - Code readability and clarity\n   - Proper separation of concerns\n   - Consistent naming conventions\n   - Adequate documentation\n\nProvide specific, actionable suggestions while maintaining the existing functionality. Focus on improvements that enhance code quality without breaking changes."}}