# Technology Stack

## Core Technologies

- **Runtime**: Node.js with TypeScript (ES2022)
- **Framework**: Express.js 5.x
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with JOS<PERSON> library
- **Validation**: Yup schemas
- **Logging**: Pino logger
- **Testing**: Jest with SWC transformer

## Package Manager

- **pnpm** (version 10.13.1) - Use pnpm for all package management

## Key Libraries

- **Drizzle ORM**: Database queries and migrations
- **Express**: HTTP server and routing
- **Yup**: Schema validation
- **JOSE**: JWT token handling
- **Pino**: Structured logging
- **Helmet**: Security middleware
- **CORS**: Cross-origin resource sharing

## API Specification

- **TypeSpec**: API specification language (.tsp files)
- **OpenAPI**: Generated from TypeSpec
- **Redocly**: API documentation preview

## Development Tools

- **ESLint**: Code linting with TypeScript rules
- **Prettier**: Code formatting
- **Husky**: Git hooks
- **Nodemon**: Development server with hot reload
- **Docker**: Containerization

## Common Commands

### Development

```bash
pnpm dev                    # Start development server with hot reload
pnpm compile               # Type check without emitting files
pnpm build                 # Build for production
pnpm test                  # Run tests with coverage
pnpm lint                  # Lint and fix code issues
pnpm format                # Format code with Prettier
```

### Database

```bash
pnpm db:push               # Push schema changes (development)
pnpm db:migrate:new        # Generate new migration
pnpm db:migrate:run        # Run pending migrations
pnpm db:studio             # Launch database explorer
pnpm db:seed               # Seed database with default data
pnpm db:seed:reset         # Reset and seed database
```

### API Specification

```bash
pnpm spec:compile          # Compile TypeSpec to OpenAPI
pnpm spec:dev              # Watch TypeSpec and preview docs
```

### Docker

```bash
pnpm build:image           # Build Docker image
```

## Environment Configuration

- Use `.env` files for environment variables
- Required variables defined in `.env.example`
- Test environment uses `.env.test`
- Configuration schema validation with Yup
