# Product Overview

CIIMAR Biobank Management System is a backend API for managing biobank collections, entries, users, and activities. The system provides:

- **Collection Management**: Create and manage biobank collections with custom fields and field groups
- **Entry Management**: Store and track biobank entries with custom field data and change history
- **User Management**: Handle user authentication, authorization, and role-based access control
- **Activity Tracking**: Log and notify users of system activities and changes
- **Invitation System**: Invite users to collections with specific roles

## Key Domains

- **Collections**: Biobank collections with configurable fields and services
- **Entries**: Individual biobank specimens with custom field data
- **Users**: System users with role-based permissions (Platform Manager, Collection Manager, etc.)
- **Activities**: System events and notifications for user awareness

## Architecture

The system follows Domain-Driven Design (DDD) principles with clean architecture patterns, separating domain logic from infrastructure concerns.
