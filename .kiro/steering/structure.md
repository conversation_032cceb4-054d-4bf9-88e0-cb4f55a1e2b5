# Project Structure

## Root Directory

```
├── src/                    # Source code
├── spec/                   # TypeSpec API specifications
├── scripts/                # Deployment and utility scripts
├── coverage/               # Test coverage reports
├── dist/                   # Compiled TypeScript output
└── node_modules/           # Dependencies
```

## Source Code Organization (`src/`)

### Modular Architecture

```
src/
├── modules/                # Domain modules (DDD bounded contexts)
│   ├── activities/         # Activity tracking and notifications
│   ├── collections/        # Biobank collections management
│   ├── entries/           # Biobank entries and field data
│   └── users/             # User management and authentication
├── shared/                # Shared infrastructure and utilities
├── scripts/               # Application scripts
└── index.ts              # Application entry point
```

### Module Structure Pattern

Each module follows a consistent structure:

```
modules/{domain}/
├── domain/                # Domain entities and business logic
│   ├── {entity}.ts       # Domain entities
│   ├── events/           # Domain events
│   ├── __tests__/        # Domain tests
│   └── __stubs__/        # Test stubs
├── dto/                  # Data Transfer Objects
├── mappers/              # Entity ↔ DTO mapping
├── models/               # Database models (Drizzle schema)
├── repositories/         # Data access layer
│   └── interfaces/       # Repository contracts
├── useCases/             # Application use cases
│   └── {useCase}/        # Individual use case folders
│       ├── {useCase}.ts  # Use case implementation
│       ├── {useCase}.dto.ts # Use case DTO
│       ├── controller.ts # HTTP controller
│       └── index.ts      # Exports
├── services/             # Domain services
├── handlers/             # Event handlers
├── infra/                # Infrastructure (HTTP routes, etc.)
└── errors/               # Domain-specific errors
```

### Shared Infrastructure (`src/shared/`)

```
shared/
├── database/             # Database configuration and schema
│   ├── schema/          # Drizzle table definitions
│   ├── drizzle/         # Migration files
│   └── seed/            # Database seeding
├── domain/              # Base domain classes
│   ├── entity.ts        # Base Entity class
│   ├── events/          # Domain event system
│   └── valueObject.ts   # Base ValueObject class
├── dto/                 # Common DTOs
├── errors/              # Shared error classes
├── infra/               # Infrastructure concerns
│   ├── auth/           # Authentication middleware
│   ├── configuration/   # App configuration
│   ├── http/           # HTTP server setup
│   └── logger/         # Logging configuration
├── repositories/        # Base repository classes
├── services/           # Shared services
└── types/              # Common type definitions
```

## API Specification (`spec/`)

```
spec/
├── common/              # Shared models and types
│   └── models/         # Common TypeSpec models
├── modules/            # Module-specific specifications
│   └── {entity}/       # Per-entity specifications
│       ├── main.tsp    # Route definitions
│       └── models.tsp  # Entity models
├── main.tsp            # Main specification file
└── dist/               # Generated OpenAPI files
```

## Naming Conventions

- **Files**: camelCase for TypeScript files
- **Folders**: camelCase for directories
- **Classes**: PascalCase
- **Interfaces**: PascalCase with 'I' prefix for generic interfaces
- **Types**: PascalCase with 'T' prefix
- **Constants**: UPPER_SNAKE_CASE
- **Database tables**: snake_case with plural names

## Import Patterns

- Use relative imports within modules
- Use absolute imports from `src/` for cross-module dependencies
- Group imports: external libraries, shared, then local
- Export from index files for clean module boundaries
